// Advanced News Segmentation Types

export interface NewsSegment {
  id: string;
  name: string;
  description: string;
  keywords: SegmentKeyword[];
  entities: SegmentEntity[];
  geographicMarkers: GeographicMarker[];
  sources: PreferredSource[];
  exclusionRules: ExclusionRule[];
  relevanceWeights: RelevanceWeights;
  minimumRelevanceScore: number;
}

export interface SegmentKeyword {
  term: string;
  weight: number;
  context?: string[];
  synonyms?: string[];
  category: 'primary' | 'secondary' | 'contextual';
  language?: 'en' | 'hi' | 'multi';
}

export interface SegmentEntity {
  name: string;
  type: 'company' | 'person' | 'organization' | 'currency' | 'index' | 'commodity';
  aliases: string[];
  weight: number;
  market: 'indian' | 'global' | 'crypto' | 'multi';
}

export interface GeographicMarker {
  location: string;
  type: 'country' | 'city' | 'region' | 'exchange';
  weight: number;
  aliases: string[];
}

export interface PreferredSource {
  name: string;
  credibilityScore: number;
  specialization: string[];
  bias?: 'bullish' | 'bearish' | 'neutral';
}

export interface ExclusionRule {
  type: 'keyword' | 'source' | 'content_pattern';
  pattern: string;
  reason: string;
}

export interface RelevanceWeights {
  titleWeight: number;
  descriptionWeight: number;
  contentWeight: number;
  sourceWeight: number;
  timeWeight: number;
  entityWeight: number;
  geographicWeight: number;
}

export interface SegmentationResult {
  articleId: string;
  segments: SegmentMatch[];
  primarySegment: string;
  confidence: number;
  relevanceScore: number;
  reasons: string[];
}

export interface SegmentMatch {
  segmentId: string;
  confidence: number;
  relevanceScore: number;
  matchedKeywords: string[];
  matchedEntities: string[];
  matchedGeographic: string[];
  reasons: string[];
}

export interface ContentAnalysis {
  topics: Topic[];
  entities: ExtractedEntity[];
  sentiment: number;
  complexity: number;
  marketRelevance: number;
  geographicFocus: string[];
  timeRelevance: number;
}

export interface Topic {
  name: string;
  confidence: number;
  keywords: string[];
}

export interface ExtractedEntity {
  text: string;
  type: string;
  confidence: number;
  context: string;
}

// Advanced Segmentation Configuration
export const ADVANCED_NEWS_SEGMENTS: Record<string, NewsSegment> = {
  'indian-markets': {
    id: 'indian-markets',
    name: '🇮🇳 Indian Markets',
    description: 'Indian stock markets, banking, and financial sector news',
    keywords: [
      // Primary Indian market terms
      { term: 'sensex', weight: 1.0, category: 'primary', synonyms: ['bse sensex', 'bombay stock exchange'] },
      { term: 'nifty', weight: 1.0, category: 'primary', synonyms: ['nifty 50', 'nse nifty'] },
      { term: 'rbi', weight: 0.9, category: 'primary', synonyms: ['reserve bank of india', 'central bank'] },
      { term: 'sebi', weight: 0.8, category: 'primary', synonyms: ['securities and exchange board'] },
      { term: 'bse', weight: 0.8, category: 'primary', synonyms: ['bombay stock exchange'] },
      { term: 'nse', weight: 0.8, category: 'primary', synonyms: ['national stock exchange'] },
      
      // Indian currency and monetary policy
      { term: 'rupee', weight: 0.9, category: 'primary', synonyms: ['inr', 'indian rupee'] },
      { term: 'repo rate', weight: 0.8, category: 'primary', context: ['monetary policy', 'interest rate'] },
      { term: 'crr', weight: 0.7, category: 'secondary', synonyms: ['cash reserve ratio'] },
      { term: 'slr', weight: 0.7, category: 'secondary', synonyms: ['statutory liquidity ratio'] },
      
      // Indian economic indicators
      { term: 'gst', weight: 0.8, category: 'primary', synonyms: ['goods and services tax'] },
      { term: 'gdp india', weight: 0.9, category: 'primary', context: ['economic growth', 'quarterly'] },
      { term: 'inflation india', weight: 0.8, category: 'primary', context: ['cpi', 'wpi'] },
      { term: 'fii', weight: 0.7, category: 'secondary', synonyms: ['foreign institutional investor'] },
      { term: 'dii', weight: 0.7, category: 'secondary', synonyms: ['domestic institutional investor'] },
      
      // Indian sectors
      { term: 'indian banking', weight: 0.8, category: 'primary', context: ['sector', 'stocks'] },
      { term: 'indian it', weight: 0.7, category: 'secondary', context: ['technology', 'services'] },
      { term: 'indian pharma', weight: 0.7, category: 'secondary', context: ['pharmaceutical', 'healthcare'] },
      { term: 'indian auto', weight: 0.7, category: 'secondary', context: ['automobile', 'automotive'] },
    ],
    entities: [
      // Major Indian companies
      { name: 'Reliance Industries', aliases: ['RIL', 'Reliance'], type: 'company', weight: 0.9, market: 'indian' },
      { name: 'TCS', aliases: ['Tata Consultancy Services'], type: 'company', weight: 0.8, market: 'indian' },
      { name: 'HDFC Bank', aliases: ['HDFC'], type: 'company', weight: 0.8, market: 'indian' },
      { name: 'Infosys', aliases: ['INFY'], type: 'company', weight: 0.8, market: 'indian' },
      { name: 'ICICI Bank', aliases: ['ICICI'], type: 'company', weight: 0.8, market: 'indian' },
      { name: 'SBI', aliases: ['State Bank of India'], type: 'company', weight: 0.8, market: 'indian' },
      { name: 'Wipro', aliases: ['WIPRO'], type: 'company', weight: 0.7, market: 'indian' },
      { name: 'Bajaj Finance', aliases: ['Bajaj Finserv'], type: 'company', weight: 0.7, market: 'indian' },
      { name: 'Adani Group', aliases: ['Adani'], type: 'company', weight: 0.8, market: 'indian' },
      { name: 'Tata Motors', aliases: ['Tata'], type: 'company', weight: 0.7, market: 'indian' },
      
      // Indian indices and currencies
      { name: 'Sensex', aliases: ['BSE Sensex'], type: 'index', weight: 1.0, market: 'indian' },
      { name: 'Nifty', aliases: ['Nifty 50'], type: 'index', weight: 1.0, market: 'indian' },
      { name: 'INR', aliases: ['Indian Rupee'], type: 'currency', weight: 0.9, market: 'indian' },
    ],
    geographicMarkers: [
      { location: 'India', type: 'country', weight: 1.0, aliases: ['Bharat'] },
      { location: 'Mumbai', type: 'city', weight: 0.8, aliases: ['Bombay'] },
      { location: 'Delhi', type: 'city', weight: 0.7, aliases: ['New Delhi'] },
      { location: 'Bangalore', type: 'city', weight: 0.6, aliases: ['Bengaluru'] },
      { location: 'BSE', type: 'exchange', weight: 0.9, aliases: ['Bombay Stock Exchange'] },
      { location: 'NSE', type: 'exchange', weight: 0.9, aliases: ['National Stock Exchange'] },
    ],
    sources: [
      { name: 'Economic Times', credibilityScore: 0.9, specialization: ['indian markets', 'economy'] },
      { name: 'Business Standard', credibilityScore: 0.8, specialization: ['indian business'] },
      { name: 'Livemint', credibilityScore: 0.8, specialization: ['indian markets'] },
      { name: 'Moneycontrol', credibilityScore: 0.8, specialization: ['indian stocks'] },
    ],
    exclusionRules: [
      { type: 'keyword', pattern: 'global markets only', reason: 'Explicitly global focus' },
      { type: 'content_pattern', pattern: 'us markets.*without.*india', reason: 'US-only content' },
    ],
    relevanceWeights: {
      titleWeight: 0.3,
      descriptionWeight: 0.25,
      contentWeight: 0.2,
      sourceWeight: 0.1,
      timeWeight: 0.05,
      entityWeight: 0.05,
      geographicWeight: 0.05
    },
    minimumRelevanceScore: 0.2
  },
  
  'global-markets': {
    id: 'global-markets',
    name: '🌍 Global Markets',
    description: 'International markets, US/European markets, and global economic news',
    keywords: [
      // US Markets
      { term: 'dow jones', weight: 0.9, category: 'primary', synonyms: ['dow', 'djia'] },
      { term: 's&p 500', weight: 0.9, category: 'primary', synonyms: ['s&p', 'spx'] },
      { term: 'nasdaq', weight: 0.9, category: 'primary', synonyms: ['nasdaq composite'] },
      { term: 'federal reserve', weight: 0.9, category: 'primary', synonyms: ['fed', 'fomc'] },
      { term: 'wall street', weight: 0.8, category: 'primary', context: ['markets', 'trading'] },
      
      // European Markets
      { term: 'ftse', weight: 0.8, category: 'primary', synonyms: ['ftse 100'] },
      { term: 'dax', weight: 0.8, category: 'primary', synonyms: ['german dax'] },
      { term: 'cac 40', weight: 0.8, category: 'primary', synonyms: ['cac'] },
      { term: 'ecb', weight: 0.8, category: 'primary', synonyms: ['european central bank'] },
      
      // Global currencies
      { term: 'dollar', weight: 0.8, category: 'primary', synonyms: ['usd', 'us dollar'] },
      { term: 'euro', weight: 0.8, category: 'primary', synonyms: ['eur'] },
      { term: 'pound', weight: 0.7, category: 'secondary', synonyms: ['gbp', 'sterling'] },
      { term: 'yen', weight: 0.7, category: 'secondary', synonyms: ['jpy', 'japanese yen'] },
      
      // Global economic indicators
      { term: 'global gdp', weight: 0.8, category: 'primary', context: ['world economy'] },
      { term: 'international trade', weight: 0.7, category: 'secondary', context: ['exports', 'imports'] },
      { term: 'geopolitical', weight: 0.7, category: 'secondary', context: ['tensions', 'conflict'] },
    ],
    entities: [
      // Major global companies
      { name: 'Apple', aliases: ['AAPL'], type: 'company', weight: 0.8, market: 'global' },
      { name: 'Microsoft', aliases: ['MSFT'], type: 'company', weight: 0.8, market: 'global' },
      { name: 'Amazon', aliases: ['AMZN'], type: 'company', weight: 0.8, market: 'global' },
      { name: 'Google', aliases: ['Alphabet', 'GOOGL'], type: 'company', weight: 0.8, market: 'global' },
      { name: 'Tesla', aliases: ['TSLA'], type: 'company', weight: 0.7, market: 'global' },
      
      // Global indices
      { name: 'S&P 500', aliases: ['SPX'], type: 'index', weight: 0.9, market: 'global' },
      { name: 'Dow Jones', aliases: ['DJIA'], type: 'index', weight: 0.9, market: 'global' },
      { name: 'NASDAQ', aliases: ['IXIC'], type: 'index', weight: 0.9, market: 'global' },
    ],
    geographicMarkers: [
      { location: 'United States', type: 'country', weight: 0.9, aliases: ['USA', 'US', 'America'] },
      { location: 'Europe', type: 'region', weight: 0.8, aliases: ['European Union', 'EU'] },
      { location: 'China', type: 'country', weight: 0.8, aliases: ['PRC'] },
      { location: 'Japan', type: 'country', weight: 0.7, aliases: [] },
      { location: 'New York', type: 'city', weight: 0.8, aliases: ['NYSE'] },
      { location: 'London', type: 'city', weight: 0.7, aliases: ['LSE'] },
    ],
    sources: [
      { name: 'Reuters', credibilityScore: 0.9, specialization: ['global markets', 'international'] },
      { name: 'Bloomberg', credibilityScore: 0.9, specialization: ['global finance'] },
      { name: 'Wall Street Journal', credibilityScore: 0.9, specialization: ['us markets'] },
      { name: 'Financial Times', credibilityScore: 0.9, specialization: ['global finance'] },
    ],
    exclusionRules: [
      { type: 'keyword', pattern: 'india only', reason: 'India-specific content' },
      { type: 'keyword', pattern: 'crypto only', reason: 'Crypto-specific content' },
    ],
    relevanceWeights: {
      titleWeight: 0.3,
      descriptionWeight: 0.25,
      contentWeight: 0.2,
      sourceWeight: 0.1,
      timeWeight: 0.05,
      entityWeight: 0.05,
      geographicWeight: 0.05
    },
    minimumRelevanceScore: 0.2
  },

  'crypto-web3': {
    id: 'crypto-web3',
    name: '💰 Crypto & Web3',
    description: 'Cryptocurrency, blockchain, DeFi, and Web3 technology news',
    keywords: [
      // Major cryptocurrencies
      { term: 'bitcoin', weight: 1.0, category: 'primary', synonyms: ['btc'] },
      { term: 'ethereum', weight: 0.9, category: 'primary', synonyms: ['eth', 'ether'] },
      { term: 'cryptocurrency', weight: 0.9, category: 'primary', synonyms: ['crypto', 'digital currency'] },
      { term: 'blockchain', weight: 0.8, category: 'primary', synonyms: ['distributed ledger'] },
      
      // DeFi and Web3
      { term: 'defi', weight: 0.8, category: 'primary', synonyms: ['decentralized finance'] },
      { term: 'web3', weight: 0.7, category: 'secondary', synonyms: ['web 3.0'] },
      { term: 'nft', weight: 0.7, category: 'secondary', synonyms: ['non-fungible token'] },
      { term: 'smart contract', weight: 0.7, category: 'secondary', context: ['ethereum', 'blockchain'] },
      
      // Trading and exchanges
      { term: 'crypto exchange', weight: 0.8, category: 'primary', context: ['trading', 'platform'] },
      { term: 'altcoin', weight: 0.6, category: 'secondary', context: ['alternative cryptocurrency'] },
      { term: 'stablecoin', weight: 0.7, category: 'secondary', context: ['usdt', 'usdc'] },
      
      // Regulation and adoption
      { term: 'crypto regulation', weight: 0.8, category: 'primary', context: ['government', 'policy'] },
      { term: 'institutional adoption', weight: 0.7, category: 'secondary', context: ['corporate', 'investment'] },
    ],
    entities: [
      // Major cryptocurrencies
      { name: 'Bitcoin', aliases: ['BTC'], type: 'currency', weight: 1.0, market: 'crypto' },
      { name: 'Ethereum', aliases: ['ETH'], type: 'currency', weight: 0.9, market: 'crypto' },
      { name: 'Binance Coin', aliases: ['BNB'], type: 'currency', weight: 0.7, market: 'crypto' },
      { name: 'Cardano', aliases: ['ADA'], type: 'currency', weight: 0.6, market: 'crypto' },
      { name: 'Solana', aliases: ['SOL'], type: 'currency', weight: 0.6, market: 'crypto' },
      
      // Crypto companies and exchanges
      { name: 'Coinbase', aliases: ['COIN'], type: 'company', weight: 0.8, market: 'crypto' },
      { name: 'Binance', aliases: [], type: 'company', weight: 0.8, market: 'crypto' },
      { name: 'FTX', aliases: [], type: 'company', weight: 0.7, market: 'crypto' },
    ],
    geographicMarkers: [
      { location: 'Global', type: 'region', weight: 0.5, aliases: ['worldwide'] },
      { location: 'United States', type: 'country', weight: 0.7, aliases: ['USA'] },
      { location: 'Singapore', type: 'country', weight: 0.6, aliases: [] },
    ],
    sources: [
      { name: 'CoinDesk', credibilityScore: 0.9, specialization: ['cryptocurrency'] },
      { name: 'Cointelegraph', credibilityScore: 0.8, specialization: ['blockchain', 'crypto'] },
      { name: 'The Block', credibilityScore: 0.8, specialization: ['crypto news'] },
    ],
    exclusionRules: [
      { type: 'keyword', pattern: 'traditional banking only', reason: 'Non-crypto financial content' },
    ],
    relevanceWeights: {
      titleWeight: 0.35,
      descriptionWeight: 0.3,
      contentWeight: 0.2,
      sourceWeight: 0.1,
      timeWeight: 0.03,
      entityWeight: 0.02,
      geographicWeight: 0.0
    },
    minimumRelevanceScore: 0.2
  }
};
