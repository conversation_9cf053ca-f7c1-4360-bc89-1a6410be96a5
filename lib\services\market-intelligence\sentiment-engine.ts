// Advanced Market Sentiment Analysis Engine

import { NewsArticle } from '../news/types';
import { 
  MarketSentiment, 
  NewsImpactScore, 
  FINANCIAL_SENTIMENT_CONFIG,
  NEWS_IMPACT_CATEGORIES 
} from './types';

export class AdvancedSentimentEngine {
  private sentimentCache = new Map<string, MarketSentiment>();
  private impactCache = new Map<string, NewsImpactScore>();

  /**
   * Analyze market sentiment from news articles with advanced NLP
   */
  async analyzeMarketSentiment(articles: NewsArticle[]): Promise<MarketSentiment[]> {
    try {
      const [indianSentiment, foreignSentiment, cryptoSentiment] = await Promise.all([
        this.analyzeMarketSpecificSentiment(articles, 'indian'),
        this.analyzeMarketSpecificSentiment(articles, 'foreign'),
        this.analyzeMarketSpecificSentiment(articles, 'crypto')
      ]);

      return [indianSentiment, foreignSentiment, cryptoSentiment];
    } catch (error) {
      console.error('Error in sentiment analysis:', error);
      return this.getFallbackSentiments();
    }
  }

  /**
   * Analyze sentiment for specific market with contextual understanding
   */
  private async analyzeMarketSpecificSentiment(
    articles: NewsArticle[], 
    market: 'indian' | 'foreign' | 'crypto'
  ): Promise<MarketSentiment> {
    const relevantArticles = this.filterRelevantArticles(articles, market);
    
    if (relevantArticles.length === 0) {
      return this.getDefaultSentiment(market);
    }

    // Multi-layer sentiment analysis
    const sentimentScores = await Promise.all(
      relevantArticles.map(article => this.analyzeSingleArticleSentiment(article, market))
    );

    // Weighted aggregation based on article impact and recency
    const aggregatedSentiment = this.aggregateSentimentScores(sentimentScores, relevantArticles);
    
    // Technical sentiment overlay
    const technicalSentiment = await this.getTechnicalSentiment(market);
    
    // Combine news sentiment with technical sentiment
    const finalSentiment = this.combineSentiments(aggregatedSentiment, technicalSentiment);

    return {
      market,
      sentiment: this.classifySentiment(finalSentiment.score),
      score: finalSentiment.score,
      confidence: finalSentiment.confidence,
      trend: this.determineTrend(sentimentScores),
      volatility: this.calculateVolatility(sentimentScores),
      lastUpdated: new Date()
    };
  }

  /**
   * Advanced single article sentiment analysis
   */
  private async analyzeSingleArticleSentiment(
    article: NewsArticle, 
    market: 'indian' | 'foreign' | 'crypto'
  ): Promise<{ score: number; confidence: number; impact: number }> {
    const content = `${article.title} ${article.description}`.toLowerCase();
    
    // 1. Lexicon-based sentiment scoring
    const lexiconScore = this.calculateLexiconSentiment(content);
    
    // 2. Context-aware sentiment adjustment
    const contextScore = this.adjustForContext(content, market);
    
    // 3. Impact category detection
    const impactMultiplier = this.detectImpactCategory(content);
    
    // 4. Temporal relevance
    const timeDecay = this.calculateTimeDecay(article.publishedAt);
    
    // 5. Source credibility factor
    const credibilityFactor = this.getSourceCredibility(article.source.name);

    // Combine all factors
    const rawScore = (lexiconScore + contextScore) * impactMultiplier * timeDecay * credibilityFactor;
    const normalizedScore = Math.max(-1, Math.min(1, rawScore));
    
    // Calculate confidence based on multiple factors
    const confidence = this.calculateConfidence(content, impactMultiplier, credibilityFactor);

    return {
      score: normalizedScore,
      confidence,
      impact: impactMultiplier
    };
  }

  /**
   * Lexicon-based sentiment scoring with financial context
   */
  private calculateLexiconSentiment(content: string): number {
    const config = FINANCIAL_SENTIMENT_CONFIG;
    let score = 0;
    let wordCount = 0;

    // Positive sentiment words
    config.positiveKeywords.forEach(word => {
      const matches = (content.match(new RegExp(word, 'gi')) || []).length;
      score += matches * 0.1;
      wordCount += matches;
    });

    // Negative sentiment words
    config.negativeKeywords.forEach(word => {
      const matches = (content.match(new RegExp(word, 'gi')) || []).length;
      score -= matches * 0.1;
      wordCount += matches;
    });

    // Neutral words (reduce extreme scores)
    config.neutralKeywords.forEach(word => {
      const matches = (content.match(new RegExp(word, 'gi')) || []).length;
      if (matches > 0) {
        score *= 0.8; // Dampen sentiment if neutral words present
      }
    });

    // Normalize by word count to prevent bias toward longer articles
    return wordCount > 0 ? score / Math.sqrt(wordCount) : 0;
  }

  /**
   * Context-aware sentiment adjustment
   */
  private adjustForContext(content: string, market: 'indian' | 'foreign' | 'crypto'): number {
    const marketTerms = FINANCIAL_SENTIMENT_CONFIG.marketSpecificTerms[market];
    let contextScore = 0;
    let relevanceBonus = 0;

    // Check for market-specific terms
    marketTerms.forEach(term => {
      if (content.includes(term.toLowerCase())) {
        relevanceBonus += 0.1;
      }
    });

    // Sector-specific sentiment patterns
    const sectorPatterns = this.getSectorSentimentPatterns(market);
    sectorPatterns.forEach(pattern => {
      if (content.includes(pattern.keyword)) {
        contextScore += pattern.sentiment * pattern.weight;
      }
    });

    return (contextScore + relevanceBonus) * 0.5; // Weight context adjustment
  }

  /**
   * Detect impact category and return multiplier
   */
  private detectImpactCategory(content: string): number {
    let maxImpact = 1.0;

    Object.entries(NEWS_IMPACT_CATEGORIES).forEach(([category, config]) => {
      const hasKeywords = config.keywords.some(keyword => 
        content.includes(keyword.toLowerCase())
      );
      
      if (hasKeywords) {
        maxImpact = Math.max(maxImpact, config.baseImpact);
      }
    });

    return maxImpact;
  }

  /**
   * Calculate time decay factor
   */
  private calculateTimeDecay(publishedAt: string): number {
    const hoursAgo = (Date.now() - new Date(publishedAt).getTime()) / (1000 * 60 * 60);
    
    // Exponential decay: newer news has higher impact
    if (hoursAgo < 1) return 1.0;
    if (hoursAgo < 6) return 0.9;
    if (hoursAgo < 24) return 0.7;
    if (hoursAgo < 72) return 0.5;
    return 0.3;
  }

  /**
   * Get source credibility factor
   */
  private getSourceCredibility(sourceName: string): number {
    const credibilityMap: Record<string, number> = {
      'reuters': 1.0,
      'bloomberg': 1.0,
      'economic times': 0.95,
      'financial times': 0.95,
      'wall street journal': 0.95,
      'business standard': 0.9,
      'livemint': 0.9,
      'moneycontrol': 0.85,
      'coindesk': 0.9,
      'cointelegraph': 0.8,
      'the block': 0.85
    };

    const source = sourceName.toLowerCase();
    for (const [key, value] of Object.entries(credibilityMap)) {
      if (source.includes(key)) {
        return value;
      }
    }
    
    return 0.7; // Default credibility for unknown sources
  }

  /**
   * Calculate confidence score
   */
  private calculateConfidence(content: string, impactMultiplier: number, credibilityFactor: number): number {
    let confidence = 0.5; // Base confidence

    // Length factor (longer articles generally more reliable)
    const wordCount = content.split(' ').length;
    confidence += Math.min(0.2, wordCount / 500);

    // Impact factor
    confidence += (impactMultiplier - 1) * 0.2;

    // Source credibility
    confidence += (credibilityFactor - 0.7) * 0.3;

    // Sentiment word density
    const sentimentWords = this.countSentimentWords(content);
    confidence += Math.min(0.2, sentimentWords / 20);

    return Math.min(1.0, confidence);
  }

  /**
   * Count sentiment-bearing words
   */
  private countSentimentWords(content: string): number {
    const config = FINANCIAL_SENTIMENT_CONFIG;
    const allSentimentWords = [
      ...config.positiveKeywords,
      ...config.negativeKeywords,
      ...config.neutralKeywords
    ];

    return allSentimentWords.filter(word => 
      content.includes(word.toLowerCase())
    ).length;
  }

  /**
   * Aggregate sentiment scores with weighting
   */
  private aggregateSentimentScores(
    scores: { score: number; confidence: number; impact: number }[],
    articles: NewsArticle[]
  ): { score: number; confidence: number } {
    if (scores.length === 0) {
      return { score: 0, confidence: 0 };
    }

    let weightedScore = 0;
    let totalWeight = 0;
    let avgConfidence = 0;

    scores.forEach((scoreData, index) => {
      const weight = scoreData.confidence * scoreData.impact;
      weightedScore += scoreData.score * weight;
      totalWeight += weight;
      avgConfidence += scoreData.confidence;
    });

    return {
      score: totalWeight > 0 ? weightedScore / totalWeight : 0,
      confidence: avgConfidence / scores.length
    };
  }

  /**
   * Get technical sentiment overlay
   */
  private async getTechnicalSentiment(market: 'indian' | 'foreign' | 'crypto'): Promise<{ score: number; confidence: number }> {
    // Simulate technical analysis sentiment
    // In production, integrate with technical analysis APIs
    
    const technicalScores = {
      indian: this.simulateTechnicalSentiment(0.1),
      foreign: this.simulateTechnicalSentiment(0.05),
      crypto: this.simulateTechnicalSentiment(0.2)
    };

    return technicalScores[market];
  }

  /**
   * Simulate technical sentiment (replace with real technical analysis)
   */
  private simulateTechnicalSentiment(volatility: number): { score: number; confidence: number } {
    const score = (Math.random() - 0.5) * 2 * volatility;
    const confidence = 0.6 + Math.random() * 0.3;
    
    return { score, confidence };
  }

  /**
   * Combine news sentiment with technical sentiment
   */
  private combineSentiments(
    newsSentiment: { score: number; confidence: number },
    technicalSentiment: { score: number; confidence: number }
  ): { score: number; confidence: number } {
    // Weight news sentiment higher than technical sentiment
    const newsWeight = 0.7;
    const technicalWeight = 0.3;

    const combinedScore = (newsSentiment.score * newsWeight) + (technicalSentiment.score * technicalWeight);
    const combinedConfidence = (newsSentiment.confidence * newsWeight) + (technicalSentiment.confidence * technicalWeight);

    return {
      score: combinedScore,
      confidence: combinedConfidence
    };
  }

  /**
   * Helper methods
   */
  private filterRelevantArticles(articles: NewsArticle[], market: 'indian' | 'foreign' | 'crypto'): NewsArticle[] {
    const marketTerms = FINANCIAL_SENTIMENT_CONFIG.marketSpecificTerms[market];
    
    return articles.filter(article => {
      const content = `${article.title} ${article.description}`.toLowerCase();
      return marketTerms.some(term => content.includes(term.toLowerCase()));
    });
  }

  private classifySentiment(score: number): 'bullish' | 'bearish' | 'neutral' {
    if (score > 0.2) return 'bullish';
    if (score < -0.2) return 'bearish';
    return 'neutral';
  }

  private determineTrend(scores: { score: number }[]): 'rising' | 'falling' | 'stable' {
    if (scores.length < 2) return 'stable';
    
    const recent = scores.slice(-3).map(s => s.score);
    const older = scores.slice(-6, -3).map(s => s.score);
    
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
    const olderAvg = older.length > 0 ? older.reduce((a, b) => a + b, 0) / older.length : recentAvg;
    
    if (recentAvg > olderAvg + 0.1) return 'rising';
    if (recentAvg < olderAvg - 0.1) return 'falling';
    return 'stable';
  }

  private calculateVolatility(scores: { score: number }[]): 'low' | 'medium' | 'high' {
    if (scores.length < 2) return 'low';
    
    const values = scores.map(s => s.score);
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    
    if (stdDev > 0.4) return 'high';
    if (stdDev > 0.2) return 'medium';
    return 'low';
  }

  private getSectorSentimentPatterns(market: 'indian' | 'foreign' | 'crypto'): { keyword: string; sentiment: number; weight: number }[] {
    const patterns = {
      indian: [
        { keyword: 'banking', sentiment: 0.1, weight: 0.8 },
        { keyword: 'it sector', sentiment: 0.2, weight: 0.7 },
        { keyword: 'pharma', sentiment: 0.1, weight: 0.6 },
        { keyword: 'auto', sentiment: -0.1, weight: 0.6 }
      ],
      foreign: [
        { keyword: 'tech stocks', sentiment: 0.2, weight: 0.8 },
        { keyword: 'energy', sentiment: -0.1, weight: 0.7 },
        { keyword: 'financials', sentiment: 0.1, weight: 0.7 }
      ],
      crypto: [
        { keyword: 'defi', sentiment: 0.3, weight: 0.8 },
        { keyword: 'regulation', sentiment: -0.2, weight: 0.9 },
        { keyword: 'adoption', sentiment: 0.4, weight: 0.8 }
      ]
    };

    return patterns[market];
  }

  private getDefaultSentiment(market: 'indian' | 'foreign' | 'crypto'): MarketSentiment {
    return {
      market,
      sentiment: 'neutral',
      score: 0,
      confidence: 0.3,
      trend: 'stable',
      volatility: 'medium',
      lastUpdated: new Date()
    };
  }

  private getFallbackSentiments(): MarketSentiment[] {
    return [
      this.getDefaultSentiment('indian'),
      this.getDefaultSentiment('foreign'),
      this.getDefaultSentiment('crypto')
    ];
  }
}

export const advancedSentimentEngine = new AdvancedSentimentEngine();
