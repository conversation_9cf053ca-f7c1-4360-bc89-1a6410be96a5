// Advanced Market Sentiment Analysis Engine

import { NewsArticle } from '../news/types';
import { 
  MarketSentiment, 
  NewsImpactScore, 
  FINANCIAL_SENTIMENT_CONFIG,
  NEWS_IMPACT_CATEGORIES 
} from './types';

export class AdvancedSentimentEngine {
  private sentimentCache = new Map<string, MarketSentiment>();
  private impactCache = new Map<string, NewsImpactScore>();

  /**
   * Analyze market sentiment from news articles with advanced NLP
   */
  async analyzeMarketSentiment(articles: NewsArticle[]): Promise<MarketSentiment[]> {
    try {
      const [indianSentiment, foreignSentiment, cryptoSentiment] = await Promise.all([
        this.analyzeMarketSpecificSentiment(articles, 'indian'),
        this.analyzeMarketSpecificSentiment(articles, 'foreign'),
        this.analyzeMarketSpecificSentiment(articles, 'crypto')
      ]);

      return [indianSentiment, foreignSentiment, cryptoSentiment];
    } catch (error) {
      console.error('Error in sentiment analysis:', error);
      return this.getFallbackSentiments();
    }
  }

  /**
   * Analyze sentiment for specific market with advanced contextual understanding
   */
  private async analyzeMarketSpecificSentiment(
    articles: NewsArticle[],
    market: 'indian' | 'foreign' | 'crypto'
  ): Promise<MarketSentiment> {
    console.log(`🎯 Analyzing ${market} market sentiment from ${articles.length} articles`);

    const relevantArticles = this.filterRelevantArticles(articles, market);
    console.log(`📰 Found ${relevantArticles.length} relevant articles for ${market} market`);

    if (relevantArticles.length === 0) {
      return this.getDefaultSentiment(market);
    }

    // Enhanced multi-layer sentiment analysis
    const sentimentScores = await Promise.all(
      relevantArticles.map(article => this.analyzeSingleArticleSentiment(article, market))
    );

    // Market-specific sentiment aggregation with domain expertise
    const aggregatedSentiment = this.aggregateMarketSpecificSentiment(sentimentScores, relevantArticles, market);

    // Get technical sentiment overlay with market-specific indicators
    const technicalSentiment = await this.getEnhancedTechnicalSentiment(market);

    // Advanced sentiment combination with market-specific weighting
    const finalSentiment = this.combineMarketSpecificSentiments(aggregatedSentiment, technicalSentiment, market);

    // Enhanced trend and volatility analysis
    const trend = this.determineMarketSpecificTrend(sentimentScores, market);
    const volatility = this.calculateMarketSpecificVolatility(sentimentScores, market);

    const result = {
      market,
      sentiment: this.classifyMarketSpecificSentiment(finalSentiment.score, market),
      score: finalSentiment.score,
      confidence: this.enhanceConfidenceScore(finalSentiment.confidence, relevantArticles, market),
      trend,
      volatility,
      lastUpdated: new Date()
    };

    console.log(`✅ ${market} sentiment analysis complete:`, {
      sentiment: result.sentiment,
      score: result.score.toFixed(3),
      confidence: result.confidence.toFixed(3),
      trend: result.trend,
      volatility: result.volatility
    });

    return result;
  }

  /**
   * Advanced single article sentiment analysis
   */
  private async analyzeSingleArticleSentiment(
    article: NewsArticle, 
    market: 'indian' | 'foreign' | 'crypto'
  ): Promise<{ score: number; confidence: number; impact: number }> {
    const content = `${article.title} ${article.description}`.toLowerCase();
    
    // 1. Lexicon-based sentiment scoring
    const lexiconScore = this.calculateLexiconSentiment(content);
    
    // 2. Context-aware sentiment adjustment
    const contextScore = this.adjustForContext(content, market);
    
    // 3. Impact category detection
    const impactMultiplier = this.detectImpactCategory(content);
    
    // 4. Temporal relevance
    const timeDecay = this.calculateTimeDecay(article.publishedAt);
    
    // 5. Source credibility factor
    const credibilityFactor = this.getSourceCredibility(article.source.name);

    // Combine all factors
    const rawScore = (lexiconScore + contextScore) * impactMultiplier * timeDecay * credibilityFactor;
    const normalizedScore = Math.max(-1, Math.min(1, rawScore));
    
    // Calculate confidence based on multiple factors
    const confidence = this.calculateConfidence(content, impactMultiplier, credibilityFactor);

    return {
      score: normalizedScore,
      confidence,
      impact: impactMultiplier
    };
  }

  /**
   * Lexicon-based sentiment scoring with financial context
   */
  private calculateLexiconSentiment(content: string): number {
    const config = FINANCIAL_SENTIMENT_CONFIG;
    let score = 0;
    let wordCount = 0;

    // Positive sentiment words
    config.positiveKeywords.forEach(word => {
      const matches = (content.match(new RegExp(word, 'gi')) || []).length;
      score += matches * 0.1;
      wordCount += matches;
    });

    // Negative sentiment words
    config.negativeKeywords.forEach(word => {
      const matches = (content.match(new RegExp(word, 'gi')) || []).length;
      score -= matches * 0.1;
      wordCount += matches;
    });

    // Neutral words (reduce extreme scores)
    config.neutralKeywords.forEach(word => {
      const matches = (content.match(new RegExp(word, 'gi')) || []).length;
      if (matches > 0) {
        score *= 0.8; // Dampen sentiment if neutral words present
      }
    });

    // Normalize by word count to prevent bias toward longer articles
    return wordCount > 0 ? score / Math.sqrt(wordCount) : 0;
  }

  /**
   * Context-aware sentiment adjustment
   */
  private adjustForContext(content: string, market: 'indian' | 'foreign' | 'crypto'): number {
    const marketTerms = FINANCIAL_SENTIMENT_CONFIG.marketSpecificTerms[market];
    let contextScore = 0;
    let relevanceBonus = 0;

    // Check for market-specific terms
    marketTerms.forEach(term => {
      if (content.includes(term.toLowerCase())) {
        relevanceBonus += 0.1;
      }
    });

    // Sector-specific sentiment patterns
    const sectorPatterns = this.getSectorSentimentPatterns(market);
    sectorPatterns.forEach(pattern => {
      if (content.includes(pattern.keyword)) {
        contextScore += pattern.sentiment * pattern.weight;
      }
    });

    return (contextScore + relevanceBonus) * 0.5; // Weight context adjustment
  }

  /**
   * Detect impact category and return multiplier
   */
  private detectImpactCategory(content: string): number {
    let maxImpact = 1.0;

    Object.entries(NEWS_IMPACT_CATEGORIES).forEach(([category, config]) => {
      const hasKeywords = config.keywords.some(keyword => 
        content.includes(keyword.toLowerCase())
      );
      
      if (hasKeywords) {
        maxImpact = Math.max(maxImpact, config.baseImpact);
      }
    });

    return maxImpact;
  }

  /**
   * Calculate time decay factor
   */
  private calculateTimeDecay(publishedAt: string): number {
    const hoursAgo = (Date.now() - new Date(publishedAt).getTime()) / (1000 * 60 * 60);
    
    // Exponential decay: newer news has higher impact
    if (hoursAgo < 1) return 1.0;
    if (hoursAgo < 6) return 0.9;
    if (hoursAgo < 24) return 0.7;
    if (hoursAgo < 72) return 0.5;
    return 0.3;
  }

  /**
   * Get source credibility factor
   */
  private getSourceCredibility(sourceName: string): number {
    const credibilityMap: Record<string, number> = {
      'reuters': 1.0,
      'bloomberg': 1.0,
      'economic times': 0.95,
      'financial times': 0.95,
      'wall street journal': 0.95,
      'business standard': 0.9,
      'livemint': 0.9,
      'moneycontrol': 0.85,
      'coindesk': 0.9,
      'cointelegraph': 0.8,
      'the block': 0.85
    };

    const source = sourceName.toLowerCase();
    for (const [key, value] of Object.entries(credibilityMap)) {
      if (source.includes(key)) {
        return value;
      }
    }
    
    return 0.7; // Default credibility for unknown sources
  }

  /**
   * Calculate confidence score
   */
  private calculateConfidence(content: string, impactMultiplier: number, credibilityFactor: number): number {
    let confidence = 0.5; // Base confidence

    // Length factor (longer articles generally more reliable)
    const wordCount = content.split(' ').length;
    confidence += Math.min(0.2, wordCount / 500);

    // Impact factor
    confidence += (impactMultiplier - 1) * 0.2;

    // Source credibility
    confidence += (credibilityFactor - 0.7) * 0.3;

    // Sentiment word density
    const sentimentWords = this.countSentimentWords(content);
    confidence += Math.min(0.2, sentimentWords / 20);

    return Math.min(1.0, confidence);
  }

  /**
   * Count sentiment-bearing words
   */
  private countSentimentWords(content: string): number {
    const config = FINANCIAL_SENTIMENT_CONFIG;
    const allSentimentWords = [
      ...config.positiveKeywords,
      ...config.negativeKeywords,
      ...config.neutralKeywords
    ];

    return allSentimentWords.filter(word => 
      content.includes(word.toLowerCase())
    ).length;
  }

  /**
   * Enhanced market-specific sentiment aggregation with domain expertise
   */
  private aggregateMarketSpecificSentiment(
    scores: { score: number; confidence: number; impact: number }[],
    articles: NewsArticle[],
    market: 'indian' | 'foreign' | 'crypto'
  ): { score: number; confidence: number } {
    if (scores.length === 0) {
      return { score: 0, confidence: 0 };
    }

    console.log(`📊 Aggregating ${scores.length} sentiment scores for ${market} market`);

    let weightedScore = 0;
    let totalWeight = 0;
    let confidenceSum = 0;

    // Market-specific weighting factors
    const marketWeights = this.getMarketSpecificWeights(market);

    scores.forEach((scoreData, index) => {
      const article = articles[index];

      // Base weight from confidence and impact
      let weight = scoreData.confidence * scoreData.impact;

      // Apply market-specific adjustments
      weight *= this.getMarketRelevanceMultiplier(article, market);
      weight *= this.getSourceCredibilityForMarket(article.source.name, market);
      weight *= this.getTimeDecayForMarket(article.publishedAt, market);
      weight *= this.getCategoryWeightForMarket(article.category, market);

      // Apply market-specific sentiment amplification/dampening
      const adjustedScore = this.adjustSentimentForMarket(scoreData.score, market, article);

      weightedScore += adjustedScore * weight;
      totalWeight += weight;
      confidenceSum += scoreData.confidence;

      console.log(`📰 Article weight calculation:`, {
        title: article.title.substring(0, 50) + '...',
        originalScore: scoreData.score.toFixed(3),
        adjustedScore: adjustedScore.toFixed(3),
        weight: weight.toFixed(3),
        confidence: scoreData.confidence.toFixed(3)
      });
    });

    const finalScore = totalWeight > 0 ? weightedScore / totalWeight : 0;
    const avgConfidence = confidenceSum / scores.length;

    console.log(`🎯 ${market} aggregation result:`, {
      finalScore: finalScore.toFixed(3),
      avgConfidence: avgConfidence.toFixed(3),
      totalWeight: totalWeight.toFixed(3)
    });

    return {
      score: finalScore,
      confidence: avgConfidence
    };
  }

  /**
   * Legacy method for backward compatibility
   */
  private aggregateSentimentScores(
    scores: { score: number; confidence: number; impact: number }[],
    articles: NewsArticle[]
  ): { score: number; confidence: number } {
    return this.aggregateMarketSpecificSentiment(scores, articles, 'indian');
  }

  /**
   * Enhanced confidence scoring with market-specific factors
   */
  private enhanceConfidenceScore(
    baseConfidence: number,
    articles: NewsArticle[],
    market: 'indian' | 'foreign' | 'crypto'
  ): number {
    let enhancedConfidence = baseConfidence;

    // Sample size factor
    const sampleSizeFactor = Math.min(1.0, articles.length / 10);
    enhancedConfidence += sampleSizeFactor * 0.1;

    // Source diversity factor
    const uniqueSources = new Set(articles.map(a => a.source.name)).size;
    const diversityFactor = Math.min(1.0, uniqueSources / 5);
    enhancedConfidence += diversityFactor * 0.1;

    // Market-specific confidence adjustments
    const marketConfidenceFactors = {
      indian: {
        policyNewsBonus: 0.15,      // High confidence in policy news
        earningsBonus: 0.1,         // Moderate confidence in earnings
        globalNewsDiscount: -0.05   // Lower confidence in global news impact
      },
      foreign: {
        economicDataBonus: 0.2,     // High confidence in economic data
        fedNewsBonus: 0.18,         // Very high confidence in Fed news
        corporateNewsBonus: 0.12    // Good confidence in corporate news
      },
      crypto: {
        regulatoryNewsBonus: 0.25,  // Extremely high confidence in regulatory news
        adoptionNewsBonus: 0.2,     // High confidence in adoption news
        volatilityDiscount: -0.1    // Lower confidence due to inherent volatility
      }
    };

    const factors = marketConfidenceFactors[market];

    // Apply market-specific bonuses based on article content
    articles.forEach(article => {
      const content = `${article.title} ${article.description}`.toLowerCase();

      if (market === 'indian') {
        if (content.includes('rbi') || content.includes('policy')) {
          enhancedConfidence += factors.policyNewsBonus / articles.length;
        }
        if (content.includes('earnings') || content.includes('results')) {
          enhancedConfidence += factors.earningsBonus / articles.length;
        }
        if (content.includes('global') || content.includes('international')) {
          enhancedConfidence += factors.globalNewsDiscount / articles.length;
        }
      } else if (market === 'foreign') {
        if (content.includes('gdp') || content.includes('inflation') || content.includes('employment')) {
          enhancedConfidence += factors.economicDataBonus / articles.length;
        }
        if (content.includes('fed') || content.includes('powell')) {
          enhancedConfidence += factors.fedNewsBonus / articles.length;
        }
        if (content.includes('earnings') || content.includes('corporate')) {
          enhancedConfidence += factors.corporateNewsBonus / articles.length;
        }
      } else if (market === 'crypto') {
        if (content.includes('regulation') || content.includes('sec') || content.includes('legal')) {
          enhancedConfidence += factors.regulatoryNewsBonus / articles.length;
        }
        if (content.includes('adoption') || content.includes('institutional')) {
          enhancedConfidence += factors.adoptionNewsBonus / articles.length;
        }
        // Apply volatility discount
        enhancedConfidence += factors.volatilityDiscount;
      }
    });

    // Time recency factor
    const avgHoursAgo = articles.reduce((sum, article) => {
      return sum + (Date.now() - new Date(article.publishedAt).getTime()) / (1000 * 60 * 60);
    }, 0) / articles.length;

    const recencyFactor = Math.max(0, 1 - (avgHoursAgo / 24)); // Decay over 24 hours
    enhancedConfidence += recencyFactor * 0.1;

    return Math.max(0.1, Math.min(1.0, enhancedConfidence));
  }

  /**
   * Enhanced technical sentiment with market-specific indicators
   */
  private async getEnhancedTechnicalSentiment(market: 'indian' | 'foreign' | 'crypto'): Promise<{ score: number; confidence: number }> {
    // Enhanced technical analysis simulation with market-specific characteristics
    const technicalProfiles = {
      indian: {
        volatility: 0.12,
        trendStrength: 0.6,
        momentum: 0.05,
        confidence: 0.7
      },
      foreign: {
        volatility: 0.08,
        trendStrength: 0.7,
        momentum: 0.03,
        confidence: 0.75
      },
      crypto: {
        volatility: 0.25,
        trendStrength: 0.5,
        momentum: 0.15,
        confidence: 0.6
      }
    };

    const profile = technicalProfiles[market];

    // Generate realistic technical sentiment
    const baseScore = (Math.random() - 0.5) * 2 * profile.momentum;
    const trendAdjustment = (Math.random() - 0.5) * profile.trendStrength * 0.3;
    const volatilityNoise = (Math.random() - 0.5) * profile.volatility * 0.2;

    const technicalScore = Math.max(-1, Math.min(1, baseScore + trendAdjustment + volatilityNoise));
    const technicalConfidence = profile.confidence + (Math.random() - 0.5) * 0.2;

    return {
      score: technicalScore,
      confidence: Math.max(0.3, Math.min(1.0, technicalConfidence))
    };
  }

  /**
   * Legacy method for backward compatibility
   */
  private async getTechnicalSentiment(market: 'indian' | 'foreign' | 'crypto'): Promise<{ score: number; confidence: number }> {
    return this.getEnhancedTechnicalSentiment(market);
  }

  /**
   * Simulate technical sentiment (replace with real technical analysis)
   */
  private simulateTechnicalSentiment(volatility: number): { score: number; confidence: number } {
    const score = (Math.random() - 0.5) * 2 * volatility;
    const confidence = 0.6 + Math.random() * 0.3;
    
    return { score, confidence };
  }

  /**
   * Enhanced market-specific sentiment combination
   */
  private combineMarketSpecificSentiments(
    newsSentiment: { score: number; confidence: number },
    technicalSentiment: { score: number; confidence: number },
    market: 'indian' | 'foreign' | 'crypto'
  ): { score: number; confidence: number } {
    // Market-specific weighting between news and technical sentiment
    const weights = {
      indian: { news: 0.75, technical: 0.25 },    // Indian market more news-driven
      foreign: { news: 0.7, technical: 0.3 },     // Balanced approach
      crypto: { news: 0.8, technical: 0.2 }       // Crypto heavily news-driven
    };

    const marketWeights = weights[market];

    const combinedScore = (newsSentiment.score * marketWeights.news) +
                         (technicalSentiment.score * marketWeights.technical);

    const combinedConfidence = (newsSentiment.confidence * marketWeights.news) +
                              (technicalSentiment.confidence * marketWeights.technical);

    console.log(`🔄 ${market} sentiment combination:`, {
      newsScore: newsSentiment.score.toFixed(3),
      newsConfidence: newsSentiment.confidence.toFixed(3),
      technicalScore: technicalSentiment.score.toFixed(3),
      technicalConfidence: technicalSentiment.confidence.toFixed(3),
      combinedScore: combinedScore.toFixed(3),
      combinedConfidence: combinedConfidence.toFixed(3)
    });

    return {
      score: combinedScore,
      confidence: combinedConfidence
    };
  }

  /**
   * Enhanced market-specific trend determination
   */
  private determineMarketSpecificTrend(
    scores: { score: number; confidence: number; impact: number }[],
    market: 'indian' | 'foreign' | 'crypto'
  ): 'rising' | 'falling' | 'stable' {
    if (scores.length < 2) return 'stable';

    // Calculate weighted trend based on recent vs older scores
    const recentScores = scores.slice(-Math.ceil(scores.length / 2));
    const olderScores = scores.slice(0, Math.floor(scores.length / 2));

    const recentAvg = recentScores.reduce((sum, s) => sum + s.score, 0) / recentScores.length;
    const olderAvg = olderScores.reduce((sum, s) => sum + s.score, 0) / olderScores.length;

    const trendDifference = recentAvg - olderAvg;

    // Market-specific trend thresholds
    const trendThresholds = {
      indian: { rising: 0.08, falling: -0.08 },
      foreign: { rising: 0.1, falling: -0.1 },
      crypto: { rising: 0.05, falling: -0.05 }  // Crypto more sensitive to trends
    };

    const threshold = trendThresholds[market];

    if (trendDifference > threshold.rising) return 'rising';
    if (trendDifference < threshold.falling) return 'falling';
    return 'stable';
  }

  /**
   * Enhanced market-specific volatility calculation
   */
  private calculateMarketSpecificVolatility(
    scores: { score: number; confidence: number; impact: number }[],
    market: 'indian' | 'foreign' | 'crypto'
  ): 'low' | 'medium' | 'high' {
    if (scores.length < 2) return 'medium';

    // Calculate standard deviation of sentiment scores
    const sentimentValues = scores.map(s => s.score);
    const mean = sentimentValues.reduce((sum, val) => sum + val, 0) / sentimentValues.length;
    const variance = sentimentValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / sentimentValues.length;
    const standardDeviation = Math.sqrt(variance);

    // Market-specific volatility thresholds
    const volatilityThresholds = {
      indian: { low: 0.15, high: 0.35 },
      foreign: { low: 0.12, high: 0.3 },
      crypto: { low: 0.2, high: 0.5 }  // Crypto naturally more volatile
    };

    const threshold = volatilityThresholds[market];

    if (standardDeviation < threshold.low) return 'low';
    if (standardDeviation > threshold.high) return 'high';
    return 'medium';
  }

  /**
   * Legacy methods for backward compatibility
   */
  private combineSentiments(
    newsSentiment: { score: number; confidence: number },
    technicalSentiment: { score: number; confidence: number }
  ): { score: number; confidence: number } {
    return this.combineMarketSpecificSentiments(newsSentiment, technicalSentiment, 'indian');
  }

  private determineTrend(scores: { score: number; confidence: number; impact: number }[]): 'rising' | 'falling' | 'stable' {
    return this.determineMarketSpecificTrend(scores, 'indian');
  }

  private calculateVolatility(scores: { score: number; confidence: number; impact: number }[]): 'low' | 'medium' | 'high' {
    return this.calculateMarketSpecificVolatility(scores, 'indian');
  }

  /**
   * Market-specific weighting and adjustment methods
   */
  private getMarketSpecificWeights(market: 'indian' | 'foreign' | 'crypto'): any {
    return {
      indian: {
        policyWeight: 1.5,      // RBI policy has high impact
        earningsWeight: 1.3,    // Corporate earnings important
        globalWeight: 0.8,      // Global news has moderate impact
        sectorWeight: 1.2       // Sector-specific news important
      },
      foreign: {
        policyWeight: 1.4,      // Fed policy crucial
        earningsWeight: 1.2,    // Earnings important
        globalWeight: 1.5,      // Global news highly relevant
        sectorWeight: 1.1       // Sector news moderately important
      },
      crypto: {
        policyWeight: 2.0,      // Regulatory news has massive impact
        earningsWeight: 0.8,    // Traditional earnings less relevant
        globalWeight: 1.3,      // Global sentiment affects crypto
        sectorWeight: 1.8       // Crypto-specific news very important
      }
    }[market];
  }

  private getMarketRelevanceMultiplier(article: NewsArticle, market: 'indian' | 'foreign' | 'crypto'): number {
    const content = `${article.title} ${article.description}`.toLowerCase();
    const marketTerms = FINANCIAL_SENTIMENT_CONFIG.marketSpecificTerms[market];

    let relevanceScore = 0;
    let termCount = 0;

    // Count relevant terms with different weights
    marketTerms.forEach(term => {
      const termLower = term.toLowerCase();
      const matches = (content.match(new RegExp(termLower, 'g')) || []).length;

      if (matches > 0) {
        termCount++;
        // Weight important terms higher
        if (this.isHighImpactTerm(termLower, market)) {
          relevanceScore += matches * 2.0;
        } else {
          relevanceScore += matches * 1.0;
        }
      }
    });

    // Normalize and apply market-specific scaling
    const baseRelevance = Math.min(2.0, 0.5 + (relevanceScore * 0.1));
    return baseRelevance * this.getMarketRelevanceBonus(market, termCount);
  }

  private isHighImpactTerm(term: string, market: 'indian' | 'foreign' | 'crypto'): boolean {
    const highImpactTerms = {
      indian: ['rbi', 'sensex', 'nifty', 'rupee', 'modi', 'budget'],
      foreign: ['fed', 'powell', 'nasdaq', 'dow', 'sp500', 'dollar'],
      crypto: ['bitcoin', 'ethereum', 'sec', 'regulation', 'defi', 'nft']
    };

    return highImpactTerms[market].includes(term);
  }

  private getMarketRelevanceBonus(market: 'indian' | 'foreign' | 'crypto', termCount: number): number {
    const bonusFactors = {
      indian: Math.min(1.5, 1.0 + (termCount * 0.1)),
      foreign: Math.min(1.4, 1.0 + (termCount * 0.08)),
      crypto: Math.min(1.8, 1.0 + (termCount * 0.15))
    };

    return bonusFactors[market];
  }

  private getSourceCredibilityForMarket(sourceName: string, market: 'indian' | 'foreign' | 'crypto'): number {
    const sourceCredibility = {
      indian: {
        'economic times': 1.0,
        'business standard': 0.95,
        'livemint': 0.9,
        'moneycontrol': 0.85,
        'reuters': 0.95,
        'bloomberg': 0.95
      },
      foreign: {
        'reuters': 1.0,
        'bloomberg': 1.0,
        'wall street journal': 0.95,
        'financial times': 0.95,
        'cnbc': 0.85,
        'marketwatch': 0.8
      },
      crypto: {
        'coindesk': 1.0,
        'cointelegraph': 0.85,
        'the block': 0.9,
        'decrypt': 0.8,
        'bloomberg': 0.95,
        'reuters': 0.95
      }
    };

    const source = sourceName.toLowerCase();
    const marketSources = sourceCredibility[market];

    for (const [key, value] of Object.entries(marketSources)) {
      if (source.includes(key)) {
        return value;
      }
    }

    return 0.7; // Default credibility
  }

  /**
   * Enhanced helper methods
   */
  private filterRelevantArticles(articles: NewsArticle[], market: 'indian' | 'foreign' | 'crypto'): NewsArticle[] {
    const marketTerms = FINANCIAL_SENTIMENT_CONFIG.marketSpecificTerms[market];

    const relevantArticles = articles.filter(article => {
      const content = `${article.title} ${article.description}`.toLowerCase();
      return marketTerms.some(term => content.includes(term.toLowerCase()));
    });

    console.log(`🔍 Filtered articles for ${market}:`, {
      total: articles.length,
      relevant: relevantArticles.length,
      percentage: ((relevantArticles.length / articles.length) * 100).toFixed(1) + '%'
    });

    return relevantArticles;
  }

  /**
   * Additional market-specific helper methods
   */
  private getTimeDecayForMarket(publishedAt: string, market: 'indian' | 'foreign' | 'crypto'): number {
    const hoursAgo = (Date.now() - new Date(publishedAt).getTime()) / (1000 * 60 * 60);

    // Different markets have different news sensitivity
    const decayRates = {
      indian: {
        immediate: { threshold: 2, factor: 1.0 },
        recent: { threshold: 12, factor: 0.8 },
        daily: { threshold: 48, factor: 0.6 },
        old: { threshold: Infinity, factor: 0.3 }
      },
      foreign: {
        immediate: { threshold: 1, factor: 1.0 },
        recent: { threshold: 8, factor: 0.85 },
        daily: { threshold: 24, factor: 0.7 },
        old: { threshold: Infinity, factor: 0.4 }
      },
      crypto: {
        immediate: { threshold: 0.5, factor: 1.0 },
        recent: { threshold: 4, factor: 0.9 },
        daily: { threshold: 12, factor: 0.7 },
        old: { threshold: Infinity, factor: 0.2 }
      }
    };

    const rates = decayRates[market];

    if (hoursAgo < rates.immediate.threshold) return rates.immediate.factor;
    if (hoursAgo < rates.recent.threshold) return rates.recent.factor;
    if (hoursAgo < rates.daily.threshold) return rates.daily.factor;
    return rates.old.factor;
  }

  private getCategoryWeightForMarket(category: string, market: 'indian' | 'foreign' | 'crypto'): number {
    const categoryWeights = {
      indian: {
        'business': 1.2,
        'economy': 1.5,
        'politics': 1.1,
        'technology': 0.9,
        'general': 0.7
      },
      foreign: {
        'business': 1.3,
        'economy': 1.4,
        'politics': 1.0,
        'technology': 1.1,
        'general': 0.8
      },
      crypto: {
        'business': 1.1,
        'economy': 1.2,
        'politics': 1.3,
        'technology': 1.8,
        'general': 0.6
      }
    };

    return categoryWeights[market][category] || 1.0;
  }

  private adjustSentimentForMarket(score: number, market: 'indian' | 'foreign' | 'crypto', article: NewsArticle): number {
    let adjustedScore = score;
    const content = `${article.title} ${article.description}`.toLowerCase();

    // Market-specific sentiment adjustments
    if (market === 'indian') {
      // Indian market is more sensitive to policy and monsoon news
      if (content.includes('policy') || content.includes('rbi')) {
        adjustedScore *= 1.3;
      }
      if (content.includes('monsoon') || content.includes('agriculture')) {
        adjustedScore *= 1.2;
      }
      // Indian market reacts strongly to global cues but with delay
      if (content.includes('global') || content.includes('international')) {
        adjustedScore *= 0.9;
      }
    } else if (market === 'foreign') {
      // Global markets are more sensitive to economic data
      if (content.includes('gdp') || content.includes('inflation') || content.includes('employment')) {
        adjustedScore *= 1.4;
      }
      if (content.includes('fed') || content.includes('interest rate')) {
        adjustedScore *= 1.5;
      }
    } else if (market === 'crypto') {
      // Crypto is extremely sensitive to regulation and adoption news
      if (content.includes('regulation') || content.includes('ban') || content.includes('legal')) {
        adjustedScore *= 2.0;
      }
      if (content.includes('adoption') || content.includes('institutional')) {
        adjustedScore *= 1.8;
      }
      if (content.includes('hack') || content.includes('security')) {
        adjustedScore *= 1.5;
      }
      // Crypto sentiment is more volatile
      adjustedScore *= 1.2;
    }

    return Math.max(-1, Math.min(1, adjustedScore));
  }

  private classifyMarketSpecificSentiment(score: number, market: 'indian' | 'foreign' | 'crypto'): 'bullish' | 'bearish' | 'neutral' {
    // Market-specific thresholds
    const thresholds = {
      indian: { bullish: 0.15, bearish: -0.15 },    // Indian market is more conservative
      foreign: { bullish: 0.2, bearish: -0.2 },     // Standard thresholds
      crypto: { bullish: 0.1, bearish: -0.1 }       // Crypto is more sensitive
    };

    const threshold = thresholds[market];

    if (score > threshold.bullish) return 'bullish';
    if (score < threshold.bearish) return 'bearish';
    return 'neutral';
  }

  private classifySentiment(score: number): 'bullish' | 'bearish' | 'neutral' {
    return this.classifyMarketSpecificSentiment(score, 'indian');
  }

  private determineTrend(scores: { score: number }[]): 'rising' | 'falling' | 'stable' {
    if (scores.length < 2) return 'stable';
    
    const recent = scores.slice(-3).map(s => s.score);
    const older = scores.slice(-6, -3).map(s => s.score);
    
    const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
    const olderAvg = older.length > 0 ? older.reduce((a, b) => a + b, 0) / older.length : recentAvg;
    
    if (recentAvg > olderAvg + 0.1) return 'rising';
    if (recentAvg < olderAvg - 0.1) return 'falling';
    return 'stable';
  }

  private calculateVolatility(scores: { score: number }[]): 'low' | 'medium' | 'high' {
    if (scores.length < 2) return 'low';
    
    const values = scores.map(s => s.score);
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    
    if (stdDev > 0.4) return 'high';
    if (stdDev > 0.2) return 'medium';
    return 'low';
  }

  private getSectorSentimentPatterns(market: 'indian' | 'foreign' | 'crypto'): { keyword: string; sentiment: number; weight: number }[] {
    const patterns = {
      indian: [
        { keyword: 'banking', sentiment: 0.1, weight: 0.8 },
        { keyword: 'it sector', sentiment: 0.2, weight: 0.7 },
        { keyword: 'pharma', sentiment: 0.1, weight: 0.6 },
        { keyword: 'auto', sentiment: -0.1, weight: 0.6 }
      ],
      foreign: [
        { keyword: 'tech stocks', sentiment: 0.2, weight: 0.8 },
        { keyword: 'energy', sentiment: -0.1, weight: 0.7 },
        { keyword: 'financials', sentiment: 0.1, weight: 0.7 }
      ],
      crypto: [
        { keyword: 'defi', sentiment: 0.3, weight: 0.8 },
        { keyword: 'regulation', sentiment: -0.2, weight: 0.9 },
        { keyword: 'adoption', sentiment: 0.4, weight: 0.8 }
      ]
    };

    return patterns[market];
  }

  private getDefaultSentiment(market: 'indian' | 'foreign' | 'crypto'): MarketSentiment {
    return {
      market,
      sentiment: 'neutral',
      score: 0,
      confidence: 0.3,
      trend: 'stable',
      volatility: 'medium',
      lastUpdated: new Date()
    };
  }

  private getFallbackSentiments(): MarketSentiment[] {
    return [
      this.getDefaultSentiment('indian'),
      this.getDefaultSentiment('foreign'),
      this.getDefaultSentiment('crypto')
    ];
  }
}

export const advancedSentimentEngine = new AdvancedSentimentEngine();
