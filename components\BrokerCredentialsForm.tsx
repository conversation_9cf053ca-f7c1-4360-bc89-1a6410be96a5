"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Shield,
  Eye,
  EyeOff,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
  HelpCircle
} from 'lucide-react';

interface BrokerCredentialsFormProps {
  brokerId: string;
  brokerName: string;
  onConnect: (credentials: any) => void;
  isLoading: boolean;
}

export default function BrokerCredentialsForm({ 
  brokerId, 
  brokerName, 
  onConnect, 
  isLoading 
}: BrokerCredentialsFormProps) {
  const [credentials, setCredentials] = useState({
    apiKey: '',
    clientCode: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showForm, setShowForm] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onConnect(credentials);
  };

  const isFormValid = credentials.apiKey && credentials.clientCode && credentials.password;

  return (
    <div className="space-y-4">
      {/* Quick Connect Button */}
      <Button
        onClick={() => onConnect({})} // Use env variables
        disabled={isLoading}
        className="w-full bg-blue-600 hover:bg-blue-700 text-white"
      >
        {isLoading ? 'Connecting...' : `Quick Connect to ${brokerName}`}
      </Button>

      <div className="text-center text-slate-400 text-sm">or</div>

      {/* Manual Credentials Button */}
      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
          >
            Enter Credentials Manually
          </Button>
        </DialogTrigger>
        
        <DialogContent className="bg-slate-800 border-slate-700 text-white max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-400" />
              {brokerName} Credentials
            </DialogTitle>
            <DialogDescription className="text-slate-300">
              Enter your {brokerName} API credentials to connect your demat account
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Security Notice */}
            <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <Shield className="h-4 w-4 text-blue-400 mt-0.5" />
                <div className="text-xs">
                  <p className="text-blue-300 font-medium">Secure & Private</p>
                  <p className="text-slate-300">
                    Credentials are used only for API connection and not stored permanently.
                  </p>
                </div>
              </div>
            </div>

            {/* API Key Field */}
            <div className="space-y-2">
              <Label htmlFor="apiKey" className="text-slate-300">
                API Key
                <Badge variant="outline" className="ml-2 text-xs border-red-500/30 text-red-300">
                  Required
                </Badge>
              </Label>
              <Input
                id="apiKey"
                type="text"
                placeholder="SmartAPI_12345678..."
                value={credentials.apiKey}
                onChange={(e) => setCredentials(prev => ({ ...prev, apiKey: e.target.value }))}
                className="bg-slate-700 border-slate-600 text-white"
              />
              <p className="text-xs text-slate-400">
                Get from: <a 
                  href="https://smartapi.angelbroking.com/" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:underline inline-flex items-center gap-1"
                >
                  SmartAPI Portal <ExternalLink className="h-3 w-3" />
                </a>
              </p>
            </div>

            {/* Client Code Field */}
            <div className="space-y-2">
              <Label htmlFor="clientCode" className="text-slate-300">
                Client Code
                <Badge variant="outline" className="ml-2 text-xs border-red-500/30 text-red-300">
                  Required
                </Badge>
              </Label>
              <Input
                id="clientCode"
                type="text"
                placeholder="A123456"
                value={credentials.clientCode}
                onChange={(e) => setCredentials(prev => ({ ...prev, clientCode: e.target.value }))}
                className="bg-slate-700 border-slate-600 text-white"
              />
              <p className="text-xs text-slate-400">
                Your Angel One trading account ID
              </p>
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-slate-300">
                Trading Password
                <Badge variant="outline" className="ml-2 text-xs border-red-500/30 text-red-300">
                  Required
                </Badge>
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Your trading password"
                  value={credentials.password}
                  onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                  className="bg-slate-700 border-slate-600 text-white pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 text-slate-400 hover:text-white"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
              <p className="text-xs text-slate-400">
                Your Angel One login password
              </p>
            </div>

            {/* 2FA Warning */}
            <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-400 mt-0.5" />
                <div className="text-xs">
                  <p className="text-yellow-300 font-medium">Important: Disable 2FA</p>
                  <p className="text-slate-300">
                    Make sure Two-Factor Authentication is disabled in your Angel One account settings.
                  </p>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex gap-2 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowForm(false)}
                className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={!isFormValid || isLoading}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isLoading ? 'Connecting...' : 'Connect'}
              </Button>
            </div>
          </form>

          {/* Help Section */}
          <div className="border-t border-slate-700 pt-4">
            <div className="flex items-center gap-2 text-slate-400 text-xs">
              <HelpCircle className="h-4 w-4" />
              <span>Need help? Contact Angel One support: 040-47 47 47 47</span>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Setup Instructions */}
      <div className="text-xs text-slate-400 text-center space-y-1">
        <p>Don't have API access?</p>
        <a 
          href="https://smartapi.angelbroking.com/" 
          target="_blank" 
          rel="noopener noreferrer"
          className="text-blue-400 hover:underline inline-flex items-center gap-1"
        >
          Register for SmartAPI <ExternalLink className="h-3 w-3" />
        </a>
      </div>
    </div>
  );
}
