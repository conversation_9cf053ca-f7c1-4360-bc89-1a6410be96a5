// News summarization service using Gemini AI

import { geminiClient } from '@/lib/gemini';
import { NewsArticle, NewsSummary, NewsError } from './types';

class NewsSummarizationService {
  private readonly maxRetries = 1; // Reduced retries for faster failure
  private readonly retryDelay = 500; // Reduced delay

  /**
   * Summarize a single news article
   */
  async summarizeArticle(article: NewsArticle): Promise<NewsSummary> {
    try {
      const prompt = this.buildSummarizationPrompt(article);

      const response = await this.callGeminiWithRetry(prompt);

      return this.parseSummaryResponse(response, article);
    } catch (error) {
      // Check if it's an AI service unavailable error
      if (error instanceof Error && error.message === 'AI_SERVICE_UNAVAILABLE') {
        console.warn('AI summarization unavailable, using fallback');
      } else {
        console.error('Error summarizing article:', error);
      }

      // Return fallback summary
      return this.createFallbackSummary(article);
    }
  }

  /**
   * Summarize multiple articles in batch
   */
  async summarizeArticles(articles: NewsArticle[]): Promise<NewsSummary[]> {
    const summaries: NewsSummary[] = [];
    
    // Process in batches to avoid rate limits
    const batchSize = 5;
    
    for (let i = 0; i < articles.length; i += batchSize) {
      const batch = articles.slice(i, i + batchSize);
      
      const batchPromises = batch.map(article => 
        this.summarizeArticle(article).catch(error => {
          console.error(`Failed to summarize article ${article.id}:`, error);
          return this.createFallbackSummary(article);
        })
      );
      
      const batchResults = await Promise.all(batchPromises);
      summaries.push(...batchResults);
      
      // Add delay between batches
      if (i + batchSize < articles.length) {
        await this.delay(500);
      }
    }
    
    return summaries;
  }

  /**
   * Generate market insights from multiple articles
   */
  async generateMarketInsights(articles: NewsArticle[]): Promise<string> {
    try {
      const prompt = this.buildMarketInsightsPrompt(articles);

      const response = await this.callGeminiWithRetry(prompt);

      return response;
    } catch (error) {
      if (error instanceof Error && error.message === 'AI_SERVICE_UNAVAILABLE') {
        return `## Market Overview

**AI insights are temporarily unavailable** due to high demand. However, based on the current news articles:

- **Market Activity**: Multiple financial developments are occurring across different sectors
- **Key Focus Areas**: Monitor the latest headlines above for real-time market movements
- **Recommendation**: Check back in a few minutes for AI-powered analysis

*The news articles above provide the latest financial developments and market updates.*`;
      }
      console.error('Error generating market insights:', error);
      return `## Market Insights Unavailable

**AI analysis is currently experiencing issues.** Here's what we know:

- **Error**: ${error instanceof Error ? error.message : 'Unknown error'}
- **Impact**: Automated market analysis temporarily unavailable
- **Alternative**: Review the news articles above for manual analysis
- **Timeline**: Service restoration in progress

*Our AI-powered insights will return shortly. Thank you for your patience.*`;
    }
  }

  /**
   * Extract key themes from news articles
   */
  async extractKeyThemes(articles: NewsArticle[]): Promise<string[]> {
    try {
      const prompt = this.buildThemeExtractionPrompt(articles);

      const response = await this.callGeminiWithRetry(prompt);

      // Parse themes from response
      const themes = response
        .split('\n')
        .filter(line => line.trim().startsWith('-') || line.trim().startsWith('•'))
        .map(line => line.replace(/^[-•]\s*/, '').trim())
        .filter(theme => theme.length > 0);

      return themes.slice(0, 10); // Limit to top 10 themes
    } catch (error) {
      if (error instanceof Error && error.message === 'AI_SERVICE_UNAVAILABLE') {
        // Generate themes from article titles when AI is unavailable
        return this.extractThemesFromTitles(articles);
      }
      console.error('Error extracting themes:', error);
      return ['Market Volatility', 'Economic Policy', 'Corporate Earnings'];
    }
  }

  /**
   * Fallback method to extract themes from article titles
   */
  private extractThemesFromTitles(articles: NewsArticle[]): string[] {
    const commonFinancialTerms = [
      'Market Volatility', 'Interest Rates', 'Inflation', 'Economic Policy',
      'Corporate Earnings', 'Stock Market', 'Banking Sector', 'Cryptocurrency',
      'Investment', 'GDP Growth', 'Federal Reserve', 'RBI Policy'
    ];

    const titleText = articles.map(a => a.title).join(' ').toLowerCase();

    return commonFinancialTerms.filter(term =>
      titleText.includes(term.toLowerCase())
    ).slice(0, 6);
  }

  /**
   * Build summarization prompt for Gemini
   */
  private buildSummarizationPrompt(article: NewsArticle): string {
    return `
As a financial news analyst, please analyze this news article and provide a structured summary:

**Article Title:** ${article.title}
**Description:** ${article.description}
**Source:** ${article.source.name}
**Category:** ${article.category}

Please provide:
1. A concise 2-3 sentence summary in plain English
2. 3-5 key points (bullet format)
3. Sentiment analysis (positive/negative/neutral)
4. Relevance score for financial markets (0.0-1.0)
5. Relevant tags/keywords (max 5)

Format your response as JSON:
{
  "summary": "Plain English summary here",
  "keyPoints": ["Point 1", "Point 2", "Point 3"],
  "sentiment": "positive|negative|neutral",
  "relevanceScore": 0.8,
  "tags": ["tag1", "tag2", "tag3"]
}

Focus on financial implications and market impact. Avoid jargon and explain complex concepts simply.
`;
  }

  /**
   * Build market insights prompt
   */
  private buildMarketInsightsPrompt(articles: NewsArticle[]): string {
    const articleSummaries = articles.slice(0, 10).map(article => 
      `- ${article.title} (${article.source.name}): ${article.description}`
    ).join('\n');

    return `
As a financial market analyst, analyze these recent news articles and provide key market insights:

**Recent News:**
${articleSummaries}

Please provide:
1. Overall market sentiment and trends
2. Key themes and patterns
3. Potential market implications
4. Important developments to watch

Write in a clear, professional tone suitable for retail investors. Focus on actionable insights and avoid excessive technical jargon.

Limit response to 300 words.
`;
  }

  /**
   * Build theme extraction prompt
   */
  private buildThemeExtractionPrompt(articles: NewsArticle[]): string {
    const titles = articles.slice(0, 20).map(article => article.title).join('\n- ');

    return `
Analyze these financial news headlines and extract the main themes and topics:

Headlines:
- ${titles}

Please identify the top recurring themes, trends, and topics. List them as bullet points, focusing on:
- Market sectors and industries
- Economic indicators and policies
- Geographic regions
- Investment themes
- Regulatory changes

Format as simple bullet points with clear, concise theme names.
`;
  }

  /**
   * Call Gemini API with retry logic and better error handling
   */
  private async callGeminiWithRetry(prompt: string): Promise<string> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await geminiClient.generateContent([{
          role: 'user',
          content: prompt
        }], {
          userBehavior: { focusArea: 'financial_analysis' },
          financialProfile: { experienceLevel: 'mixed' }
        });

        return response.content;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        // Check for specific error types
        const errorMessage = lastError.message.toLowerCase();

        // Don't retry on rate limit or service unavailable - fail fast
        if (errorMessage.includes('429') ||
            errorMessage.includes('too many requests') ||
            errorMessage.includes('503') ||
            errorMessage.includes('service unavailable') ||
            errorMessage.includes('quota exceeded')) {
          console.warn(`Gemini API ${errorMessage.includes('429') ? 'rate limited' : 'unavailable'}, skipping AI summarization`);
          throw new Error('AI_SERVICE_UNAVAILABLE');
        }

        // Only retry on other types of errors
        if (attempt < this.maxRetries) {
          await this.delay(this.retryDelay * attempt);
        }
      }
    }

    throw lastError || new Error('Failed to get response from Gemini');
  }

  /**
   * Parse Gemini response for summary
   */
  private parseSummaryResponse(response: string, article: NewsArticle): NewsSummary {
    try {
      // Try to parse JSON response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        
        return {
          originalTitle: article.title,
          summary: parsed.summary || article.description,
          keyPoints: Array.isArray(parsed.keyPoints) ? parsed.keyPoints : [],
          sentiment: ['positive', 'negative', 'neutral'].includes(parsed.sentiment) 
            ? parsed.sentiment : 'neutral',
          relevanceScore: typeof parsed.relevanceScore === 'number' 
            ? Math.max(0, Math.min(1, parsed.relevanceScore)) : 0.5,
          tags: Array.isArray(parsed.tags) ? parsed.tags.slice(0, 5) : []
        };
      }
    } catch (error) {
      console.error('Error parsing summary response:', error);
    }
    
    // Fallback parsing
    return this.createFallbackSummary(article);
  }

  /**
   * Create fallback summary when AI fails
   */
  private createFallbackSummary(article: NewsArticle): NewsSummary {
    return {
      originalTitle: article.title,
      summary: article.description,
      keyPoints: [article.title],
      sentiment: 'neutral',
      relevanceScore: article.relevanceScore || 0.5,
      tags: [article.category.replace('-', ' ')]
    };
  }

  /**
   * Utility function for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export const newsSummarizationService = new NewsSummarizationService();
