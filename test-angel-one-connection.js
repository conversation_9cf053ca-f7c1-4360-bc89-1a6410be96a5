// Test script to verify Angel One API connection
// Run this with: node test-angel-one-connection.js

const https = require('https');

// Replace these with your actual credentials
const API_KEY = 'your_actual_api_key_here';
const CLIENT_CODE = 'your_actual_client_code_here';
const PASSWORD = 'your_actual_trading_password_here';

async function testAngelOneConnection() {
  console.log('🔄 Testing Angel One API connection...\n');
  
  // Check if credentials are provided
  if (API_KEY === 'your_actual_api_key_here' || 
      CLIENT_CODE === 'your_actual_client_code_here' || 
      PASSWORD === 'your_actual_trading_password_here') {
    console.log('❌ Please update the credentials in this file first!');
    console.log('📝 Edit test-angel-one-connection.js and add your real credentials');
    return;
  }

  const loginData = JSON.stringify({
    clientcode: CLIENT_CODE,
    password: PASSWORD
  });

  const options = {
    hostname: 'apiconnect.angelbroking.com',
    port: 443,
    path: '/rest/auth/angelbroking/user/v1/loginByPassword',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-UserType': 'USER',
      'X-SourceID': 'WEB',
      'X-ClientLocalIP': '127.0.0.1',
      'X-ClientPublicIP': '127.0.0.1',
      'X-MACAddress': '00:00:00:00:00:00',
      'X-PrivateKey': API_KEY,
      'Content-Length': Buffer.byteLength(loginData)
    }
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          console.log('📡 API Response:');
          console.log('Status:', response.status);
          console.log('Message:', response.message);
          
          if (response.status === true) {
            console.log('\n✅ SUCCESS! Angel One API connection working!');
            console.log('🎉 You can now use real API mode in the portfolio');
            console.log('📊 Your actual portfolio data will be loaded');
          } else {
            console.log('\n❌ Connection failed. Common issues:');
            
            if (response.message?.toLowerCase().includes('totp')) {
              console.log('🔒 TOTP/2FA Issue:');
              console.log('   - Disable 2FA in Angel One account settings');
              console.log('   - Go to Profile → Security → Turn off 2FA');
            } else if (response.message?.toLowerCase().includes('invalid')) {
              console.log('🔑 Credential Issue:');
              console.log('   - Check API key is correct');
              console.log('   - Verify client code (trading account ID)');
              console.log('   - Confirm password is trading password');
            } else {
              console.log('❓ Other Issue:', response.message);
              console.log('   - Contact Angel One support: 040-47 47 47 47');
            }
          }
          
          resolve(response);
        } catch (error) {
          console.log('❌ Error parsing response:', error.message);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log('❌ Network error:', error.message);
      console.log('🌐 Check your internet connection');
      reject(error);
    });

    req.write(loginData);
    req.end();
  });
}

// Run the test
testAngelOneConnection()
  .then(() => {
    console.log('\n📋 Next Steps:');
    console.log('1. If successful: Toggle to "Real API" mode in portfolio');
    console.log('2. If failed: Follow the suggestions above');
    console.log('3. Need help: Contact Angel One support');
  })
  .catch(() => {
    console.log('\n🆘 Need Help?');
    console.log('📞 Angel One Support: 040-47 47 47 47');
    console.log('📧 Email: <EMAIL>');
    console.log('🌐 SmartAPI: https://smartapi.angelbroking.com/');
  });
