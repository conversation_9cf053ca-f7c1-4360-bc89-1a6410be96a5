// News service types and interfaces

export interface NewsArticle {
  id: string;
  title: string;
  description: string;
  content?: string;
  url: string;
  urlToImage?: string;
  publishedAt: string;
  source: {
    id: string | null;
    name: string;
  };
  category: NewsCategory;
  relevanceScore?: number;
  summary?: string;
  sentiment?: 'positive' | 'negative' | 'neutral';
  tags?: string[];
}

export type NewsCategory = 
  | 'india-specific'
  | 'global-markets' 
  | 'crypto-web3'
  | 'stocks-companies'
  | 'macro-policy'
  | 'general';

export interface NewsQuery {
  query?: string;
  category?: NewsCategory;
  sortBy?: 'publishedAt' | 'relevancy' | 'popularity';
  from?: string; // ISO date string
  to?: string; // ISO date string
  language?: string;
  pageSize?: number;
  page?: number;
}

export interface NewsResponse {
  status: 'ok' | 'error';
  totalResults: number;
  articles: NewsArticle[];
  message?: string;
}

export interface NewsAPIResponse {
  status: string;
  totalResults: number;
  articles: Array<{
    source: {
      id: string | null;
      name: string;
    };
    author: string | null;
    title: string;
    description: string;
    url: string;
    urlToImage: string | null;
    publishedAt: string;
    content: string | null;
  }>;
}

export interface CategoryConfig {
  name: string;
  icon: string;
  keywords: string[];
  sources?: string[];
  description: string;
}

export interface SearchFilters {
  category?: NewsCategory;
  dateRange?: {
    from: Date;
    to: Date;
  };
  sources?: string[];
  sentiment?: 'positive' | 'negative' | 'neutral';
  minRelevanceScore?: number;
}

export interface NewsSummary {
  originalTitle: string;
  summary: string;
  keyPoints: string[];
  sentiment: 'positive' | 'negative' | 'neutral';
  relevanceScore: number;
  tags: string[];
}

export interface NewsError {
  code: string;
  message: string;
  details?: any;
}

// Category configurations
export const NEWS_CATEGORIES: Record<NewsCategory, CategoryConfig> = {
  'india-specific': {
    name: '🇮🇳 India-Specific',
    icon: '🇮🇳',
    keywords: ['RBI', 'Reserve Bank of India', 'Sensex', 'Nifty', 'BSE', 'NSE', 'Indian banking', 'Indian startups', 'rupee', 'GST', 'Indian economy', 'SEBI', 'Indian stock market'],
    sources: ['economic-times', 'business-standard', 'livemint'],
    description: 'News specific to Indian markets, RBI policies, Sensex, banking, and startups'
  },
  'global-markets': {
    name: '🌍 Global Markets',
    icon: '🌍',
    keywords: ['Federal Reserve', 'Fed policy', 'global equities', 'geopolitics', 'international markets', 'world economy', 'global trade', 'currency markets', 'international finance'],
    sources: ['reuters', 'bloomberg', 'financial-times', 'wall-street-journal'],
    description: 'International markets, Fed policies, global economic trends, and geopolitical impacts'
  },
  'crypto-web3': {
    name: '💰 Crypto & Web3',
    icon: '💰',
    keywords: ['Bitcoin', 'Ethereum', 'cryptocurrency', 'blockchain', 'DeFi', 'NFT', 'Web3', 'crypto regulation', 'digital assets', 'altcoins'],
    sources: ['coindesk', 'cointelegraph'],
    description: 'Cryptocurrency markets, blockchain technology, DeFi, and Web3 developments'
  },
  'stocks-companies': {
    name: '📊 Stock/Company-Specific',
    icon: '📊',
    keywords: ['earnings', 'quarterly results', 'stock analysis', 'company news', 'IPO', 'mergers', 'acquisitions', 'corporate governance'],
    sources: ['reuters', 'bloomberg', 'cnbc'],
    description: 'Individual company news, earnings reports, stock analysis, and corporate developments'
  },
  'macro-policy': {
    name: '🏦 Macro & Policy',
    icon: '🏦',
    keywords: ['inflation', 'interest rates', 'fiscal policy', 'monetary policy', 'economic indicators', 'GDP', 'unemployment', 'central bank'],
    sources: ['reuters', 'bloomberg', 'financial-times'],
    description: 'Macroeconomic trends, policy changes, inflation, interest rates, and economic indicators'
  },
  'general': {
    name: '📰 General Financial',
    icon: '📰',
    keywords: ['finance', 'economy', 'business', 'market', 'investment'],
    sources: [],
    description: 'General financial and business news'
  }
};

// API Configuration
export const NEWS_API_CONFIG = {
  BASE_URL: 'https://newsapi.org/v2',
  ENDPOINTS: {
    EVERYTHING: '/everything',
    TOP_HEADLINES: '/top-headlines',
    SOURCES: '/sources'
  },
  DEFAULT_PARAMS: {
    language: 'en',
    pageSize: 20,
    sortBy: 'publishedAt' as const
  }
};
