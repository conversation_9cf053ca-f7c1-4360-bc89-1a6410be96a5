// Technical Analysis Engine for Market Insights

import { TechnicalIndicators, TimeFrameAnalysis, MarketData } from './types';

export class TechnicalAnalysisEngine {
  
  // Calculate RSI (Relative Strength Index)
  static calculateRSI(prices: number[], period: number = 14): number {
    if (prices.length < period + 1) return 50; // Default neutral RSI
    
    let gains = 0;
    let losses = 0;
    
    // Calculate initial average gain and loss
    for (let i = 1; i <= period; i++) {
      const change = prices[i] - prices[i - 1];
      if (change > 0) gains += change;
      else losses += Math.abs(change);
    }
    
    let avgGain = gains / period;
    let avgLoss = losses / period;
    
    // Calculate RSI using <PERSON>'s smoothing
    for (let i = period + 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      const gain = change > 0 ? change : 0;
      const loss = change < 0 ? Math.abs(change) : 0;
      
      avgGain = (avgGain * (period - 1) + gain) / period;
      avgLoss = (avgLoss * (period - 1) + loss) / period;
    }
    
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
  }
  
  // Calculate MACD (Moving Average Convergence Divergence)
  static calculateMACD(prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9) {
    const emaFast = this.calculateEMA(prices, fastPeriod);
    const emaSlow = this.calculateEMA(prices, slowPeriod);
    
    const macdLine = emaFast.map((fast, i) => fast - emaSlow[i]);
    const signalLine = this.calculateEMA(macdLine, signalPeriod);
    const histogram = macdLine.map((macd, i) => macd - signalLine[i]);
    
    const latest = macdLine.length - 1;
    return {
      macd: macdLine[latest] || 0,
      signal: signalLine[latest] || 0,
      histogram: histogram[latest] || 0
    };
  }
  
  // Calculate Exponential Moving Average
  static calculateEMA(prices: number[], period: number): number[] {
    const ema = [];
    const multiplier = 2 / (period + 1);
    
    // Start with SMA for first value
    let sum = 0;
    for (let i = 0; i < period && i < prices.length; i++) {
      sum += prices[i];
    }
    ema[period - 1] = sum / period;
    
    // Calculate EMA for remaining values
    for (let i = period; i < prices.length; i++) {
      ema[i] = (prices[i] * multiplier) + (ema[i - 1] * (1 - multiplier));
    }
    
    return ema;
  }
  
  // Calculate Simple Moving Average
  static calculateSMA(prices: number[], period: number): number {
    if (prices.length < period) return prices[prices.length - 1] || 0;
    
    const recentPrices = prices.slice(-period);
    return recentPrices.reduce((sum, price) => sum + price, 0) / period;
  }
  
  // Calculate Bollinger Bands
  static calculateBollingerBands(prices: number[], period: number = 20, stdDev: number = 2) {
    const sma = this.calculateSMA(prices, period);
    const recentPrices = prices.slice(-period);
    
    // Calculate standard deviation
    const variance = recentPrices.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;
    const standardDeviation = Math.sqrt(variance);
    
    return {
      upper: sma + (standardDeviation * stdDev),
      middle: sma,
      lower: sma - (standardDeviation * stdDev)
    };
  }
  
  // Calculate Volatility
  static calculateVolatility(prices: number[], period: number = 20): number {
    if (prices.length < 2) return 0;
    
    const returns = [];
    for (let i = 1; i < prices.length; i++) {
      returns.push(Math.log(prices[i] / prices[i - 1]));
    }
    
    const recentReturns = returns.slice(-period);
    const mean = recentReturns.reduce((sum, ret) => sum + ret, 0) / recentReturns.length;
    const variance = recentReturns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / recentReturns.length;
    
    return Math.sqrt(variance * 252) * 100; // Annualized volatility as percentage
  }
  
  // Determine Momentum
  static determineMomentum(rsi: number, macd: any, priceChange: number): 'bullish' | 'bearish' | 'neutral' {
    let bullishSignals = 0;
    let bearishSignals = 0;
    
    // RSI signals
    if (rsi > 70) bearishSignals++;
    else if (rsi < 30) bullishSignals++;
    else if (rsi > 50) bullishSignals += 0.5;
    else bearishSignals += 0.5;
    
    // MACD signals
    if (macd.macd > macd.signal && macd.histogram > 0) bullishSignals++;
    else if (macd.macd < macd.signal && macd.histogram < 0) bearishSignals++;
    
    // Price momentum
    if (priceChange > 2) bullishSignals++;
    else if (priceChange < -2) bearishSignals++;
    
    if (bullishSignals > bearishSignals) return 'bullish';
    if (bearishSignals > bullishSignals) return 'bearish';
    return 'neutral';
  }
  
  // Generate Technical Signal
  static generateTechnicalSignal(indicators: TechnicalIndicators, priceChange: number): 'buy' | 'sell' | 'hold' {
    let score = 0;
    
    // RSI analysis
    if (indicators.rsi < 30) score += 2; // Oversold - buy signal
    else if (indicators.rsi > 70) score -= 2; // Overbought - sell signal
    else if (indicators.rsi > 50) score += 1; // Above midline - bullish
    else score -= 1; // Below midline - bearish
    
    // MACD analysis
    if (indicators.macd.macd > indicators.macd.signal) score += 1;
    else score -= 1;
    
    if (indicators.macd.histogram > 0) score += 1;
    else score -= 1;
    
    // Price vs Moving Averages
    if (indicators.sma20 > indicators.sma50) score += 1; // Uptrend
    else score -= 1; // Downtrend
    
    // Bollinger Bands
    const currentPrice = indicators.sma20; // Approximation
    if (currentPrice < indicators.bollinger.lower) score += 1; // Oversold
    else if (currentPrice > indicators.bollinger.upper) score -= 1; // Overbought
    
    // Final decision
    if (score >= 3) return 'buy';
    if (score <= -3) return 'sell';
    return 'hold';
  }
  
  // Analyze Time Frame
  static analyzeTimeFrame(
    marketData: MarketData,
    historicalPrices: number[],
    timeframe: '1H' | '1D' | '1W'
  ): TimeFrameAnalysis {
    const indicators = this.calculateAllIndicators(historicalPrices);
    const momentum = this.determineMomentum(indicators.rsi, indicators.macd, marketData.changePercent);
    const signal = this.generateTechnicalSignal(indicators, marketData.changePercent);
    
    // Determine momentum strength
    let momentumStrength: TimeFrameAnalysis['momentum'];
    if (Math.abs(marketData.changePercent) > 5) {
      momentumStrength = marketData.changePercent > 0 ? 'strong_bullish' : 'strong_bearish';
    } else if (Math.abs(marketData.changePercent) > 2) {
      momentumStrength = marketData.changePercent > 0 ? 'bullish' : 'bearish';
    } else {
      momentumStrength = 'neutral';
    }
    
    // Calculate support and resistance
    const recentPrices = historicalPrices.slice(-20);
    const support = Math.min(...recentPrices);
    const resistance = Math.max(...recentPrices);
    
    return {
      timeframe,
      priceMovement: marketData.changePercent,
      volumeChange: marketData.volumeChange || 0,
      volatilitySpike: indicators.volatility > 30,
      technicalSignal: signal,
      momentum: momentumStrength,
      keyLevels: {
        support,
        resistance
      }
    };
  }
  
  // Calculate All Technical Indicators
  static calculateAllIndicators(prices: number[]): TechnicalIndicators {
    const rsi = this.calculateRSI(prices);
    const macd = this.calculateMACD(prices);
    const sma20 = this.calculateSMA(prices, 20);
    const sma50 = this.calculateSMA(prices, 50);
    const bollinger = this.calculateBollingerBands(prices);
    const volatility = this.calculateVolatility(prices);
    const momentum = this.determineMomentum(rsi, macd, 0);
    
    return {
      rsi,
      macd,
      sma20,
      sma50,
      bollinger,
      volatility,
      momentum
    };
  }
  
  // Detect Reversal Zones
  static detectReversalZones(indicators: TechnicalIndicators): {
    isReversalZone: boolean;
    type: 'bullish_reversal' | 'bearish_reversal' | 'none';
    confidence: number;
  } {
    let reversalScore = 0;
    let type: 'bullish_reversal' | 'bearish_reversal' | 'none' = 'none';
    
    // RSI divergence signals
    if (indicators.rsi < 30) {
      reversalScore += 2;
      type = 'bullish_reversal';
    } else if (indicators.rsi > 70) {
      reversalScore += 2;
      type = 'bearish_reversal';
    }
    
    // MACD crossover
    if (indicators.macd.histogram > 0 && indicators.macd.macd > indicators.macd.signal) {
      if (type === 'bullish_reversal') reversalScore += 1;
    } else if (indicators.macd.histogram < 0 && indicators.macd.macd < indicators.macd.signal) {
      if (type === 'bearish_reversal') reversalScore += 1;
    }
    
    // Bollinger Band squeeze
    const bandWidth = indicators.bollinger.upper - indicators.bollinger.lower;
    const avgPrice = indicators.sma20;
    if (bandWidth / avgPrice < 0.1) { // Tight bands indicate potential breakout
      reversalScore += 1;
    }
    
    return {
      isReversalZone: reversalScore >= 2,
      type,
      confidence: Math.min(reversalScore / 4, 1) * 100
    };
  }
}
