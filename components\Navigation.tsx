"use client";

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Home,
  MessageCircle,
  BarChart3,
  PieChart,
  Brain,
  Mic,
  TrendingUp,
  BookOpen,
  Newspaper,
  Menu,
  X
} from 'lucide-react';

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const pathname = usePathname();

  const navItems = [
    { href: '/', label: 'Home', icon: Home },
    { href: '/chat', label: 'AI Chat', icon: MessageCircle },
    { href: '/dashboard', label: 'Dashboard', icon: BarChart3 },
    { href: '/portfolio', label: 'Portfolio', icon: PieChart },
    { href: '/stock-analysis', label: 'Stock Analysis', icon: TrendingUp },
    { href: '/news', label: 'Financial News', icon: Newspaper },
    { href: '/concept-map', label: 'Knowledge Map', icon: Brain },
    { href: '/insights', label: 'AI Market Insights', icon: BarChart3 },
    { href: '/learning', label: 'Learning', icon: BookOpen },
  ];

  return (
    <nav className="bg-slate-900/95 backdrop-blur-sm border-b border-slate-800 sticky top-0 z-[100]">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          
          {/* Logo */}
          <Link href="/" className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
              <Brain className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl font-bold text-white hidden sm:block">
              FinanceAI
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;
              
              return (
                <Link key={item.href} href={item.href}>
                  <Button
                    variant={isActive ? "secondary" : "ghost"}
                    size="sm"
                    className={`flex items-center gap-2 ${
                      isActive 
                        ? 'bg-slate-800 text-white' 
                        : 'text-slate-300 hover:text-white hover:bg-slate-800'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    {item.label}
                  </Button>
                </Link>
              );
            })}
          </div>

          {/* Status Badge */}
          <div className="hidden md:flex items-center gap-4">
            <Badge variant="outline" className="bg-green-500/10 text-green-300 border-green-500/30">
              AI Online
            </Badge>
          </div>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden text-slate-300"
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden py-4 border-t border-slate-800">
            <div className="space-y-2">
              {navItems.map((item) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;
                
                return (
                  <Link key={item.href} href={item.href} onClick={() => setIsOpen(false)}>
                    <Button
                      variant={isActive ? "secondary" : "ghost"}
                      size="sm"
                      className={`w-full justify-start gap-2 ${
                        isActive 
                          ? 'bg-slate-800 text-white' 
                          : 'text-slate-300 hover:text-white hover:bg-slate-800'
                      }`}
                    >
                      <Icon className="h-4 w-4" />
                      {item.label}
                    </Button>
                  </Link>
                );
              })}
              <div className="pt-2 mt-2 border-t border-slate-800">
                <Badge variant="outline" className="bg-green-500/10 text-green-300 border-green-500/30">
                  AI Online
                </Badge>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}