"use client";

import { useState, useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  TrendingUp,
  TrendingDown,
  Search,
  Brain,
  BarChart3,
  Clock,
  DollarSign,
  Activity,
  AlertTriangle,
  CheckCircle,
  Eye,
  Calendar,
  Zap,
  Target,
  BookOpen,
  RefreshCw,
  ArrowUp,
  ArrowDown,
  Minus,
  Star,
  Globe,
  Volume2,
  Lightbulb,
  GraduationCap
} from 'lucide-react';
import StockChart from '@/components/StockChart';

interface StockData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
  high52Week: number;
  low52Week: number;
  pe: number;
  dividend: number;
  lastUpdate: string;
  market: 'US' | 'IN';
  currency: 'USD' | 'INR';
  exchange: string;
  priceInUSD?: number;
  priceInINR?: number;
  exchangeRate?: number;
}

interface AIInsight {
  type: 'opportunity' | 'risk' | 'neutral';
  confidence: number;
  signal: string;
  reasoning: string;
  timeframe: string;
  actionSuggestion: string;
}

export default function StockAnalysisPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedStock, setSelectedStock] = useState<string>(searchParams.get('ticker') || '');
  const [stockData, setStockData] = useState<StockData | null>(null);
  const [aiInsights, setAiInsights] = useState<AIInsight[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('live');
  const [searchQuery, setSearchQuery] = useState('');
  const [timeframe, setTimeframe] = useState('1D');
  const [isRealTime, setIsRealTime] = useState(false);
  const [currency, setCurrency] = useState<'USD' | 'INR'>('USD');
  const [detailedInsight, setDetailedInsight] = useState<any>(null);
  const [isLoadingDetailed, setIsLoadingDetailed] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Popular stocks for quick access
  const popularUSStocks = [
    { symbol: 'AAPL', name: 'Apple Inc.' },
    { symbol: 'MSFT', name: 'Microsoft Corp.' },
    { symbol: 'GOOGL', name: 'Alphabet Inc.' },
    { symbol: 'AMZN', name: 'Amazon.com Inc.' },
    { symbol: 'TSLA', name: 'Tesla Inc.' },
    { symbol: 'NVDA', name: 'NVIDIA Corp.' },
    { symbol: 'META', name: 'Meta Platforms' },
    { symbol: 'NFLX', name: 'Netflix Inc.' }
  ];

  const popularIndianStocks = [
    { symbol: 'RELIANCE.NS', name: 'Reliance Industries' },
    { symbol: 'TCS.NS', name: 'Tata Consultancy Services' },
    { symbol: 'INFY.NS', name: 'Infosys Limited' },
    { symbol: 'HDFCBANK.NS', name: 'HDFC Bank Limited' },
    { symbol: 'ICICIBANK.NS', name: 'ICICI Bank Limited' },
    { symbol: 'SBIN.NS', name: 'State Bank of India' },
    { symbol: 'ITC.NS', name: 'ITC Limited' },
    { symbol: 'LT.NS', name: 'Larsen & Toubro' }
  ];

  const popularStocks = currency === 'INR' ? popularIndianStocks : popularUSStocks;

  useEffect(() => {
    if (selectedStock) {
      fetchStockData(selectedStock);
      if (isRealTime) {
        startRealTimeUpdates();
      }
    }
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [selectedStock, isRealTime, currency]);

  const fetchStockData = async (ticker: string) => {
    setIsLoading(true);
    try {
      // Fetch live stock data
      const response = await fetch(`/api/liveData/${ticker}?currency=${currency}`);
      if (response.ok) {
        const data = await response.json();
        setStockData(data);
        
        // Fetch AI insights
        await fetchAIInsights(ticker, data);
      }
    } catch (error) {
      console.error('Error fetching stock data:', error);
      // Mock data for development
      setStockData({
        symbol: ticker.toUpperCase(),
        name: `${ticker.toUpperCase()} Corporation`,
        price: 150.25,
        change: 2.45,
        changePercent: 1.66,
        volume: 45678900,
        marketCap: 2450000000000,
        high52Week: 180.50,
        low52Week: 120.30,
        pe: 28.5,
        dividend: 0.88,
        lastUpdate: new Date().toISOString(),
        market: 'US',
        currency: 'USD',
        exchange: 'NASDAQ'
      });
      
      // Mock AI insights
      setAiInsights([
        {
          type: 'opportunity',
          confidence: 75,
          signal: 'Bullish Momentum',
          reasoning: 'Stock is showing strong upward momentum with increasing volume and breaking above key resistance levels.',
          timeframe: 'Short-term (1-2 weeks)',
          actionSuggestion: 'Consider buying on pullbacks to support levels'
        },
        {
          type: 'risk',
          confidence: 60,
          signal: 'Overbought RSI',
          reasoning: 'RSI is above 70, indicating potential overbought conditions. May see short-term correction.',
          timeframe: 'Immediate (1-3 days)',
          actionSuggestion: 'Wait for RSI to cool down before entering'
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchDetailedInsights = async (ticker: string, stockData: any) => {
    setIsLoadingDetailed(true);
    try {
      const response = await fetch('/api/ai/detailedInsights', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ticker,
          stockData,
          market: stockData.market || 'US'
        })
      });

      if (response.ok) {
        const insights = await response.json();
        setDetailedInsight(insights);
      }
    } catch (error) {
      console.error('Error fetching detailed insights:', error);
    } finally {
      setIsLoadingDetailed(false);
    }
  };

  const fetchAIInsights = async (ticker: string, stockData: StockData) => {
    try {
      const response = await fetch('/api/ai/opportunityMap', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ticker, stockData })
      });
      
      if (response.ok) {
        const insights = await response.json();
        setAiInsights(insights);
      }
    } catch (error) {
      console.error('Error fetching AI insights:', error);
    }
  };

  const startRealTimeUpdates = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    intervalRef.current = setInterval(() => {
      if (selectedStock) {
        fetchStockData(selectedStock);
      }
    }, 30000); // Update every 30 seconds
  };

  const handleStockSelect = (ticker: string) => {
    setSelectedStock(ticker);
    router.push(`/stock-analysis?ticker=${ticker}`);
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      handleStockSelect(searchQuery.trim().toUpperCase());
      setSearchQuery('');
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1e12) return `$${(num / 1e12).toFixed(2)}T`;
    if (num >= 1e9) return `$${(num / 1e9).toFixed(2)}B`;
    if (num >= 1e6) return `$${(num / 1e6).toFixed(2)}M`;
    if (num >= 1e3) return `$${(num / 1e3).toFixed(2)}K`;
    return `$${num.toFixed(2)}`;
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1e9) return `${(volume / 1e9).toFixed(2)}B`;
    if (volume >= 1e6) return `${(volume / 1e6).toFixed(2)}M`;
    if (volume >= 1e3) return `${(volume / 1e3).toFixed(2)}K`;
    return volume.toString();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-white flex items-center justify-center gap-3">
              <BarChart3 className="h-8 w-8 text-blue-400" />
              AI Stock Analysis Engine
            </h1>
            <p className="text-slate-300 max-w-3xl mx-auto text-lg">
              Real-time stock analysis powered by AI. Get live data, opportunity signals, and educational insights.
            </p>

            {/* Currency Toggle */}
            <div className="flex justify-center mt-4">
              <div className="bg-slate-800/50 rounded-lg p-1 flex gap-1">
                <button
                  onClick={() => setCurrency('USD')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                    currency === 'USD'
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'text-slate-300 hover:text-white hover:bg-slate-700'
                  }`}
                >
                  🇺🇸 US Market (USD)
                </button>
                <button
                  onClick={() => setCurrency('INR')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                    currency === 'INR'
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'text-slate-300 hover:text-white hover:bg-slate-700'
                  }`}
                >
                  🇮🇳 Indian Market (INR)
                </button>
              </div>
            </div>
          </div>

          {/* Stock Search */}
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Search className="h-5 w-5 text-blue-400" />
                Stock Search
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="Enter stock ticker (e.g., AAPL, MSFT, GOOGL)"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                />
                <Button onClick={handleSearch} className="bg-blue-600 hover:bg-blue-700">
                  <Search className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Popular Stocks */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-slate-300">Popular Stocks</h4>
                <div className="flex flex-wrap gap-2">
                  {popularStocks.map((stock) => (
                    <Button
                      key={stock.symbol}
                      variant="outline"
                      size="sm"
                      onClick={() => handleStockSelect(stock.symbol)}
                      className={`border-slate-600 text-slate-300 hover:bg-slate-700 ${
                        selectedStock === stock.symbol ? 'bg-blue-600 text-white border-blue-500' : ''
                      }`}
                    >
                      {stock.symbol}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Main Analysis Interface */}
          {selectedStock && stockData && (
            <div className="space-y-6">
              {/* Stock Header */}
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div>
                        <h2 className="text-2xl font-bold text-white">{stockData.symbol}</h2>
                        <p className="text-slate-400">{stockData.name}</p>
                      </div>
                      <Badge variant="outline" className="border-blue-500/30 text-blue-300">
                        Live Data
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsRealTime(!isRealTime)}
                        className={`border-slate-600 ${
                          isRealTime ? 'bg-green-600 text-white' : 'text-slate-300'
                        }`}
                      >
                        <RefreshCw className={`h-4 w-4 mr-2 ${isRealTime ? 'animate-spin' : ''}`} />
                        {isRealTime ? 'Live' : 'Manual'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fetchStockData(selectedStock)}
                        disabled={isLoading}
                        className="border-slate-600 text-slate-300"
                      >
                        <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                      </Button>
                    </div>
                  </div>

                  {/* Price Information */}
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">
                        {stockData.currency === 'INR' ? '₹' : '$'}{stockData.price.toFixed(2)}
                      </div>
                      <div className="text-sm text-slate-400">
                        Current Price ({stockData.currency})
                        {stockData.exchangeRate && (
                          <div className="text-xs text-slate-500 mt-1">
                            {currency === 'USD' && stockData.priceInINR ? `₹${stockData.priceInINR.toFixed(2)}` :
                             currency === 'INR' && stockData.priceInUSD ? `$${stockData.priceInUSD.toFixed(2)}` : ''}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className={`text-xl font-bold flex items-center justify-center gap-1 ${
                        stockData.change >= 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {stockData.change >= 0 ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                        ${Math.abs(stockData.change).toFixed(2)}
                      </div>
                      <div className="text-sm text-slate-400">Change</div>
                    </div>
                    <div className="text-center">
                      <div className={`text-xl font-bold ${
                        stockData.changePercent >= 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {stockData.changePercent >= 0 ? '+' : ''}{stockData.changePercent.toFixed(2)}%
                      </div>
                      <div className="text-sm text-slate-400">% Change</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">{formatVolume(stockData.volume)}</div>
                      <div className="text-sm text-slate-400">Volume</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">{formatNumber(stockData.marketCap)}</div>
                      <div className="text-sm text-slate-400">Market Cap</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">{stockData.pe.toFixed(1)}</div>
                      <div className="text-sm text-slate-400">P/E Ratio</div>
                    </div>
                  </div>

                  {/* 52-Week Range */}
                  <div className="mt-4">
                    <div className="flex items-center justify-between text-sm text-slate-400 mb-2">
                      <span>52-Week Range</span>
                      <span>${stockData.low52Week.toFixed(2)} - ${stockData.high52Week.toFixed(2)}</span>
                    </div>
                    <div className="relative">
                      <div className="w-full bg-slate-700 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-2 rounded-full"
                          style={{
                            width: `${((stockData.price - stockData.low52Week) / (stockData.high52Week - stockData.low52Week)) * 100}%`
                          }}
                        />
                      </div>
                      <div
                        className="absolute top-0 w-1 h-2 bg-white rounded"
                        style={{
                          left: `${((stockData.price - stockData.low52Week) / (stockData.high52Week - stockData.low52Week)) * 100}%`
                        }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Tabbed Analysis Interface */}
              <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                <div className="flex justify-center">
                  <TabsList className="bg-slate-800/50 border-slate-700 p-1">
                    <TabsTrigger value="live" className="flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      Live View
                    </TabsTrigger>
                    <TabsTrigger value="ai-insights" className="flex items-center gap-2">
                      <Brain className="h-4 w-4" />
                      AI Insights
                    </TabsTrigger>
                    <TabsTrigger value="historical" className="flex items-center gap-2">
                      <BarChart3 className="h-4 w-4" />
                      Historical Data
                    </TabsTrigger>
                    <TabsTrigger value="learning" className="flex items-center gap-2">
                      <BookOpen className="h-4 w-4" />
                      Learning Hub
                    </TabsTrigger>
                  </TabsList>
                </div>

                {/* Live View Tab */}
                <TabsContent value="live" className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Live Chart */}
                    <div className="lg:col-span-2">
                      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-white flex items-center gap-2">
                              <BarChart3 className="h-5 w-5 text-blue-400" />
                              Live Chart
                            </CardTitle>
                            <div className="flex gap-2">
                              {['1D', '5D', '1M', '3M', '1Y'].map((period) => (
                                <Button
                                  key={period}
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setTimeframe(period)}
                                  className={`border-slate-600 text-xs ${
                                    timeframe === period ? 'bg-blue-600 text-white' : 'text-slate-300'
                                  }`}
                                >
                                  {period}
                                </Button>
                              ))}
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          {/* Interactive Stock Chart */}
                          <StockChart
                            ticker={stockData.symbol}
                            timeframe={timeframe}
                            height={320}
                          />
                        </CardContent>
                      </Card>
                    </div>

                    {/* Market Depth & Quick Stats */}
                    <div className="space-y-6">
                      {/* Quick Stats */}
                      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                        <CardHeader>
                          <CardTitle className="text-white text-lg">Quick Stats</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="space-y-3">
                            <div className="flex justify-between">
                              <span className="text-slate-400">Open</span>
                              <span className="text-white">${(stockData.price - stockData.change + 0.5).toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-400">High</span>
                              <span className="text-white">${(stockData.price + 2.3).toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-400">Low</span>
                              <span className="text-white">${(stockData.price - 1.8).toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-400">Avg Volume</span>
                              <span className="text-white">{formatVolume(stockData.volume * 0.85)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-400">Dividend</span>
                              <span className="text-white">${(stockData.dividend || 0).toFixed(2)}</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* Technical Indicators */}
                      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                        <CardHeader>
                          <CardTitle className="text-white text-lg">Technical Indicators</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="space-y-3">
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-slate-400">RSI (14)</span>
                                <span className="text-yellow-400">68.5</span>
                              </div>
                              <Progress value={68.5} className="h-2" />
                              <div className="text-xs text-slate-500 mt-1">Approaching overbought</div>
                            </div>
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-slate-400">MACD</span>
                                <span className="text-green-400">Bullish</span>
                              </div>
                              <div className="text-xs text-slate-500">Signal line crossover</div>
                            </div>
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-slate-400">Volume Trend</span>
                                <span className="text-blue-400">Above Average</span>
                              </div>
                              <div className="text-xs text-slate-500">+23% vs 20-day avg</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* Market Sentiment */}
                      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                        <CardHeader>
                          <CardTitle className="text-white text-lg">Market Sentiment</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-slate-400">Analyst Rating</span>
                            <Badge variant="outline" className="border-green-500/30 text-green-300">
                              Buy
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-slate-400">Price Target</span>
                            <span className="text-green-400">${(stockData.price * 1.15).toFixed(2)}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-slate-400">Social Sentiment</span>
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-4 w-4 text-green-400" />
                              <span className="text-green-400">Positive</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </TabsContent>

                {/* AI Insights Tab */}
                <TabsContent value="ai-insights" className="space-y-6">
                  <div className="text-center space-y-2 mb-6">
                    <h2 className="text-2xl font-bold text-white">AI-Powered Market Analysis</h2>
                    <p className="text-slate-300 max-w-2xl mx-auto">
                      Advanced AI analysis of market conditions, opportunities, and risks for {stockData.symbol}
                    </p>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* AI Insights Cards */}
                    {aiInsights.map((insight, index) => (
                      <Card key={index} className={`bg-slate-800/50 border-slate-700 backdrop-blur-sm ${
                        insight.type === 'opportunity' ? 'border-l-4 border-l-green-500' :
                        insight.type === 'risk' ? 'border-l-4 border-l-red-500' :
                        'border-l-4 border-l-yellow-500'
                      }`}>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <CardTitle className={`text-lg flex items-center gap-2 ${
                              insight.type === 'opportunity' ? 'text-green-400' :
                              insight.type === 'risk' ? 'text-red-400' :
                              'text-yellow-400'
                            }`}>
                              {insight.type === 'opportunity' ? <TrendingUp className="h-5 w-5" /> :
                               insight.type === 'risk' ? <AlertTriangle className="h-5 w-5" /> :
                               <Eye className="h-5 w-5" />}
                              {insight.signal}
                            </CardTitle>
                            <Badge variant="outline" className={`${
                              insight.confidence >= 80 ? 'border-green-500/30 text-green-300' :
                              insight.confidence >= 60 ? 'border-yellow-500/30 text-yellow-300' :
                              'border-red-500/30 text-red-300'
                            }`}>
                              {insight.confidence}% Confidence
                            </Badge>
                          </div>
                          <CardDescription className="text-slate-400">
                            {insight.timeframe}
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <h4 className="text-sm font-medium text-slate-300 mb-2">AI Analysis</h4>
                            <p className="text-slate-400 text-sm">{insight.reasoning}</p>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-slate-300 mb-2">Suggested Action</h4>
                            <p className="text-white text-sm font-medium">{insight.actionSuggestion}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-slate-600 text-slate-300"
                              onClick={() => router.push(`/chat?query=${encodeURIComponent(`Explain more about ${insight.signal} for ${stockData.symbol}`)}`)}
                            >
                              <Brain className="h-3 w-3 mr-1" />
                              Ask AI More
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-slate-600 text-slate-300"
                              onClick={() => setActiveTab('learning')}
                            >
                              <BookOpen className="h-3 w-3 mr-1" />
                              Learn More
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {/* Overall AI Assessment */}
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center gap-2">
                        <Brain className="h-5 w-5 text-purple-400" />
                        Overall AI Assessment
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                          <div className="text-2xl font-bold text-green-400">75%</div>
                          <div className="text-sm text-slate-400">Buy Confidence</div>
                          <div className="text-xs text-slate-500 mt-1">Based on technical & fundamental analysis</div>
                        </div>
                        <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                          <div className="text-2xl font-bold text-yellow-400">Medium</div>
                          <div className="text-sm text-slate-400">Risk Level</div>
                          <div className="text-xs text-slate-500 mt-1">Moderate volatility expected</div>
                        </div>
                        <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                          <div className="text-2xl font-bold text-blue-400">2-4 weeks</div>
                          <div className="text-sm text-slate-400">Time Horizon</div>
                          <div className="text-xs text-slate-500 mt-1">Optimal holding period</div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <h4 className="text-lg font-medium text-white">AI Summary</h4>
                        <p className="text-slate-300">
                          Based on current market conditions, technical indicators, and historical patterns,
                          {stockData.symbol} shows <span className="text-green-400 font-medium">strong bullish momentum</span> with
                          some short-term overbought concerns. The stock is trading near the upper end of its recent range
                          with increasing volume, suggesting continued interest from institutional investors.
                        </p>
                        <div className="flex gap-2 mt-4">
                          <Button
                            className="bg-blue-600 hover:bg-blue-700"
                            onClick={() => fetchDetailedInsights(stockData.symbol, stockData)}
                            disabled={isLoadingDetailed}
                          >
                            <Brain className="h-4 w-4 mr-2" />
                            {isLoadingDetailed ? 'Analyzing...' : 'Get Detailed Analysis'}
                          </Button>
                          <Button
                            variant="outline"
                            className="border-slate-600 text-slate-300"
                            onClick={() => router.push(`/chat?query=${encodeURIComponent(`Create a learning plan to understand how to analyze ${stockData.symbol} type stocks`)}`)}
                          >
                            <Lightbulb className="h-4 w-4 mr-2" />
                            Learn Analysis Skills
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Detailed AI Insights */}
                  {detailedInsight && (
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <Brain className="h-5 w-5 text-blue-400" />
                          Comprehensive AI Analysis for {stockData.symbol}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        {/* Overview */}
                        <div className="p-4 bg-slate-700/30 rounded-lg">
                          <h4 className="text-lg font-medium text-white mb-2">Executive Summary</h4>
                          <p className="text-slate-300">{detailedInsight.overview}</p>
                        </div>

                        {/* Action Plan */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-slate-700/30 rounded-lg">
                            <h4 className="text-lg font-medium text-white mb-3">Recommendation</h4>
                            <div className="flex items-center gap-2 mb-2">
                              <Badge className={`${
                                detailedInsight.actionPlan.recommendation === 'Strong Buy' || detailedInsight.actionPlan.recommendation === 'Buy'
                                  ? 'bg-green-600'
                                  : detailedInsight.actionPlan.recommendation === 'Hold'
                                  ? 'bg-yellow-600'
                                  : 'bg-red-600'
                              }`}>
                                {detailedInsight.actionPlan.recommendation}
                              </Badge>
                              <span className="text-slate-400">({detailedInsight.actionPlan.confidence}% confidence)</span>
                            </div>
                            <p className="text-slate-300 text-sm">{detailedInsight.actionPlan.reasoning}</p>
                          </div>

                          <div className="p-4 bg-slate-700/30 rounded-lg">
                            <h4 className="text-lg font-medium text-white mb-3">Next Steps</h4>
                            <ul className="space-y-1">
                              {detailedInsight.actionPlan.nextSteps.map((step: string, index: number) => (
                                <li key={index} className="text-slate-300 text-sm flex items-start gap-2">
                                  <span className="text-blue-400 mt-1">•</span>
                                  {step}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>

                        {/* Technical & Fundamental Analysis */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <h4 className="text-lg font-medium text-white">Technical Analysis</h4>
                            <div className="space-y-3">
                              <div className="p-3 bg-slate-700/20 rounded">
                                <h5 className="text-sm font-medium text-slate-300 mb-1">Trend</h5>
                                <p className="text-slate-400 text-xs">{detailedInsight.technicalAnalysis.trend}</p>
                              </div>
                              <div className="p-3 bg-slate-700/20 rounded">
                                <h5 className="text-sm font-medium text-slate-300 mb-1">Momentum</h5>
                                <p className="text-slate-400 text-xs">{detailedInsight.technicalAnalysis.momentum}</p>
                              </div>
                              <div className="p-3 bg-slate-700/20 rounded">
                                <h5 className="text-sm font-medium text-slate-300 mb-1">Support & Resistance</h5>
                                <p className="text-slate-400 text-xs">{detailedInsight.technicalAnalysis.support}</p>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-4">
                            <h4 className="text-lg font-medium text-white">Fundamental Analysis</h4>
                            <div className="space-y-3">
                              <div className="p-3 bg-slate-700/20 rounded">
                                <h5 className="text-sm font-medium text-slate-300 mb-1">Valuation</h5>
                                <p className="text-slate-400 text-xs">{detailedInsight.fundamentalAnalysis.valuation}</p>
                              </div>
                              <div className="p-3 bg-slate-700/20 rounded">
                                <h5 className="text-sm font-medium text-slate-300 mb-1">Growth Prospects</h5>
                                <p className="text-slate-400 text-xs">{detailedInsight.fundamentalAnalysis.growth}</p>
                              </div>
                              <div className="p-3 bg-slate-700/20 rounded">
                                <h5 className="text-sm font-medium text-slate-300 mb-1">Financial Health</h5>
                                <p className="text-slate-400 text-xs">{detailedInsight.fundamentalAnalysis.financialHealth}</p>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Opportunities & Risks */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          <div className="p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
                            <h4 className="text-lg font-medium text-green-400 mb-3">Opportunities</h4>
                            <div className="space-y-2">
                              <p className="text-slate-300 text-sm"><strong>Entry Points:</strong> {detailedInsight.opportunities.entryPoints}</p>
                              <p className="text-slate-300 text-sm"><strong>Price Targets:</strong> {detailedInsight.opportunities.priceTargets}</p>
                              <p className="text-slate-300 text-sm"><strong>Catalysts:</strong> {detailedInsight.opportunities.catalysts}</p>
                            </div>
                          </div>

                          <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
                            <h4 className="text-lg font-medium text-red-400 mb-3">Risk Assessment</h4>
                            <div className="space-y-2">
                              <p className="text-slate-300 text-sm"><strong>Short-term:</strong> {detailedInsight.riskAssessment.shortTerm}</p>
                              <p className="text-slate-300 text-sm"><strong>Medium-term:</strong> {detailedInsight.riskAssessment.mediumTerm}</p>
                              <div className="mt-2">
                                <p className="text-slate-300 text-sm font-medium mb-1">Key Risks:</p>
                                <ul className="space-y-1">
                                  {detailedInsight.riskAssessment.keyRisks.map((risk: string, index: number) => (
                                    <li key={index} className="text-slate-400 text-xs flex items-start gap-2">
                                      <span className="text-red-400 mt-1">•</span>
                                      {risk}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                {/* Historical Data Tab */}
                <TabsContent value="historical" className="space-y-6">
                  <div className="text-center space-y-2 mb-6">
                    <h2 className="text-2xl font-bold text-white">Historical Analysis & Patterns</h2>
                    <p className="text-slate-300 max-w-2xl mx-auto">
                      Explore historical patterns, trends, and key events that shaped {stockData.symbol}'s performance
                    </p>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Historical Performance */}
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <BarChart3 className="h-5 w-5 text-blue-400" />
                          Historical Performance
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-slate-400">1 Month</span>
                            <span className="text-green-400 font-medium">+5.2%</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-slate-400">3 Months</span>
                            <span className="text-green-400 font-medium">+12.8%</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-slate-400">6 Months</span>
                            <span className="text-green-400 font-medium">+18.5%</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-slate-400">1 Year</span>
                            <span className="text-green-400 font-medium">+24.3%</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-slate-400">5 Years</span>
                            <span className="text-green-400 font-medium">+156.7%</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Pattern Recognition */}
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <Target className="h-5 w-5 text-purple-400" />
                          AI Pattern Recognition
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-3">
                          <div className="p-3 bg-slate-700/30 rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <CheckCircle className="h-4 w-4 text-green-400" />
                              <span className="text-green-400 font-medium">Ascending Triangle</span>
                            </div>
                            <p className="text-xs text-slate-400">Bullish pattern forming over 3 months</p>
                          </div>
                          <div className="p-3 bg-slate-700/30 rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <Eye className="h-4 w-4 text-yellow-400" />
                              <span className="text-yellow-400 font-medium">Support at $145</span>
                            </div>
                            <p className="text-xs text-slate-400">Strong support level tested 3 times</p>
                          </div>
                          <div className="p-3 bg-slate-700/30 rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <TrendingUp className="h-4 w-4 text-blue-400" />
                              <span className="text-blue-400 font-medium">Volume Breakout</span>
                            </div>
                            <p className="text-xs text-slate-400">Recent volume spike above average</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Key Events Timeline */}
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center gap-2">
                        <Calendar className="h-5 w-5 text-green-400" />
                        Key Events Timeline
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-start gap-4">
                          <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h4 className="text-white font-medium">Q3 Earnings Beat</h4>
                              <span className="text-xs text-slate-400">2 weeks ago</span>
                            </div>
                            <p className="text-sm text-slate-400">Revenue exceeded expectations by 8%, stock gained 12%</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-4">
                          <div className="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h4 className="text-white font-medium">Product Launch</h4>
                              <span className="text-xs text-slate-400">1 month ago</span>
                            </div>
                            <p className="text-sm text-slate-400">New product line announcement, positive market reaction</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-4">
                          <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2"></div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h4 className="text-white font-medium">Analyst Upgrade</h4>
                              <span className="text-xs text-slate-400">6 weeks ago</span>
                            </div>
                            <p className="text-sm text-slate-400">Major investment bank upgraded to "Buy" rating</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Learning Hub Tab */}
                <TabsContent value="learning" className="space-y-6">
                  <div className="text-center space-y-2 mb-6">
                    <h2 className="text-2xl font-bold text-white">Learning Hub for {stockData.symbol}</h2>
                    <p className="text-slate-300 max-w-2xl mx-auto">
                      Educational resources, news, and insights to help you understand this stock better
                    </p>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Educational Content */}
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <BookOpen className="h-5 w-5 text-blue-400" />
                          Learn About This Stock
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-3">
                          <Button
                            variant="outline"
                            className="w-full justify-start border-slate-600 text-slate-300"
                            onClick={() => router.push(`/chat?query=${encodeURIComponent(`Explain the business model of ${stockData.symbol} in simple terms`)}`)}
                          >
                            <BookOpen className="h-4 w-4 mr-2" />
                            Understanding the Business
                          </Button>
                          <Button
                            variant="outline"
                            className="w-full justify-start border-slate-600 text-slate-300"
                            onClick={() => router.push(`/chat?query=${encodeURIComponent(`What are the key financial metrics to watch for ${stockData.symbol}?`)}`)}
                          >
                            <BarChart3 className="h-4 w-4 mr-2" />
                            Key Financial Metrics
                          </Button>
                          <Button
                            variant="outline"
                            className="w-full justify-start border-slate-600 text-slate-300"
                            onClick={() => router.push(`/chat?query=${encodeURIComponent(`What are the main risks and opportunities for ${stockData.symbol}?`)}`)}
                          >
                            <AlertTriangle className="h-4 w-4 mr-2" />
                            Risks & Opportunities
                          </Button>
                          <Button
                            variant="outline"
                            className="w-full justify-start border-slate-600 text-slate-300"
                            onClick={() => router.push(`/chat?query=${encodeURIComponent(`How does ${stockData.symbol} compare to its competitors?`)}`)}
                          >
                            <Target className="h-4 w-4 mr-2" />
                            Competitive Analysis
                          </Button>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Latest News */}
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <Globe className="h-5 w-5 text-green-400" />
                          Latest News & Events
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-3">
                          <div className="p-3 bg-slate-700/30 rounded-lg">
                            <h4 className="text-white font-medium text-sm mb-1">Q3 Earnings Report Released</h4>
                            <p className="text-xs text-slate-400 mb-2">Strong revenue growth and positive guidance</p>
                            <span className="text-xs text-blue-400">2 hours ago</span>
                          </div>
                          <div className="p-3 bg-slate-700/30 rounded-lg">
                            <h4 className="text-white font-medium text-sm mb-1">Analyst Coverage Update</h4>
                            <p className="text-xs text-slate-400 mb-2">Price target raised to $175 by Goldman Sachs</p>
                            <span className="text-xs text-blue-400">1 day ago</span>
                          </div>
                          <div className="p-3 bg-slate-700/30 rounded-lg">
                            <h4 className="text-white font-medium text-sm mb-1">Industry Outlook</h4>
                            <p className="text-xs text-slate-400 mb-2">Sector showing strong momentum this quarter</p>
                            <span className="text-xs text-blue-400">3 days ago</span>
                          </div>
                        </div>
                        <Button
                          className="w-full bg-blue-600 hover:bg-blue-700"
                          onClick={() => router.push(`/news?filter=${stockData.symbol}`)}
                        >
                          <Globe className="h-4 w-4 mr-2" />
                          View All News
                        </Button>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Learning Modules */}
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center gap-2">
                        <GraduationCap className="h-5 w-5 text-purple-400" />
                        Recommended Learning Modules
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="p-4 bg-slate-700/30 rounded-lg">
                          <h4 className="text-white font-medium mb-2">Stock Valuation</h4>
                          <p className="text-xs text-slate-400 mb-3">Learn how to value stocks using P/E, DCF, and other methods</p>
                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full border-slate-600 text-slate-300"
                            onClick={() => router.push('/learning?topic=stock-valuation')}
                          >
                            Start Learning
                          </Button>
                        </div>
                        <div className="p-4 bg-slate-700/30 rounded-lg">
                          <h4 className="text-white font-medium mb-2">Technical Analysis</h4>
                          <p className="text-xs text-slate-400 mb-3">Master chart patterns, indicators, and trading signals</p>
                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full border-slate-600 text-slate-300"
                            onClick={() => router.push('/learning?topic=technical-analysis')}
                          >
                            Start Learning
                          </Button>
                        </div>
                        <div className="p-4 bg-slate-700/30 rounded-lg">
                          <h4 className="text-white font-medium mb-2">Risk Management</h4>
                          <p className="text-xs text-slate-400 mb-3">Understand how to manage investment risks effectively</p>
                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full border-slate-600 text-slate-300"
                            onClick={() => router.push('/learning?topic=risk-management')}
                          >
                            Start Learning
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
