"use client";

import { useState, useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  TrendingUp,
  TrendingDown,
  Search,
  Brain,
  BarChart3,
  Clock,
  DollarSign,
  Activity,
  AlertTriangle,
  CheckCircle,
  Eye,
  Calendar,
  Zap,
  Target,
  BookOpen,
  RefreshCw,
  ArrowUp,
  ArrowDown,
  Minus,
  Star,
  Globe,
  Volume2,
  Lightbulb,
  GraduationCap
} from 'lucide-react';
import StockChart from '@/components/StockChart';

interface StockData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
  high52Week: number;
  low52Week: number;
  pe: number;
  dividend: number;
  lastUpdate: string;
  market: 'US' | 'IN';
  currency: 'USD' | 'INR';
  exchange: string;
  priceInUSD?: number;
  priceInINR?: number;
  exchangeRate?: number;
}

interface AIInsight {
  type: 'opportunity' | 'risk' | 'neutral';
  confidence: number;
  signal: string;
  reasoning: string;
  timeframe: string;
  actionSuggestion: string;
}

export default function StockAnalysisPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedStock, setSelectedStock] = useState<string>(searchParams.get('ticker') || '');
  const [stockData, setStockData] = useState<StockData | null>(null);
  const [aiInsights, setAiInsights] = useState<AIInsight[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('live');
  const [searchQuery, setSearchQuery] = useState('');
  const [timeframe, setTimeframe] = useState('1D');
  const [isRealTime, setIsRealTime] = useState(false);
  const [currency, setCurrency] = useState<'USD' | 'INR'>('USD');
  const [displayCurrency, setDisplayCurrency] = useState<'USD' | 'INR'>('USD');
  const [detailedInsight, setDetailedInsight] = useState<any>(null);
  const [isLoadingDetailed, setIsLoadingDetailed] = useState(false);
  const [newsData, setNewsData] = useState<any>(null);
  const [isLoadingNews, setIsLoadingNews] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Popular stocks for quick access
  const popularUSStocks = [
    { symbol: 'AAPL', name: 'Apple Inc.' },
    { symbol: 'MSFT', name: 'Microsoft Corp.' },
    { symbol: 'GOOGL', name: 'Alphabet Inc.' },
    { symbol: 'AMZN', name: 'Amazon.com Inc.' },
    { symbol: 'TSLA', name: 'Tesla Inc.' },
    { symbol: 'NVDA', name: 'NVIDIA Corp.' },
    { symbol: 'META', name: 'Meta Platforms' },
    { symbol: 'NFLX', name: 'Netflix Inc.' }
  ];

  const popularIndianStocks = [
    { symbol: 'RELIANCE.NS', name: 'Reliance Industries' },
    { symbol: 'TCS.NS', name: 'Tata Consultancy Services' },
    { symbol: 'INFY.NS', name: 'Infosys Limited' },
    { symbol: 'HDFCBANK.NS', name: 'HDFC Bank Limited' },
    { symbol: 'ICICIBANK.NS', name: 'ICICI Bank Limited' },
    { symbol: 'SBIN.NS', name: 'State Bank of India' },
    { symbol: 'ITC.NS', name: 'ITC Limited' },
    { symbol: 'LT.NS', name: 'Larsen & Toubro' }
  ];

  const popularStocks = currency === 'INR' ? popularIndianStocks : popularUSStocks;

  // Function to detect market and set appropriate currency
  const detectMarketAndSetCurrency = (ticker: string) => {
    // Indian stock patterns
    if (ticker.endsWith('.NS') || ticker.endsWith('.BO') || ticker.endsWith('.BSE')) {
      setDisplayCurrency('INR');
      return 'IN';
    }

    // Common Indian stock symbols
    const indianStocks = ['RELIANCE', 'TCS', 'INFY', 'HDFCBANK', 'ICICIBANK', 'SBIN', 'ITC', 'LT', 'BHARTIARTL', 'ASIANPAINT'];
    if (indianStocks.includes(ticker.replace(/\.(NS|BO|BSE)$/, ''))) {
      setDisplayCurrency('INR');
      return 'IN';
    }

    // Default to US market
    setDisplayCurrency('USD');
    return 'US';
  };

  useEffect(() => {
    if (selectedStock) {
      fetchStockData(selectedStock);
      if (isRealTime) {
        startRealTimeUpdates();
      }
    }
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [selectedStock, isRealTime]);

  const fetchStockData = async (ticker: string) => {
    setIsLoading(true);

    // Detect market and set appropriate currency
    const detectedMarket = detectMarketAndSetCurrency(ticker);
    const appropriateCurrency = detectedMarket === 'IN' ? 'INR' : 'USD';

    try {
      // Fetch live stock data with appropriate currency
      const response = await fetch(`/api/liveData/${ticker}?currency=${appropriateCurrency}`);
      if (response.ok) {
        const data = await response.json();
        setStockData(data);

        // Update currency state to match the stock's native currency
        setCurrency(appropriateCurrency);

        // Fetch AI insights and news
        await fetchAIInsights(ticker, data);
        await fetchNewsData(ticker);
      }
    } catch (error) {
      console.error('Error fetching stock data:', error);
      // Mock data for development
      setStockData({
        symbol: ticker.toUpperCase(),
        name: `${ticker.toUpperCase()} Corporation`,
        price: 150.25,
        change: 2.45,
        changePercent: 1.66,
        volume: 45678900,
        marketCap: 2450000000000,
        high52Week: 180.50,
        low52Week: 120.30,
        pe: 28.5,
        dividend: 0.88,
        lastUpdate: new Date().toISOString(),
        market: 'US',
        currency: 'USD',
        exchange: 'NASDAQ'
      });
      
      // Mock AI insights
      setAiInsights([
        {
          type: 'opportunity',
          confidence: 75,
          signal: 'Bullish Momentum',
          reasoning: 'Stock is showing strong upward momentum with increasing volume and breaking above key resistance levels.',
          timeframe: 'Short-term (1-2 weeks)',
          actionSuggestion: 'Consider buying on pullbacks to support levels'
        },
        {
          type: 'risk',
          confidence: 60,
          signal: 'Overbought RSI',
          reasoning: 'RSI is above 70, indicating potential overbought conditions. May see short-term correction.',
          timeframe: 'Immediate (1-3 days)',
          actionSuggestion: 'Wait for RSI to cool down before entering'
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchDetailedInsights = async (ticker: string, stockData: any) => {
    setIsLoadingDetailed(true);
    try {
      const response = await fetch('/api/ai/detailedInsights', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ticker,
          stockData,
          market: stockData.market || 'US'
        })
      });

      if (response.ok) {
        const insights = await response.json();
        setDetailedInsight(insights);
      }
    } catch (error) {
      console.error('Error fetching detailed insights:', error);
    } finally {
      setIsLoadingDetailed(false);
    }
  };

  const fetchNewsData = async (ticker: string) => {
    setIsLoadingNews(true);
    try {
      const response = await fetch(`/api/news/stock/${ticker}?limit=6`);
      if (response.ok) {
        const news = await response.json();
        setNewsData(news);
      }
    } catch (error) {
      console.error('Error fetching news data:', error);
      // Set mock news data as fallback
      setNewsData(generateMockNewsData(ticker));
    } finally {
      setIsLoadingNews(false);
    }
  };

  const generateMockNewsData = (ticker: string) => {
    return {
      symbol: ticker,
      articles: [
        {
          title: `${ticker} Reports Strong Q3 Earnings, Beats Expectations`,
          description: `${ticker} Corporation announced quarterly earnings that exceeded analyst expectations, driven by strong revenue growth and improved margins.`,
          url: `#`,
          source: 'Financial Times',
          publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          sentiment: 'positive',
          relevanceScore: 0.95
        },
        {
          title: `Analyst Upgrades ${ticker} to Buy Rating`,
          description: `Goldman Sachs upgraded ${ticker} to a Buy rating, citing strong fundamentals and growth prospects.`,
          url: `#`,
          source: 'Reuters',
          publishedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          sentiment: 'positive',
          relevanceScore: 0.88
        },
        {
          title: `${ticker} Announces New Product Line Expansion`,
          description: `The company revealed plans to expand its product portfolio with innovative solutions targeting emerging markets.`,
          url: `#`,
          source: 'Bloomberg',
          publishedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          sentiment: 'positive',
          relevanceScore: 0.82
        },
        {
          title: `Market Volatility Affects ${ticker} Trading Volume`,
          description: `Recent market uncertainty has led to increased trading volume in ${ticker} shares as investors reassess positions.`,
          url: `#`,
          source: 'MarketWatch',
          publishedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          sentiment: 'neutral',
          relevanceScore: 0.65
        },
        {
          title: `${ticker} Stock Shows Resilience Amid Market Downturn`,
          description: `Despite broader market challenges, ${ticker} maintains strong performance with solid fundamentals.`,
          url: `#`,
          source: 'CNBC',
          publishedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          sentiment: 'positive',
          relevanceScore: 0.78
        },
        {
          title: `Industry Analysis: ${ticker} Positioned for Growth`,
          description: `Sector analysis indicates favorable conditions for ${ticker} with strong competitive positioning.`,
          url: `#`,
          source: 'Wall Street Journal',
          publishedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          sentiment: 'positive',
          relevanceScore: 0.72
        }
      ],
      sentiment: {
        overall: 'positive',
        score: 0.75,
        distribution: { positive: 75, negative: 10, neutral: 15 }
      },
      keyTopics: ['Earnings', 'Growth', 'Market Performance', 'Analyst Ratings']
    };
  };

  const fetchAIInsights = async (ticker: string, stockData: StockData) => {
    try {
      const response = await fetch('/api/ai/opportunityMap', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ticker, stockData })
      });
      
      if (response.ok) {
        const insights = await response.json();
        setAiInsights(insights);
      }
    } catch (error) {
      console.error('Error fetching AI insights:', error);
    }
  };

  const startRealTimeUpdates = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    intervalRef.current = setInterval(() => {
      if (selectedStock) {
        fetchStockData(selectedStock);
      }
    }, 30000); // Update every 30 seconds
  };

  const handleStockSelect = (ticker: string) => {
    // Detect market and set appropriate currency
    detectMarketAndSetCurrency(ticker);

    setSelectedStock(ticker);
    router.push(`/stock-analysis?ticker=${ticker}`);
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      const ticker = searchQuery.trim().toUpperCase();

      // Detect market and set appropriate currency before searching
      detectMarketAndSetCurrency(ticker);

      handleStockSelect(ticker);
      setSearchQuery('');
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1e12) return `$${(num / 1e12).toFixed(2)}T`;
    if (num >= 1e9) return `$${(num / 1e9).toFixed(2)}B`;
    if (num >= 1e6) return `$${(num / 1e6).toFixed(2)}M`;
    if (num >= 1e3) return `$${(num / 1e3).toFixed(2)}K`;
    return `$${num.toFixed(2)}`;
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1e9) return `${(volume / 1e9).toFixed(2)}B`;
    if (volume >= 1e6) return `${(volume / 1e6).toFixed(2)}M`;
    if (volume >= 1e3) return `${(volume / 1e3).toFixed(2)}K`;
    return volume.toString();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-white flex items-center justify-center gap-3">
              <BarChart3 className="h-8 w-8 text-blue-400" />
              AI Stock Analysis Engine
            </h1>
            <p className="text-slate-300 max-w-3xl mx-auto text-lg">
              Real-time stock analysis powered by AI. Get live data, opportunity signals, and educational insights.
            </p>
            <p className="text-slate-400 max-w-2xl mx-auto text-sm">
              💡 Smart Currency Detection: US stocks automatically display in USD, Indian stocks in INR
            </p>

            {/* Market Toggle */}
            <div className="flex justify-center mt-4">
              <div className="bg-slate-800/50 rounded-lg p-1 flex gap-1">
                <button
                  onClick={() => {
                    setCurrency('USD');
                    setDisplayCurrency('USD');
                  }}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                    currency === 'USD'
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'text-slate-300 hover:text-white hover:bg-slate-700'
                  }`}
                >
                  🇺🇸 US Market (USD)
                </button>
                <button
                  onClick={() => {
                    setCurrency('INR');
                    setDisplayCurrency('INR');
                  }}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
                    currency === 'INR'
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'text-slate-300 hover:text-white hover:bg-slate-700'
                  }`}
                >
                  🇮🇳 Indian Market (INR)
                </button>
              </div>

              {/* Current Stock Market Indicator */}
              {stockData && (
                <div className="ml-4 flex items-center gap-2 text-sm">
                  <span className="text-slate-400">Current:</span>
                  <Badge variant="outline" className={`${
                    stockData.market === 'IN'
                      ? 'border-orange-500/30 text-orange-300'
                      : 'border-blue-500/30 text-blue-300'
                  }`}>
                    {stockData.market === 'IN' ? '🇮🇳 Indian Stock' : '🇺🇸 US Stock'}
                  </Badge>
                  <span className="text-slate-500">•</span>
                  <span className="text-slate-300">{stockData.exchange}</span>
                </div>
              )}
            </div>
          </div>

          {/* Stock Search */}
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Search className="h-5 w-5 text-blue-400" />
                Stock Search
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder={currency === 'INR'
                    ? "Enter stock ticker (e.g., RELIANCE.NS, TCS.NS, INFY.NS)"
                    : "Enter stock ticker (e.g., AAPL, MSFT, GOOGL)"
                  }
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                />
                <Button onClick={handleSearch} className="bg-blue-600 hover:bg-blue-700">
                  <Search className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Popular Stocks */}
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-slate-300">Popular Stocks</h4>
                <div className="flex flex-wrap gap-2">
                  {popularStocks.map((stock) => (
                    <Button
                      key={stock.symbol}
                      variant="outline"
                      size="sm"
                      onClick={() => handleStockSelect(stock.symbol)}
                      className={`border-slate-600 text-slate-300 hover:bg-slate-700 ${
                        selectedStock === stock.symbol ? 'bg-blue-600 text-white border-blue-500' : ''
                      }`}
                    >
                      {stock.symbol}
                    </Button>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Main Analysis Interface */}
          {selectedStock && stockData && (
            <div className="space-y-6">
              {/* Stock Header */}
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div>
                        <h2 className="text-2xl font-bold text-white">{stockData.symbol}</h2>
                        <p className="text-slate-400">{stockData.name}</p>
                      </div>
                      <Badge variant="outline" className="border-blue-500/30 text-blue-300">
                        Live Data
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsRealTime(!isRealTime)}
                        className={`border-slate-600 ${
                          isRealTime ? 'bg-green-600 text-white' : 'text-slate-300'
                        }`}
                      >
                        <RefreshCw className={`h-4 w-4 mr-2 ${isRealTime ? 'animate-spin' : ''}`} />
                        {isRealTime ? 'Live' : 'Manual'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => fetchStockData(selectedStock)}
                        disabled={isLoading}
                        className="border-slate-600 text-slate-300"
                      >
                        <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                      </Button>
                    </div>
                  </div>

                  {/* Price Information */}
                  <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">
                        {stockData.currency === 'INR' ? '₹' : '$'}{stockData.price.toFixed(2)}
                      </div>
                      <div className="text-sm text-slate-400">
                        Current Price ({stockData.currency})
                        {stockData.exchangeRate && (
                          <div className="text-xs text-slate-500 mt-1">
                            {stockData.currency === 'USD' && stockData.priceInINR ? `≈ ₹${stockData.priceInINR.toFixed(2)}` :
                             stockData.currency === 'INR' && stockData.priceInUSD ? `≈ $${stockData.priceInUSD.toFixed(2)}` : ''}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="text-center">
                      <div className={`text-xl font-bold flex items-center justify-center gap-1 ${
                        stockData.change >= 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {stockData.change >= 0 ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                        ${Math.abs(stockData.change).toFixed(2)}
                      </div>
                      <div className="text-sm text-slate-400">Change</div>
                    </div>
                    <div className="text-center">
                      <div className={`text-xl font-bold ${
                        stockData.changePercent >= 0 ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {stockData.changePercent >= 0 ? '+' : ''}{stockData.changePercent.toFixed(2)}%
                      </div>
                      <div className="text-sm text-slate-400">% Change</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">{formatVolume(stockData.volume)}</div>
                      <div className="text-sm text-slate-400">Volume</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">{formatNumber(stockData.marketCap)}</div>
                      <div className="text-sm text-slate-400">Market Cap</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">{stockData.pe.toFixed(1)}</div>
                      <div className="text-sm text-slate-400">P/E Ratio</div>
                    </div>
                  </div>

                  {/* 52-Week Range */}
                  <div className="mt-4">
                    <div className="flex items-center justify-between text-sm text-slate-400 mb-2">
                      <span>52-Week Range</span>
                      <span>${stockData.low52Week.toFixed(2)} - ${stockData.high52Week.toFixed(2)}</span>
                    </div>
                    <div className="relative">
                      <div className="w-full bg-slate-700 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-2 rounded-full"
                          style={{
                            width: `${((stockData.price - stockData.low52Week) / (stockData.high52Week - stockData.low52Week)) * 100}%`
                          }}
                        />
                      </div>
                      <div
                        className="absolute top-0 w-1 h-2 bg-white rounded"
                        style={{
                          left: `${((stockData.price - stockData.low52Week) / (stockData.high52Week - stockData.low52Week)) * 100}%`
                        }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Tabbed Analysis Interface */}
              <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
                <div className="flex justify-center">
                  <TabsList className="bg-slate-800/50 border-slate-700 p-1">
                    <TabsTrigger value="live" className="flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      Live View
                    </TabsTrigger>
                    <TabsTrigger value="ai-insights" className="flex items-center gap-2">
                      <Brain className="h-4 w-4" />
                      AI Insights
                    </TabsTrigger>
                    <TabsTrigger value="news" className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      News & Sentiment
                    </TabsTrigger>
                    <TabsTrigger value="historical" className="flex items-center gap-2">
                      <BarChart3 className="h-4 w-4" />
                      Historical Data
                    </TabsTrigger>
                    <TabsTrigger value="learning" className="flex items-center gap-2">
                      <BookOpen className="h-4 w-4" />
                      Learning Hub
                    </TabsTrigger>
                  </TabsList>
                </div>

                {/* Live View Tab */}
                <TabsContent value="live" className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Live Chart */}
                    <div className="lg:col-span-2">
                      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <CardTitle className="text-white flex items-center gap-2">
                              <BarChart3 className="h-5 w-5 text-blue-400" />
                              Live Chart
                            </CardTitle>
                            <div className="flex gap-2">
                              {['1D', '5D', '1M', '3M', '1Y'].map((period) => (
                                <Button
                                  key={period}
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setTimeframe(period)}
                                  className={`border-slate-600 text-xs ${
                                    timeframe === period ? 'bg-blue-600 text-white' : 'text-slate-300'
                                  }`}
                                >
                                  {period}
                                </Button>
                              ))}
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          {/* Interactive Stock Chart */}
                          <StockChart
                            ticker={stockData.symbol}
                            timeframe={timeframe}
                            height={320}
                          />
                        </CardContent>
                      </Card>
                    </div>

                    {/* Market Depth & Quick Stats */}
                    <div className="space-y-6">
                      {/* Quick Stats */}
                      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                        <CardHeader>
                          <CardTitle className="text-white text-lg">Quick Stats</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="space-y-3">
                            <div className="flex justify-between">
                              <span className="text-slate-400">Open</span>
                              <span className="text-white">${(stockData.price - stockData.change + 0.5).toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-400">High</span>
                              <span className="text-white">${(stockData.price + 2.3).toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-400">Low</span>
                              <span className="text-white">${(stockData.price - 1.8).toFixed(2)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-400">Avg Volume</span>
                              <span className="text-white">{formatVolume(stockData.volume * 0.85)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-slate-400">Dividend</span>
                              <span className="text-white">${(stockData.dividend || 0).toFixed(2)}</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* Technical Indicators */}
                      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                        <CardHeader>
                          <CardTitle className="text-white text-lg">Technical Indicators</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div className="space-y-3">
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-slate-400">RSI (14)</span>
                                <span className="text-yellow-400">68.5</span>
                              </div>
                              <Progress value={68.5} className="h-2" />
                              <div className="text-xs text-slate-500 mt-1">Approaching overbought</div>
                            </div>
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-slate-400">MACD</span>
                                <span className="text-green-400">Bullish</span>
                              </div>
                              <div className="text-xs text-slate-500">Signal line crossover</div>
                            </div>
                            <div>
                              <div className="flex justify-between mb-1">
                                <span className="text-slate-400">Volume Trend</span>
                                <span className="text-blue-400">Above Average</span>
                              </div>
                              <div className="text-xs text-slate-500">+23% vs 20-day avg</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      {/* Market Sentiment */}
                      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                        <CardHeader>
                          <CardTitle className="text-white text-lg">Market Sentiment</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-slate-400">Analyst Rating</span>
                            <Badge variant="outline" className="border-green-500/30 text-green-300">
                              Buy
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-slate-400">Price Target</span>
                            <span className="text-green-400">${(stockData.price * 1.15).toFixed(2)}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-slate-400">Social Sentiment</span>
                            <div className="flex items-center gap-1">
                              <TrendingUp className="h-4 w-4 text-green-400" />
                              <span className="text-green-400">Positive</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </TabsContent>

                {/* AI Insights Tab */}
                <TabsContent value="ai-insights" className="space-y-6">
                  <div className="text-center space-y-2 mb-6">
                    <h2 className="text-2xl font-bold text-white">AI-Powered Market Analysis</h2>
                    <p className="text-slate-300 max-w-2xl mx-auto">
                      Advanced AI analysis of market conditions, opportunities, and risks for {stockData.symbol}
                    </p>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* AI Insights Cards */}
                    {aiInsights.map((insight, index) => (
                      <Card key={index} className={`bg-slate-800/50 border-slate-700 backdrop-blur-sm ${
                        insight.type === 'opportunity' ? 'border-l-4 border-l-green-500' :
                        insight.type === 'risk' ? 'border-l-4 border-l-red-500' :
                        'border-l-4 border-l-yellow-500'
                      }`}>
                        <CardHeader>
                          <div className="flex items-center justify-between">
                            <CardTitle className={`text-lg flex items-center gap-2 ${
                              insight.type === 'opportunity' ? 'text-green-400' :
                              insight.type === 'risk' ? 'text-red-400' :
                              'text-yellow-400'
                            }`}>
                              {insight.type === 'opportunity' ? <TrendingUp className="h-5 w-5" /> :
                               insight.type === 'risk' ? <AlertTriangle className="h-5 w-5" /> :
                               <Eye className="h-5 w-5" />}
                              {insight.signal}
                            </CardTitle>
                            <Badge variant="outline" className={`${
                              insight.confidence >= 80 ? 'border-green-500/30 text-green-300' :
                              insight.confidence >= 60 ? 'border-yellow-500/30 text-yellow-300' :
                              'border-red-500/30 text-red-300'
                            }`}>
                              {insight.confidence}% Confidence
                            </Badge>
                          </div>
                          <CardDescription className="text-slate-400">
                            {insight.timeframe}
                          </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          <div>
                            <h4 className="text-sm font-medium text-slate-300 mb-2">AI Analysis</h4>
                            <p className="text-slate-400 text-sm">{insight.reasoning}</p>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-slate-300 mb-2">Suggested Action</h4>
                            <p className="text-white text-sm font-medium">{insight.actionSuggestion}</p>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-slate-600 text-slate-300"
                              onClick={() => router.push(`/chat?query=${encodeURIComponent(`Explain more about ${insight.signal} for ${stockData.symbol}`)}`)}
                            >
                              <Brain className="h-3 w-3 mr-1" />
                              Ask AI More
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-slate-600 text-slate-300"
                              onClick={() => setActiveTab('learning')}
                            >
                              <BookOpen className="h-3 w-3 mr-1" />
                              Learn More
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {/* Overall AI Assessment */}
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center gap-2">
                        <Brain className="h-5 w-5 text-purple-400" />
                        Overall AI Assessment
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                          <div className="text-2xl font-bold text-green-400">75%</div>
                          <div className="text-sm text-slate-400">Buy Confidence</div>
                          <div className="text-xs text-slate-500 mt-1">Based on technical & fundamental analysis</div>
                        </div>
                        <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                          <div className="text-2xl font-bold text-yellow-400">Medium</div>
                          <div className="text-sm text-slate-400">Risk Level</div>
                          <div className="text-xs text-slate-500 mt-1">Moderate volatility expected</div>
                        </div>
                        <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                          <div className="text-2xl font-bold text-blue-400">2-4 weeks</div>
                          <div className="text-sm text-slate-400">Time Horizon</div>
                          <div className="text-xs text-slate-500 mt-1">Optimal holding period</div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        <h4 className="text-lg font-medium text-white">AI Summary</h4>
                        <p className="text-slate-300">
                          Based on current market conditions, technical indicators, and historical patterns,
                          {stockData.symbol} shows <span className="text-green-400 font-medium">strong bullish momentum</span> with
                          some short-term overbought concerns. The stock is trading near the upper end of its recent range
                          with increasing volume, suggesting continued interest from institutional investors.
                        </p>
                        <div className="flex gap-2 mt-4">
                          <Button
                            className="bg-blue-600 hover:bg-blue-700"
                            onClick={() => fetchDetailedInsights(stockData.symbol, stockData)}
                            disabled={isLoadingDetailed}
                          >
                            <Brain className="h-4 w-4 mr-2" />
                            {isLoadingDetailed ? 'Analyzing...' : 'Get Detailed Analysis'}
                          </Button>
                          <Button
                            variant="outline"
                            className="border-slate-600 text-slate-300"
                            onClick={() => router.push(`/chat?query=${encodeURIComponent(`Create a learning plan to understand how to analyze ${stockData.symbol} type stocks`)}`)}
                          >
                            <Lightbulb className="h-4 w-4 mr-2" />
                            Learn Analysis Skills
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Detailed AI Insights */}
                  {detailedInsight && (
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <Brain className="h-5 w-5 text-blue-400" />
                          Comprehensive AI Analysis for {stockData.symbol}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        {/* Overview */}
                        <div className="p-4 bg-slate-700/30 rounded-lg">
                          <h4 className="text-lg font-medium text-white mb-2">Executive Summary</h4>
                          <p className="text-slate-300">{detailedInsight.overview}</p>
                        </div>

                        {/* Action Plan */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="p-4 bg-slate-700/30 rounded-lg">
                            <h4 className="text-lg font-medium text-white mb-3">Recommendation</h4>
                            <div className="flex items-center gap-2 mb-2">
                              <Badge className={`${
                                detailedInsight.actionPlan.recommendation === 'Strong Buy' || detailedInsight.actionPlan.recommendation === 'Buy'
                                  ? 'bg-green-600'
                                  : detailedInsight.actionPlan.recommendation === 'Hold'
                                  ? 'bg-yellow-600'
                                  : 'bg-red-600'
                              }`}>
                                {detailedInsight.actionPlan.recommendation}
                              </Badge>
                              <span className="text-slate-400">({detailedInsight.actionPlan.confidence}% confidence)</span>
                            </div>
                            <p className="text-slate-300 text-sm">{detailedInsight.actionPlan.reasoning}</p>
                          </div>

                          <div className="p-4 bg-slate-700/30 rounded-lg">
                            <h4 className="text-lg font-medium text-white mb-3">Next Steps</h4>
                            <ul className="space-y-1">
                              {detailedInsight.actionPlan.nextSteps.map((step: string, index: number) => (
                                <li key={index} className="text-slate-300 text-sm flex items-start gap-2">
                                  <span className="text-blue-400 mt-1">•</span>
                                  {step}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>

                        {/* Technical & Fundamental Analysis */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <h4 className="text-lg font-medium text-white">Technical Analysis</h4>
                            <div className="space-y-3">
                              <div className="p-3 bg-slate-700/20 rounded">
                                <h5 className="text-sm font-medium text-slate-300 mb-1">Trend</h5>
                                <p className="text-slate-400 text-xs">{detailedInsight.technicalAnalysis.trend}</p>
                              </div>
                              <div className="p-3 bg-slate-700/20 rounded">
                                <h5 className="text-sm font-medium text-slate-300 mb-1">Momentum</h5>
                                <p className="text-slate-400 text-xs">{detailedInsight.technicalAnalysis.momentum}</p>
                              </div>
                              <div className="p-3 bg-slate-700/20 rounded">
                                <h5 className="text-sm font-medium text-slate-300 mb-1">Support & Resistance</h5>
                                <p className="text-slate-400 text-xs">{detailedInsight.technicalAnalysis.support}</p>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-4">
                            <h4 className="text-lg font-medium text-white">Fundamental Analysis</h4>
                            <div className="space-y-3">
                              <div className="p-3 bg-slate-700/20 rounded">
                                <h5 className="text-sm font-medium text-slate-300 mb-1">Valuation</h5>
                                <p className="text-slate-400 text-xs">{detailedInsight.fundamentalAnalysis.valuation}</p>
                              </div>
                              <div className="p-3 bg-slate-700/20 rounded">
                                <h5 className="text-sm font-medium text-slate-300 mb-1">Growth Prospects</h5>
                                <p className="text-slate-400 text-xs">{detailedInsight.fundamentalAnalysis.growth}</p>
                              </div>
                              <div className="p-3 bg-slate-700/20 rounded">
                                <h5 className="text-sm font-medium text-slate-300 mb-1">Financial Health</h5>
                                <p className="text-slate-400 text-xs">{detailedInsight.fundamentalAnalysis.financialHealth}</p>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Opportunities & Risks */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          <div className="p-4 bg-green-900/20 border border-green-500/30 rounded-lg">
                            <h4 className="text-lg font-medium text-green-400 mb-3">Opportunities</h4>
                            <div className="space-y-2">
                              <p className="text-slate-300 text-sm"><strong>Entry Points:</strong> {detailedInsight.opportunities.entryPoints}</p>
                              <p className="text-slate-300 text-sm"><strong>Price Targets:</strong> {detailedInsight.opportunities.priceTargets}</p>
                              <p className="text-slate-300 text-sm"><strong>Catalysts:</strong> {detailedInsight.opportunities.catalysts}</p>
                            </div>
                          </div>

                          <div className="p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
                            <h4 className="text-lg font-medium text-red-400 mb-3">Risk Assessment</h4>
                            <div className="space-y-2">
                              <p className="text-slate-300 text-sm"><strong>Short-term:</strong> {detailedInsight.riskAssessment.shortTerm}</p>
                              <p className="text-slate-300 text-sm"><strong>Medium-term:</strong> {detailedInsight.riskAssessment.mediumTerm}</p>
                              <div className="mt-2">
                                <p className="text-slate-300 text-sm font-medium mb-1">Key Risks:</p>
                                <ul className="space-y-1">
                                  {detailedInsight.riskAssessment.keyRisks.map((risk: string, index: number) => (
                                    <li key={index} className="text-slate-400 text-xs flex items-start gap-2">
                                      <span className="text-red-400 mt-1">•</span>
                                      {risk}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                {/* News & Sentiment Tab */}
                <TabsContent value="news" className="space-y-6">
                  <div className="text-center space-y-2 mb-6">
                    <h2 className="text-2xl font-bold text-white">News & Market Sentiment</h2>
                    <p className="text-slate-300 max-w-2xl mx-auto">
                      Latest news, market sentiment analysis, and trending topics for {stockData.symbol}
                    </p>
                  </div>

                  {/* Sentiment Overview */}
                  {newsData && (
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <Activity className="h-5 w-5 text-purple-400" />
                          Market Sentiment Analysis
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                          {/* Overall Sentiment */}
                          <div className="text-center p-4 bg-slate-700/30 rounded-lg">
                            <div className={`text-3xl font-bold mb-2 ${
                              newsData.sentiment.overall === 'positive' ? 'text-green-400' :
                              newsData.sentiment.overall === 'negative' ? 'text-red-400' :
                              'text-yellow-400'
                            }`}>
                              {newsData.sentiment.overall === 'positive' ? '😊' :
                               newsData.sentiment.overall === 'negative' ? '😟' : '😐'}
                            </div>
                            <div className="text-lg font-medium text-white capitalize">
                              {newsData.sentiment.overall}
                            </div>
                            <div className="text-sm text-slate-400">Overall Sentiment</div>
                            <div className="text-xs text-slate-500 mt-1">
                              Score: {(newsData.sentiment.score * 100).toFixed(0)}%
                            </div>
                          </div>

                          {/* Sentiment Distribution */}
                          <div className="space-y-3">
                            <h4 className="text-white font-medium">Sentiment Breakdown</h4>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <span className="text-green-400 text-sm">Positive</span>
                                <span className="text-green-400 font-medium">{newsData.sentiment.distribution.positive}%</span>
                              </div>
                              <div className="w-full bg-slate-700 rounded-full h-2">
                                <div
                                  className="bg-green-400 h-2 rounded-full"
                                  style={{ width: `${newsData.sentiment.distribution.positive}%` }}
                                ></div>
                              </div>

                              <div className="flex items-center justify-between">
                                <span className="text-yellow-400 text-sm">Neutral</span>
                                <span className="text-yellow-400 font-medium">{newsData.sentiment.distribution.neutral}%</span>
                              </div>
                              <div className="w-full bg-slate-700 rounded-full h-2">
                                <div
                                  className="bg-yellow-400 h-2 rounded-full"
                                  style={{ width: `${newsData.sentiment.distribution.neutral}%` }}
                                ></div>
                              </div>

                              <div className="flex items-center justify-between">
                                <span className="text-red-400 text-sm">Negative</span>
                                <span className="text-red-400 font-medium">{newsData.sentiment.distribution.negative}%</span>
                              </div>
                              <div className="w-full bg-slate-700 rounded-full h-2">
                                <div
                                  className="bg-red-400 h-2 rounded-full"
                                  style={{ width: `${newsData.sentiment.distribution.negative}%` }}
                                ></div>
                              </div>
                            </div>
                          </div>

                          {/* Key Topics */}
                          <div className="space-y-3">
                            <h4 className="text-white font-medium">Trending Topics</h4>
                            <div className="flex flex-wrap gap-2">
                              {newsData.keyTopics.map((topic: string, index: number) => (
                                <Badge
                                  key={index}
                                  variant="outline"
                                  className="border-blue-500/30 text-blue-300"
                                >
                                  {topic}
                                </Badge>
                              ))}
                            </div>
                            <div className="text-xs text-slate-500 mt-2">
                              Based on {newsData.articles.length} recent articles
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* News Articles */}
                  <div className="space-y-4">
                    <h3 className="text-xl font-bold text-white">Latest News & Updates</h3>

                    {isLoadingNews ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {[...Array(6)].map((_, index) => (
                          <Card key={index} className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                            <CardContent className="p-4">
                              <div className="animate-pulse space-y-3">
                                <div className="h-4 bg-slate-700 rounded w-3/4"></div>
                                <div className="h-3 bg-slate-700 rounded w-full"></div>
                                <div className="h-3 bg-slate-700 rounded w-2/3"></div>
                                <div className="flex justify-between">
                                  <div className="h-3 bg-slate-700 rounded w-1/4"></div>
                                  <div className="h-3 bg-slate-700 rounded w-1/4"></div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : newsData ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {newsData.articles.map((article: any, index: number) => (
                          <Card key={index} className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-colors">
                            <CardContent className="p-4 space-y-3">
                              {/* Article Header */}
                              <div className="flex items-start justify-between gap-2">
                                <Badge
                                  variant="outline"
                                  className={`text-xs ${
                                    article.sentiment === 'positive' ? 'border-green-500/30 text-green-300' :
                                    article.sentiment === 'negative' ? 'border-red-500/30 text-red-300' :
                                    'border-yellow-500/30 text-yellow-300'
                                  }`}
                                >
                                  {article.sentiment === 'positive' ? '📈 Positive' :
                                   article.sentiment === 'negative' ? '📉 Negative' : '📊 Neutral'}
                                </Badge>
                                <div className="text-xs text-slate-500">
                                  {Math.round(article.relevanceScore * 100)}% relevant
                                </div>
                              </div>

                              {/* Article Title */}
                              <h4 className="text-white font-medium text-sm leading-tight line-clamp-2">
                                {article.title}
                              </h4>

                              {/* Article Description */}
                              <p className="text-slate-400 text-xs leading-relaxed line-clamp-3">
                                {article.description}
                              </p>

                              {/* Article Footer */}
                              <div className="flex items-center justify-between text-xs">
                                <span className="text-blue-400 font-medium">{article.source}</span>
                                <span className="text-slate-500">
                                  {new Date(article.publishedAt).toLocaleDateString()}
                                </span>
                              </div>

                              {/* Read More Button */}
                              <Button
                                size="sm"
                                variant="outline"
                                className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                                onClick={() => article.url !== '#' && window.open(article.url, '_blank')}
                              >
                                <Globe className="h-3 w-3 mr-1" />
                                Read Full Article
                              </Button>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                        <CardContent className="p-8 text-center">
                          <Globe className="h-12 w-12 text-slate-500 mx-auto mb-4" />
                          <h3 className="text-white font-medium mb-2">No News Available</h3>
                          <p className="text-slate-400 text-sm">
                            Unable to fetch news for this stock. Please try again later.
                          </p>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </TabsContent>

                {/* Historical Data Tab */}
                <TabsContent value="historical" className="space-y-6">
                  <div className="text-center space-y-2 mb-6">
                    <h2 className="text-2xl font-bold text-white">Historical Analysis & Patterns</h2>
                    <p className="text-slate-300 max-w-2xl mx-auto">
                      Explore historical patterns, trends, and key events that shaped {stockData.symbol}'s performance
                    </p>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Historical Performance */}
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <BarChart3 className="h-5 w-5 text-blue-400" />
                          Historical Performance
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-slate-400">1 Month</span>
                            <span className="text-green-400 font-medium">+5.2%</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-slate-400">3 Months</span>
                            <span className="text-green-400 font-medium">+12.8%</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-slate-400">6 Months</span>
                            <span className="text-green-400 font-medium">+18.5%</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-slate-400">1 Year</span>
                            <span className="text-green-400 font-medium">+24.3%</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-slate-400">5 Years</span>
                            <span className="text-green-400 font-medium">+156.7%</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Pattern Recognition */}
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <Target className="h-5 w-5 text-purple-400" />
                          AI Pattern Recognition
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-3">
                          <div className="p-3 bg-slate-700/30 rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <CheckCircle className="h-4 w-4 text-green-400" />
                              <span className="text-green-400 font-medium">Ascending Triangle</span>
                            </div>
                            <p className="text-xs text-slate-400">Bullish pattern forming over 3 months</p>
                          </div>
                          <div className="p-3 bg-slate-700/30 rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <Eye className="h-4 w-4 text-yellow-400" />
                              <span className="text-yellow-400 font-medium">Support at $145</span>
                            </div>
                            <p className="text-xs text-slate-400">Strong support level tested 3 times</p>
                          </div>
                          <div className="p-3 bg-slate-700/30 rounded-lg">
                            <div className="flex items-center gap-2 mb-1">
                              <TrendingUp className="h-4 w-4 text-blue-400" />
                              <span className="text-blue-400 font-medium">Volume Breakout</span>
                            </div>
                            <p className="text-xs text-slate-400">Recent volume spike above average</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Key Events Timeline */}
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center gap-2">
                        <Calendar className="h-5 w-5 text-green-400" />
                        Key Events Timeline
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-start gap-4">
                          <div className="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h4 className="text-white font-medium">Q3 Earnings Beat</h4>
                              <span className="text-xs text-slate-400">2 weeks ago</span>
                            </div>
                            <p className="text-sm text-slate-400">Revenue exceeded expectations by 8%, stock gained 12%</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-4">
                          <div className="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h4 className="text-white font-medium">Product Launch</h4>
                              <span className="text-xs text-slate-400">1 month ago</span>
                            </div>
                            <p className="text-sm text-slate-400">New product line announcement, positive market reaction</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-4">
                          <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2"></div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <h4 className="text-white font-medium">Analyst Upgrade</h4>
                              <span className="text-xs text-slate-400">6 weeks ago</span>
                            </div>
                            <p className="text-sm text-slate-400">Major investment bank upgraded to "Buy" rating</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Learning Hub Tab */}
                <TabsContent value="learning" className="space-y-6">
                  <div className="text-center space-y-2 mb-6">
                    <h2 className="text-2xl font-bold text-white">Learning Hub for {stockData.symbol}</h2>
                    <p className="text-slate-300 max-w-2xl mx-auto">
                      Educational resources, news, and insights to help you understand this stock better
                    </p>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Educational Content */}
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <BookOpen className="h-5 w-5 text-blue-400" />
                          Learn About This Stock
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-3">
                          <Button
                            variant="outline"
                            className="w-full justify-start border-slate-600 text-slate-300"
                            onClick={() => router.push(`/chat?query=${encodeURIComponent(`Explain the business model of ${stockData.symbol} in simple terms`)}`)}
                          >
                            <BookOpen className="h-4 w-4 mr-2" />
                            Understanding the Business
                          </Button>
                          <Button
                            variant="outline"
                            className="w-full justify-start border-slate-600 text-slate-300"
                            onClick={() => router.push(`/chat?query=${encodeURIComponent(`What are the key financial metrics to watch for ${stockData.symbol}?`)}`)}
                          >
                            <BarChart3 className="h-4 w-4 mr-2" />
                            Key Financial Metrics
                          </Button>
                          <Button
                            variant="outline"
                            className="w-full justify-start border-slate-600 text-slate-300"
                            onClick={() => router.push(`/chat?query=${encodeURIComponent(`What are the main risks and opportunities for ${stockData.symbol}?`)}`)}
                          >
                            <AlertTriangle className="h-4 w-4 mr-2" />
                            Risks & Opportunities
                          </Button>
                          <Button
                            variant="outline"
                            className="w-full justify-start border-slate-600 text-slate-300"
                            onClick={() => router.push(`/chat?query=${encodeURIComponent(`How does ${stockData.symbol} compare to its competitors?`)}`)}
                          >
                            <Target className="h-4 w-4 mr-2" />
                            Competitive Analysis
                          </Button>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Latest News */}
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <Globe className="h-5 w-5 text-green-400" />
                          Latest News & Events
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-3">
                          <div className="p-3 bg-slate-700/30 rounded-lg">
                            <h4 className="text-white font-medium text-sm mb-1">Q3 Earnings Report Released</h4>
                            <p className="text-xs text-slate-400 mb-2">Strong revenue growth and positive guidance</p>
                            <span className="text-xs text-blue-400">2 hours ago</span>
                          </div>
                          <div className="p-3 bg-slate-700/30 rounded-lg">
                            <h4 className="text-white font-medium text-sm mb-1">Analyst Coverage Update</h4>
                            <p className="text-xs text-slate-400 mb-2">Price target raised to $175 by Goldman Sachs</p>
                            <span className="text-xs text-blue-400">1 day ago</span>
                          </div>
                          <div className="p-3 bg-slate-700/30 rounded-lg">
                            <h4 className="text-white font-medium text-sm mb-1">Industry Outlook</h4>
                            <p className="text-xs text-slate-400 mb-2">Sector showing strong momentum this quarter</p>
                            <span className="text-xs text-blue-400">3 days ago</span>
                          </div>
                        </div>
                        <Button
                          className="w-full bg-blue-600 hover:bg-blue-700"
                          onClick={() => router.push(`/news?filter=${stockData.symbol}`)}
                        >
                          <Globe className="h-4 w-4 mr-2" />
                          View All News
                        </Button>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Learning Modules */}
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="text-white flex items-center gap-2">
                        <GraduationCap className="h-5 w-5 text-purple-400" />
                        Recommended Learning Modules
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="p-4 bg-slate-700/30 rounded-lg">
                          <h4 className="text-white font-medium mb-2">Stock Valuation</h4>
                          <p className="text-xs text-slate-400 mb-3">Learn how to value stocks using P/E, DCF, and other methods</p>
                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full border-slate-600 text-slate-300"
                            onClick={() => router.push('/learning?topic=stock-valuation')}
                          >
                            Start Learning
                          </Button>
                        </div>
                        <div className="p-4 bg-slate-700/30 rounded-lg">
                          <h4 className="text-white font-medium mb-2">Technical Analysis</h4>
                          <p className="text-xs text-slate-400 mb-3">Master chart patterns, indicators, and trading signals</p>
                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full border-slate-600 text-slate-300"
                            onClick={() => router.push('/learning?topic=technical-analysis')}
                          >
                            Start Learning
                          </Button>
                        </div>
                        <div className="p-4 bg-slate-700/30 rounded-lg">
                          <h4 className="text-white font-medium mb-2">Risk Management</h4>
                          <p className="text-xs text-slate-400 mb-3">Understand how to manage investment risks effectively</p>
                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full border-slate-600 text-slate-300"
                            onClick={() => router.push('/learning?topic=risk-management')}
                          >
                            Start Learning
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
