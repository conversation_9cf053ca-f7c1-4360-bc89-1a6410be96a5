// Market Insights API Endpoint

import { NextRequest, NextResponse } from 'next/server';
import { MarketInsightEngineMain } from '@/lib/market-insights/market-insight-engine';
import { MarketData, FilterOptions } from '@/lib/market-insights/types';

// Initialize the engine
const insightEngine = new MarketInsightEngineMain(process.env.GEMINI_API_KEY);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse filters from query parameters
    const filters: FilterOptions = {
      region: searchParams.get('region') as any || 'all',
      sector: searchParams.get('sector') || undefined,
      timeframe: searchParams.get('timeframe') as any || '1D',
      assetClass: searchParams.get('assetClass') as any || undefined,
      sentiment: searchParams.get('sentiment') as any || undefined
    };
    
    // Check for cached results first
    const cached = insightEngine.getCachedInsights();
    if (cached && !searchParams.get('refresh')) {
      return NextResponse.json({
        success: true,
        data: cached,
        cached: true,
        timestamp: new Date().toISOString()
      });
    }
    
    // Generate fresh insights
    const marketData = await fetchMarketData();
    const historicalData = await fetchHistoricalData(marketData);
    const newsData = await fetchNewsData();
    
    const insights = await insightEngine.generateMarketInsights(
      marketData,
      historicalData,
      newsData,
      filters
    );
    
    return NextResponse.json({
      success: true,
      data: insights,
      cached: false,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Market insights API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate market insights',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { question, context } = body;
    
    if (!question) {
      return NextResponse.json(
        { success: false, error: 'Question is required' },
        { status: 400 }
      );
    }
    
    // This would integrate with the predictive prompting system
    // For now, return a placeholder response
    const response = "This feature requires Gemini API integration for AI-powered responses.";
    
    return NextResponse.json({
      success: true,
      data: {
        question,
        answer: response,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('Question answering error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to answer question',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// Mock data functions (replace with real data sources)
async function fetchMarketData(): Promise<MarketData[]> {
  // In production, this would fetch from real APIs like Alpha Vantage, Yahoo Finance, etc.
  return [
    {
      symbol: 'RELIANCE',
      name: 'Reliance Industries Ltd',
      price: 2456.75,
      change: 45.20,
      changePercent: 1.87,
      volume: 2500000,
      volumeChange: 125,
      marketCap: **************,
      sector: 'Energy',
      exchange: 'NSE'
    },
    {
      symbol: 'TCS',
      name: 'Tata Consultancy Services',
      price: 3567.25,
      change: -23.45,
      changePercent: -0.65,
      volume: 1800000,
      volumeChange: 95,
      marketCap: **************,
      sector: 'IT',
      exchange: 'NSE'
    },
    {
      symbol: 'HDFCBANK',
      name: 'HDFC Bank Ltd',
      price: 1634.85,
      change: 12.30,
      changePercent: 0.76,
      volume: 3200000,
      volumeChange: 110,
      marketCap: **************,
      sector: 'Banking',
      exchange: 'NSE'
    },
    {
      symbol: 'INFY',
      name: 'Infosys Ltd',
      price: 1456.90,
      change: 18.75,
      changePercent: 1.30,
      volume: 2100000,
      volumeChange: 140,
      marketCap: *************,
      sector: 'IT',
      exchange: 'NSE'
    },
    {
      symbol: 'ICICIBANK',
      name: 'ICICI Bank Ltd',
      price: 945.60,
      change: -8.90,
      changePercent: -0.93,
      volume: 4500000,
      volumeChange: 160,
      marketCap: *************,
      sector: 'Banking',
      exchange: 'NSE'
    },
    {
      symbol: 'HINDUNILVR',
      name: 'Hindustan Unilever Ltd',
      price: 2678.45,
      change: 34.20,
      changePercent: 1.29,
      volume: 890000,
      volumeChange: 85,
      marketCap: *************,
      sector: 'FMCG',
      exchange: 'NSE'
    },
    {
      symbol: 'ITC',
      name: 'ITC Ltd',
      price: 456.75,
      change: -5.25,
      changePercent: -1.14,
      volume: 5600000,
      volumeChange: 180,
      marketCap: *************,
      sector: 'FMCG',
      exchange: 'NSE'
    },
    {
      symbol: 'SBIN',
      name: 'State Bank of India',
      price: 567.80,
      change: 15.60,
      changePercent: 2.83,
      volume: 8900000,
      volumeChange: 220,
      marketCap: *************,
      sector: 'Banking',
      exchange: 'NSE'
    },
    {
      symbol: 'BHARTIARTL',
      name: 'Bharti Airtel Ltd',
      price: 876.45,
      change: -12.35,
      changePercent: -1.39,
      volume: 3400000,
      volumeChange: 130,
      marketCap: *************,
      sector: 'Telecom',
      exchange: 'NSE'
    },
    {
      symbol: 'KOTAKBANK',
      name: 'Kotak Mahindra Bank',
      price: 1789.60,
      change: 22.40,
      changePercent: 1.27,
      volume: 1200000,
      volumeChange: 105,
      marketCap: *************,
      sector: 'Banking',
      exchange: 'NSE'
    },
    // Crypto assets
    {
      symbol: 'BTC',
      name: 'Bitcoin',
      price: 43250.00,
      change: 1250.00,
      changePercent: 2.98,
      volume: ***********,
      volumeChange: 145,
      marketCap: ************,
      sector: 'Cryptocurrency',
      exchange: 'CRYPTO'
    },
    {
      symbol: 'ETH',
      name: 'Ethereum',
      price: 2650.00,
      change: -85.50,
      changePercent: -3.12,
      volume: ***********,
      volumeChange: 165,
      marketCap: ************,
      sector: 'Cryptocurrency',
      exchange: 'CRYPTO'
    },
    {
      symbol: 'SOL',
      name: 'Solana',
      price: 98.75,
      change: 4.25,
      changePercent: 4.50,
      volume: **********,
      volumeChange: 190,
      marketCap: ***********,
      sector: 'Cryptocurrency',
      exchange: 'CRYPTO'
    }
  ];
}

async function fetchHistoricalData(marketData: MarketData[]): Promise<Map<string, number[]>> {
  const historicalData = new Map<string, number[]>();
  
  // Generate mock historical data for each asset
  for (const asset of marketData) {
    const prices: number[] = [];
    let currentPrice = asset.price;
    
    // Generate 50 days of historical prices
    for (let i = 49; i >= 0; i--) {
      const randomChange = (Math.random() - 0.5) * 0.04; // ±2% random change
      currentPrice = currentPrice * (1 + randomChange);
      prices.unshift(currentPrice);
    }
    
    historicalData.set(asset.symbol, prices);
  }
  
  return historicalData;
}

async function fetchNewsData(): Promise<any[]> {
  // Mock news data
  return [
    {
      title: "TCS Reports Strong Q3 Earnings, Beats Estimates",
      description: "Tata Consultancy Services reported better-than-expected quarterly results with revenue growth of 12% YoY",
      symbols: ['TCS'],
      sentiment: 'positive',
      category: 'earnings',
      timestamp: new Date().toISOString()
    },
    {
      title: "RBI Maintains Repo Rate at 6.5%, Signals Cautious Stance",
      description: "Reserve Bank of India keeps interest rates unchanged, citing inflation concerns and global uncertainties",
      symbols: ['HDFCBANK', 'ICICIBANK', 'SBIN', 'KOTAKBANK'],
      sentiment: 'neutral',
      category: 'macro',
      timestamp: new Date().toISOString()
    },
    {
      title: "Reliance Industries Announces Major Green Energy Investment",
      description: "RIL commits $10 billion investment in renewable energy and green hydrogen projects over next 5 years",
      symbols: ['RELIANCE'],
      sentiment: 'positive',
      category: 'company',
      timestamp: new Date().toISOString()
    },
    {
      title: "IT Sector Faces Headwinds from Global Economic Slowdown",
      description: "Indian IT companies may see reduced demand as global clients cut technology spending amid recession fears",
      symbols: ['TCS', 'INFY', 'WIPRO', 'HCLTECH'],
      sentiment: 'negative',
      category: 'sector',
      timestamp: new Date().toISOString()
    },
    {
      title: "Bitcoin Rallies on Institutional Adoption News",
      description: "Major financial institutions announce increased Bitcoin allocation, driving cryptocurrency prices higher",
      symbols: ['BTC', 'ETH'],
      sentiment: 'positive',
      category: 'crypto',
      timestamp: new Date().toISOString()
    }
  ];
}
