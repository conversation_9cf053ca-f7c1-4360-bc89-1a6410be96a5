import { NextRequest, NextResponse } from 'next/server';

// Multi-source API configuration for Indian & US markets
const ALPHA_VANTAGE_API_KEY = process.env.ALPHA_VANTAGE_API_KEY || 'demo';
const TWELVE_DATA_API_KEY = process.env.TWELVE_DATA_API_KEY || 'demo';
const FINNHUB_API_KEY = process.env.FINNHUB_API_KEY || 'demo';
const EXCHANGE_RATE_API_KEY = process.env.EXCHANGE_RATE_API_KEY || 'demo';

// API Base URLs
const ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query';
const TWELVE_DATA_BASE_URL = 'https://api.twelvedata.com';
const YAHOO_FINANCE_BASE_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';
const FINNHUB_BASE_URL = 'https://finnhub.io/api/v1';
const EXCHANGE_RATE_BASE_URL = 'https://v6.exchangerate-api.com/v6';

interface StockQuote {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
  high52Week: number;
  low52Week: number;
  pe: number;
  dividend: number;
  lastUpdate: string;
  market: 'US' | 'IN';
  currency: 'USD' | 'INR';
  exchange: string;
  priceInUSD?: number;
  priceInINR?: number;
  exchangeRate?: number;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { ticker: string } }
) {
  try {
    const ticker = params.ticker.toUpperCase();
    const { searchParams } = new URL(request.url);
    const currency = searchParams.get('currency') || 'USD';

    // Detect market based on ticker format
    const market = detectMarket(ticker);

    let stockData: StockQuote | null = null;

    if (market === 'IN') {
      // Try Indian market APIs
      stockData = await fetchIndianStock(ticker);
      if (!stockData) {
        stockData = await fetchFromYahooFinance(ticker, market);
      }
    } else {
      // Try US market APIs
      stockData = await fetchFromAlphaVantage(ticker);
      if (!stockData) {
        stockData = await fetchFromYahooFinance(ticker, market);
      }
      if (!stockData) {
        stockData = await fetchFromTwelveData(ticker);
      }
      if (!stockData) {
        stockData = await fetchFromFinnhub(ticker);
      }
    }

    // If all APIs fail, return mock data
    if (!stockData) {
      stockData = generateMockData(ticker, market);
    }

    // Add currency conversion
    if (stockData && currency !== stockData.currency) {
      stockData = await addCurrencyConversion(stockData, currency);
    }

    return NextResponse.json(stockData);
  } catch (error) {
    console.error('Error fetching stock data:', error);

    // Return mock data as fallback
    const mockData = generateMockData(params.ticker.toUpperCase(), 'US');
    return NextResponse.json(mockData);
  }
}

function detectMarket(ticker: string): 'US' | 'IN' {
  // Indian stock patterns
  if (ticker.endsWith('.NS') || ticker.endsWith('.BO') || ticker.endsWith('.BSE')) {
    return 'IN';
  }

  // Common Indian stock symbols
  const indianStocks = ['RELIANCE', 'TCS', 'INFY', 'HDFCBANK', 'ICICIBANK', 'SBIN', 'ITC', 'LT', 'BHARTIARTL', 'ASIANPAINT'];
  if (indianStocks.includes(ticker.replace(/\.(NS|BO|BSE)$/, ''))) {
    return 'IN';
  }

  // Default to US market
  return 'US';
}

async function fetchIndianStock(ticker: string): Promise<StockQuote | null> {
  try {
    // Try Twelve Data for Indian stocks (supports NSE/BSE)
    const cleanTicker = ticker.replace(/\.(NS|BO|BSE)$/, '');
    const exchange = ticker.includes('.NS') ? 'NSE' : ticker.includes('.BO') ? 'BSE' : 'NSE';

    const response = await fetch(
      `${TWELVE_DATA_BASE_URL}/quote?symbol=${cleanTicker}&exchange=${exchange}&apikey=${TWELVE_DATA_API_KEY}`
    );

    if (!response.ok) {
      throw new Error('Twelve Data API error');
    }

    const data = await response.json();

    // Check for API errors
    if (data.status === 'error') {
      console.log('Twelve Data error:', data.message || 'Unknown error');
      throw new Error(data.message || 'Twelve Data API error');
    }

    if (!data.symbol && !data.close) {
      console.log('Twelve Data response:', data);
      throw new Error('No data available');
    }

    const price = parseFloat(data.close || '0');
    const change = parseFloat(data.change || '0');
    const changePercent = parseFloat(data.percent_change || '0');

    return {
      symbol: ticker,
      name: data.name || `${cleanTicker} Corporation`,
      price: price,
      change: change,
      changePercent: changePercent,
      volume: parseInt(data.volume || '0'),
      marketCap: parseInt(data.market_cap || '0'),
      high52Week: parseFloat(data.fifty_two_week?.high || (price * 1.2).toString()),
      low52Week: parseFloat(data.fifty_two_week?.low || (price * 0.8).toString()),
      pe: parseFloat(data.pe_ratio || '25'),
      dividend: parseFloat(data.dividend_yield || '0'),
      lastUpdate: new Date().toISOString(),
      market: 'IN',
      currency: 'INR',
      exchange: exchange
    };
  } catch (error) {
    console.error('Indian stock fetch error:', error);
    return null;
  }
}

async function fetchFromFinnhub(ticker: string): Promise<StockQuote | null> {
  try {
    // Finnhub provides free real-time data
    const response = await fetch(
      `${FINNHUB_BASE_URL}/quote?symbol=${ticker}&token=${FINNHUB_API_KEY}`
    );

    if (!response.ok) {
      throw new Error('Finnhub API error');
    }

    const data = await response.json();

    if (!data.c || data.c === 0) {
      console.log('Finnhub response:', data);
      throw new Error('No data from Finnhub');
    }

    const price = data.c; // Current price
    const change = data.d; // Change
    const changePercent = data.dp; // Change percent

    return {
      symbol: ticker,
      name: `${ticker} Corporation`,
      price: parseFloat(price.toFixed(2)),
      change: parseFloat(change.toFixed(2)),
      changePercent: parseFloat(changePercent.toFixed(2)),
      volume: 0, // Finnhub doesn't provide volume in quote endpoint
      marketCap: 0,
      high52Week: data.h || price * 1.2,
      low52Week: data.l || price * 0.8,
      pe: 25,
      dividend: 0,
      lastUpdate: new Date().toISOString(),
      market: 'US',
      currency: 'USD',
      exchange: 'NASDAQ'
    };
  } catch (error) {
    console.error('Finnhub fetch error:', error);
    return null;
  }
}

async function fetchFromTwelveData(ticker: string): Promise<StockQuote | null> {
  try {
    const response = await fetch(
      `${TWELVE_DATA_BASE_URL}/quote?symbol=${ticker}&apikey=${TWELVE_DATA_API_KEY}`
    );

    if (!response.ok) {
      throw new Error('Twelve Data API error');
    }

    const data = await response.json();

    if (data.status === 'error' || !data.symbol) {
      throw new Error('No data available');
    }

    const price = parseFloat(data.close || '0');
    const change = parseFloat(data.change || '0');
    const changePercent = parseFloat(data.percent_change || '0');

    return {
      symbol: ticker,
      name: data.name || `${ticker} Corporation`,
      price: price,
      change: change,
      changePercent: changePercent,
      volume: parseInt(data.volume || '0'),
      marketCap: parseInt(data.market_cap || '0'),
      high52Week: parseFloat(data.fifty_two_week?.high || (price * 1.2).toString()),
      low52Week: parseFloat(data.fifty_two_week?.low || (price * 0.8).toString()),
      pe: parseFloat(data.pe_ratio || '25'),
      dividend: parseFloat(data.dividend_yield || '0'),
      lastUpdate: new Date().toISOString(),
      market: 'US',
      currency: 'USD',
      exchange: 'NASDAQ'
    };
  } catch (error) {
    console.error('Twelve Data fetch error:', error);
    return null;
  }
}

async function fetchFromAlphaVantage(ticker: string): Promise<StockQuote | null> {
  try {
    // Get quote data
    const quoteResponse = await fetch(
      `${ALPHA_VANTAGE_BASE_URL}?function=GLOBAL_QUOTE&symbol=${ticker}&apikey=${ALPHA_VANTAGE_API_KEY}`
    );
    
    if (!quoteResponse.ok) {
      throw new Error('Alpha Vantage API error');
    }
    
    const quoteData = await quoteResponse.json();

    // Check for API limit or error messages
    if (quoteData['Error Message'] || quoteData['Note']) {
      console.log('Alpha Vantage API limit or error:', quoteData['Error Message'] || quoteData['Note']);
      throw new Error('Alpha Vantage API limit reached or error');
    }

    const quote = quoteData['Global Quote'];

    if (!quote || Object.keys(quote).length === 0) {
      console.log('Alpha Vantage response:', quoteData);
      throw new Error('No quote data available');
    }
    
    // Get company overview for additional data
    const overviewResponse = await fetch(
      `${ALPHA_VANTAGE_BASE_URL}?function=OVERVIEW&symbol=${ticker}&apikey=${ALPHA_VANTAGE_API_KEY}`
    );
    
    let overview = {};
    if (overviewResponse.ok) {
      overview = await overviewResponse.json();
    }
    
    const price = parseFloat(quote['05. price'] || '0');
    const change = parseFloat(quote['09. change'] || '0');
    const changePercent = parseFloat(quote['10. change percent']?.replace('%', '') || '0');
    
    return {
      symbol: ticker,
      name: overview['Name'] || `${ticker} Corporation`,
      price: price,
      change: change,
      changePercent: changePercent,
      volume: parseInt(quote['06. volume'] || '0'),
      marketCap: parseInt(overview['MarketCapitalization'] || '0'),
      high52Week: parseFloat(overview['52WeekHigh'] || (price * 1.2).toString()),
      low52Week: parseFloat(overview['52WeekLow'] || (price * 0.8).toString()),
      pe: parseFloat(overview['PERatio'] || '25'),
      dividend: parseFloat(overview['DividendYield'] || '0'),
      lastUpdate: new Date().toISOString(),
      market: 'US',
      currency: 'USD',
      exchange: 'NASDAQ'
    };
  } catch (error) {
    console.error('Alpha Vantage fetch error:', error);
    return null;
  }
}

async function fetchFromYahooFinance(ticker: string, market: 'US' | 'IN' = 'US'): Promise<StockQuote | null> {
  try {
    // Add proper headers to avoid blocking
    const response = await fetch(`${YAHOO_FINANCE_BASE_URL}/${ticker}`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
      }
    });

    if (!response.ok) {
      console.log('Yahoo Finance HTTP error:', response.status, response.statusText);
      throw new Error(`Yahoo Finance API error: ${response.status}`);
    }

    const data = await response.json();

    // Check for Yahoo Finance error structure
    if (data.chart?.error) {
      console.log('Yahoo Finance API error:', data.chart.error);
      throw new Error('Yahoo Finance API error');
    }

    const result = data.chart?.result?.[0];

    if (!result) {
      console.log('Yahoo Finance response:', data);
      throw new Error('No data from Yahoo Finance');
    }
    
    const meta = result.meta;
    const quote = result.indicators?.quote?.[0];
    
    if (!meta || !quote) {
      throw new Error('Invalid Yahoo Finance data structure');
    }
    
    const price = meta.regularMarketPrice || 0;
    const previousClose = meta.previousClose || price;
    const change = price - previousClose;
    const changePercent = (change / previousClose) * 100;
    
    return {
      symbol: ticker,
      name: meta.longName || `${ticker} Corporation`,
      price: price,
      change: change,
      changePercent: changePercent,
      volume: meta.regularMarketVolume || 0,
      marketCap: meta.marketCap || 0,
      high52Week: meta.fiftyTwoWeekHigh || price * 1.2,
      low52Week: meta.fiftyTwoWeekLow || price * 0.8,
      pe: meta.trailingPE || 25,
      dividend: meta.dividendYield || 0,
      lastUpdate: new Date().toISOString(),
      market: market,
      currency: market === 'IN' ? 'INR' : 'USD',
      exchange: market === 'IN' ? 'NSE' : 'NASDAQ'
    };
  } catch (error) {
    console.error('Yahoo Finance fetch error:', error);
    return null;
  }
}

async function addCurrencyConversion(stockData: StockQuote, targetCurrency: string): Promise<StockQuote> {
  try {
    if (stockData.currency === targetCurrency) {
      return stockData;
    }

    // Get exchange rate
    const exchangeRate = await getExchangeRate(stockData.currency, targetCurrency);

    if (exchangeRate) {
      const convertedPrice = stockData.price * exchangeRate;
      const convertedChange = stockData.change * exchangeRate;

      return {
        ...stockData,
        priceInUSD: targetCurrency === 'USD' ? convertedPrice : stockData.price,
        priceInINR: targetCurrency === 'INR' ? convertedPrice : stockData.price,
        exchangeRate: exchangeRate
      };
    }

    return stockData;
  } catch (error) {
    console.error('Currency conversion error:', error);
    return stockData;
  }
}

async function getExchangeRate(fromCurrency: string, toCurrency: string): Promise<number | null> {
  try {
    // Use a free exchange rate API
    const response = await fetch(
      `${EXCHANGE_RATE_BASE_URL}/${EXCHANGE_RATE_API_KEY}/pair/${fromCurrency}/${toCurrency}`
    );

    if (response.ok) {
      const data = await response.json();
      return data.conversion_rate;
    }

    // Fallback to approximate rates if API fails
    if (fromCurrency === 'USD' && toCurrency === 'INR') {
      return 83.0; // Approximate USD to INR
    } else if (fromCurrency === 'INR' && toCurrency === 'USD') {
      return 0.012; // Approximate INR to USD
    }

    return null;
  } catch (error) {
    console.error('Exchange rate fetch error:', error);
    return null;
  }
}

function generateMockData(ticker: string, market: 'US' | 'IN' = 'US'): StockQuote {
  // Generate realistic mock data based on actual stock patterns
  const stockPrices = {
    // US Stocks
    'AAPL': { base: 189, volatility: 0.02 },
    'MSFT': { base: 378, volatility: 0.015 },
    'GOOGL': { base: 138, volatility: 0.025 },
    'AMZN': { base: 145, volatility: 0.03 },
    'TSLA': { base: 248, volatility: 0.05 },
    'NVDA': { base: 875, volatility: 0.04 },
    'META': { base: 325, volatility: 0.03 },
    'NFLX': { base: 445, volatility: 0.025 },

    // Indian Stocks (in INR)
    'RELIANCE': { base: 2450, volatility: 0.02 },
    'TCS': { base: 3650, volatility: 0.015 },
    'INFY': { base: 1580, volatility: 0.02 },
    'HDFCBANK': { base: 1650, volatility: 0.025 },
    'ICICIBANK': { base: 950, volatility: 0.03 },
    'SBIN': { base: 580, volatility: 0.035 },
    'ITC': { base: 420, volatility: 0.02 },
    'LT': { base: 3250, volatility: 0.025 }
  };

  const cleanTicker = ticker.replace(/\.(NS|BO|BSE)$/, '');
  const stockInfo = stockPrices[cleanTicker as keyof typeof stockPrices];

  const basePrice = stockInfo ? stockInfo.base : (market === 'IN' ? 1500 : 150);
  const volatility = stockInfo ? stockInfo.volatility : 0.02;

  const change = (Math.random() - 0.5) * basePrice * volatility * 2;
  const changePercent = (change / basePrice) * 100;

  const currentPrice = basePrice + change;

  return {
    symbol: ticker,
    name: market === 'IN' ? `${cleanTicker} Limited` : `${cleanTicker} Corporation`,
    price: parseFloat(currentPrice.toFixed(2)),
    change: parseFloat(change.toFixed(2)),
    changePercent: parseFloat(changePercent.toFixed(2)),
    volume: Math.floor(Math.random() * ********) + 1000000,
    marketCap: Math.floor(Math.random() * *********0000) + *********00,
    high52Week: parseFloat((basePrice * (1.15 + Math.random() * 0.25)).toFixed(2)),
    low52Week: parseFloat((basePrice * (0.75 + Math.random() * 0.15)).toFixed(2)),
    pe: parseFloat((15 + Math.random() * 25).toFixed(1)),
    dividend: parseFloat((Math.random() * 3).toFixed(2)),
    lastUpdate: new Date().toISOString(),
    market: market,
    currency: market === 'IN' ? 'INR' : 'USD',
    exchange: market === 'IN' ? 'NSE' : 'NASDAQ'
  };
}
