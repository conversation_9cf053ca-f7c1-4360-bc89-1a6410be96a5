import { NextRequest, NextResponse } from 'next/server';

// Alpha Vantage API configuration
const ALPHA_VANTAGE_API_KEY = process.env.ALPHA_VANTAGE_API_KEY || 'demo';
const ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query';

// Fallback to Yahoo Finance API if Alpha Vantage fails
const YAHOO_FINANCE_BASE_URL = 'https://query1.finance.yahoo.com/v8/finance/chart';

interface StockQuote {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
  high52Week: number;
  low52Week: number;
  pe: number;
  dividend: number;
  lastUpdate: string;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { ticker: string } }
) {
  try {
    const ticker = params.ticker.toUpperCase();
    
    // Try Alpha Vantage first
    let stockData = await fetchFromAlphaVantage(ticker);
    
    // If Alpha Vantage fails, try Yahoo Finance
    if (!stockData) {
      stockData = await fetchFromYahooFinance(ticker);
    }
    
    // If both fail, return mock data for development
    if (!stockData) {
      stockData = generateMockData(ticker);
    }
    
    return NextResponse.json(stockData);
  } catch (error) {
    console.error('Error fetching stock data:', error);
    
    // Return mock data as fallback
    const mockData = generateMockData(params.ticker.toUpperCase());
    return NextResponse.json(mockData);
  }
}

async function fetchFromAlphaVantage(ticker: string): Promise<StockQuote | null> {
  try {
    // Get quote data
    const quoteResponse = await fetch(
      `${ALPHA_VANTAGE_BASE_URL}?function=GLOBAL_QUOTE&symbol=${ticker}&apikey=${ALPHA_VANTAGE_API_KEY}`
    );
    
    if (!quoteResponse.ok) {
      throw new Error('Alpha Vantage API error');
    }
    
    const quoteData = await quoteResponse.json();
    const quote = quoteData['Global Quote'];
    
    if (!quote || Object.keys(quote).length === 0) {
      throw new Error('No quote data available');
    }
    
    // Get company overview for additional data
    const overviewResponse = await fetch(
      `${ALPHA_VANTAGE_BASE_URL}?function=OVERVIEW&symbol=${ticker}&apikey=${ALPHA_VANTAGE_API_KEY}`
    );
    
    let overview = {};
    if (overviewResponse.ok) {
      overview = await overviewResponse.json();
    }
    
    const price = parseFloat(quote['05. price'] || '0');
    const change = parseFloat(quote['09. change'] || '0');
    const changePercent = parseFloat(quote['10. change percent']?.replace('%', '') || '0');
    
    return {
      symbol: ticker,
      name: overview['Name'] || `${ticker} Corporation`,
      price: price,
      change: change,
      changePercent: changePercent,
      volume: parseInt(quote['06. volume'] || '0'),
      marketCap: parseInt(overview['MarketCapitalization'] || '0'),
      high52Week: parseFloat(overview['52WeekHigh'] || (price * 1.2).toString()),
      low52Week: parseFloat(overview['52WeekLow'] || (price * 0.8).toString()),
      pe: parseFloat(overview['PERatio'] || '25'),
      dividend: parseFloat(overview['DividendYield'] || '0'),
      lastUpdate: new Date().toISOString()
    };
  } catch (error) {
    console.error('Alpha Vantage fetch error:', error);
    return null;
  }
}

async function fetchFromYahooFinance(ticker: string): Promise<StockQuote | null> {
  try {
    const response = await fetch(`${YAHOO_FINANCE_BASE_URL}/${ticker}`);
    
    if (!response.ok) {
      throw new Error('Yahoo Finance API error');
    }
    
    const data = await response.json();
    const result = data.chart?.result?.[0];
    
    if (!result) {
      throw new Error('No data from Yahoo Finance');
    }
    
    const meta = result.meta;
    const quote = result.indicators?.quote?.[0];
    
    if (!meta || !quote) {
      throw new Error('Invalid Yahoo Finance data structure');
    }
    
    const price = meta.regularMarketPrice || 0;
    const previousClose = meta.previousClose || price;
    const change = price - previousClose;
    const changePercent = (change / previousClose) * 100;
    
    return {
      symbol: ticker,
      name: meta.longName || `${ticker} Corporation`,
      price: price,
      change: change,
      changePercent: changePercent,
      volume: meta.regularMarketVolume || 0,
      marketCap: meta.marketCap || 0,
      high52Week: meta.fiftyTwoWeekHigh || price * 1.2,
      low52Week: meta.fiftyTwoWeekLow || price * 0.8,
      pe: meta.trailingPE || 25,
      dividend: meta.dividendYield || 0,
      lastUpdate: new Date().toISOString()
    };
  } catch (error) {
    console.error('Yahoo Finance fetch error:', error);
    return null;
  }
}

function generateMockData(ticker: string): StockQuote {
  // Generate realistic mock data for development
  const basePrice = Math.random() * 200 + 50; // Price between $50-$250
  const change = (Math.random() - 0.5) * 10; // Change between -$5 to +$5
  const changePercent = (change / basePrice) * 100;
  
  return {
    symbol: ticker,
    name: `${ticker} Corporation`,
    price: parseFloat(basePrice.toFixed(2)),
    change: parseFloat(change.toFixed(2)),
    changePercent: parseFloat(changePercent.toFixed(2)),
    volume: Math.floor(Math.random() * 50000000) + 1000000, // 1M to 50M
    marketCap: Math.floor(Math.random() * 1000000000000) + 10000000000, // 10B to 1T
    high52Week: parseFloat((basePrice * (1.1 + Math.random() * 0.3)).toFixed(2)),
    low52Week: parseFloat((basePrice * (0.7 + Math.random() * 0.2)).toFixed(2)),
    pe: parseFloat((15 + Math.random() * 25).toFixed(1)), // P/E between 15-40
    dividend: parseFloat((Math.random() * 3).toFixed(2)), // Dividend 0-3%
    lastUpdate: new Date().toISOString()
  };
}
