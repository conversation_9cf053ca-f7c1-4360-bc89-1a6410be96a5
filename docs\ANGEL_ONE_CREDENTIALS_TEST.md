# Angel One Credentials Test Guide

## The TOTP Error Solution

The "Invalid totp" error you're seeing is because Angel One requires proper credentials and may have 2FA enabled. Here's how to fix it:

## Quick Fix: Use Demo Mode

**Immediate Solution (Recommended for Testing):**

1. **Visit**: http://localhost:3000/portfolio
2. **Keep "Demo Mode" Selected**: Don't toggle to "Real API"
3. **Select Angel One**: Click on Angel One broker card
4. **Connect**: Click "Connect to Angel One"
5. **Result**: You'll see realistic portfolio data without needing real credentials

## To Use Real Angel One API

### Step 1: Get Proper Credentials

You need these 3 pieces of information from Angel One:

1. **API Key**: From Angel One SmartAPI registration
2. **Client Code**: Your Angel One trading account ID (usually 6-8 characters)
3. **Password**: Your Angel One trading password

### Step 2: Register for Angel One SmartAPI

1. **Visit**: https://smartapi.angelbroking.com/
2. **Click "Register"**
3. **Fill Details**:
   - Name
   - Email
   - Phone
   - Angel One Client Code (your trading account)
4. **Verify Account**: They'll verify you're an existing Angel One customer
5. **Get API Key**: Once approved, you'll receive an API key

### Step 3: Check Your Account Settings

**Important**: Your Angel One account should NOT have 2FA/TOTP enabled for API access.

**To disable 2FA:**
1. Login to Angel One app/website
2. Go to Profile → Security Settings
3. Disable "Two-Factor Authentication"
4. Save changes

### Step 4: Test Your Credentials

Create a `.env.local` file in your project root:

```env
# Angel One SmartAPI Credentials
ANGEL_ONE_API_KEY=your_actual_api_key_here
ANGEL_ONE_CLIENT_CODE=your_actual_client_code_here
ANGEL_ONE_PASSWORD=your_actual_trading_password_here

# Enable real API
NEXT_PUBLIC_ENABLE_REAL_API=true
```

### Step 5: Update Portfolio Connection

The system will automatically use these credentials when you:
1. Toggle to "Real API" mode
2. Select Angel One
3. Click Connect

## Common Issues & Solutions

### Issue 1: "Invalid totp"
**Cause**: 2FA is enabled on your account
**Solution**: Disable 2FA in Angel One account settings

### Issue 2: "Invalid credentials"
**Cause**: Wrong API key, client code, or password
**Solution**: 
- Verify API key from SmartAPI dashboard
- Check client code (should be your trading account ID)
- Confirm password is your trading password

### Issue 3: "API key not found"
**Cause**: Haven't registered for SmartAPI
**Solution**: Register at https://smartapi.angelbroking.com/

### Issue 4: "Account not verified"
**Cause**: SmartAPI registration pending approval
**Solution**: Wait for Angel One to approve your API access

## Alternative: Mock Data Testing

**If you can't get real API access immediately:**

1. **Use Demo Mode**: Keep the toggle on "Demo Mode"
2. **Full Functionality**: All portfolio features work with realistic mock data
3. **No Setup Required**: Works immediately without any credentials
4. **Perfect for Testing**: Test all features before setting up real API

## Credential Format Examples

### Correct Format:
```env
ANGEL_ONE_API_KEY=SmartAPI123456789  # From SmartAPI dashboard
ANGEL_ONE_CLIENT_CODE=A12345         # Your trading account ID
ANGEL_ONE_PASSWORD=YourPassword123   # Your trading password
```

### Incorrect Format:
```env
ANGEL_ONE_API_KEY=your_api_key_here  # Placeholder text
ANGEL_ONE_CLIENT_CODE=client_code    # Placeholder text
ANGEL_ONE_PASSWORD=password          # Placeholder text
```

## Testing Steps

### Test 1: Demo Mode (Always Works)
1. Visit portfolio page
2. Keep "Demo Mode" selected
3. Select any broker
4. Connect
5. ✅ Should show realistic portfolio data

### Test 2: Real API Mode (Requires Setup)
1. Complete Steps 1-4 above
2. Toggle to "Real API" mode
3. Select Angel One
4. Connect
5. ✅ Should show your actual portfolio data

## Support

### If You Need Help:
1. **Angel One Support**: 040-47 47 47 47
2. **SmartAPI Support**: <EMAIL>
3. **Documentation**: https://smartapi.angelbroking.com/docs

### What to Ask Angel One Support:
- "I need API access for portfolio monitoring"
- "Please help me register for SmartAPI"
- "I'm getting TOTP errors with API access"
- "Can you disable 2FA for API access?"

## Current System Status

### ✅ What's Working:
- Demo mode with realistic data
- Error detection and helpful messages
- Fallback to mock data when API fails
- Clear setup instructions

### 🔧 What Needs Setup:
- Real Angel One API credentials
- SmartAPI registration
- 2FA disabled on account

**Recommendation**: Start with Demo Mode to test all features, then set up real API access when ready.
