const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testDatabase() {
  try {
    console.log('🔌 Testing database connection...')
    
    // Try to connect
    await prisma.$connect()
    console.log('✅ Database connected successfully!')
    
    // Try to query (this will fail if tables don't exist)
    try {
      const userCount = await prisma.user.count()
      console.log(`✅ Users table exists with ${userCount} users`)
    } catch (error) {
      console.log('❌ Users table does not exist:', error.message)
      
      // Try to create the table using raw SQL
      console.log('🔧 Attempting to create users table...')
      
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS "users" (
          "id" TEXT NOT NULL,
          "name" TEXT,
          "email" TEXT NOT NULL,
          "emailVerified" TIMESTAMP(3),
          "image" TEXT,
          "password" TEXT,
          "role" TEXT NOT NULL DEFAULT 'user',
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "firstName" TEXT,
          "lastName" TEXT,
          "phone" TEXT,
          "dateOfBirth" TIMESTAMP(3),
          "country" TEXT,
          "city" TEXT,
          "investmentExperience" TEXT,
          "riskTolerance" TEXT,
          "investmentGoals" TEXT[],
          "annualIncome" TEXT,
          "preferredCurrency" TEXT NOT NULL DEFAULT 'USD',
          "preferredRegion" TEXT NOT NULL DEFAULT 'global',
          "notificationsEnabled" BOOLEAN NOT NULL DEFAULT true,
          "darkMode" BOOLEAN NOT NULL DEFAULT true,
          "subscriptionTier" TEXT NOT NULL DEFAULT 'free',
          "subscriptionExpiry" TIMESTAMP(3),
          "apiCallsUsed" INTEGER NOT NULL DEFAULT 0,
          "apiCallsLimit" INTEGER NOT NULL DEFAULT 100,
          CONSTRAINT "users_pkey" PRIMARY KEY ("id")
        )
      `
      
      await prisma.$executeRaw`
        CREATE UNIQUE INDEX IF NOT EXISTS "users_email_key" ON "users"("email")
      `
      
      console.log('✅ Users table created successfully!')
      
      // Test again
      const userCount = await prisma.user.count()
      console.log(`✅ Users table now exists with ${userCount} users`)
    }
    
  } catch (error) {
    console.error('❌ Database test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testDatabase()
