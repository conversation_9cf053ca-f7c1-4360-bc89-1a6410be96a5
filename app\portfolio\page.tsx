"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Pie<PERSON>hart, 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  AlertTriangle,
  Target,
  Zap,
  Wallet,
  RefreshCw,
  Eye,
  EyeOff,
  Plus,
  Minus,
  Activity,
  Shield,
  Link,
  Building2,
  Calendar,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  Settings,
  Download,
  Bell
} from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Area, ResponsiveContainer, <PERSON>A<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>tesianGrid, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart, Pie, Cell } from 'recharts';

export default function PortfolioPage() {
  const [isConnected, setIsConnected] = useState(false);
  const [selectedBroker, setSelectedBroker] = useState('');
  const [portfolioData, setPortfolioData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showValues, setShowValues] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState('1D');
  const [refreshing, setRefreshing] = useState(false);
  const [useRealAPI, setUseRealAPI] = useState(false);

  // Supported brokers for demat account integration
  const supportedBrokers = [
    { id: 'zerodha', name: 'Zerodha', logo: '🟢', status: 'active' },
    { id: 'upstox', name: 'Upstox', logo: '🟠', status: 'active' },
    { id: 'angelone', name: 'Angel One', logo: '🔵', status: 'active' },
    { id: 'groww', name: 'Groww', logo: '🟡', status: 'active' },
    { id: 'icicidirect', name: 'ICICI Direct', logo: '🔴', status: 'active' },
    { id: 'hdfcsec', name: 'HDFC Securities', logo: '🟤', status: 'active' },
    { id: 'kotak', name: 'Kotak Securities', logo: '⚫', status: 'active' },
    { id: 'sharekhan', name: 'Sharekhan', logo: '🟣', status: 'active' }
  ];

  // Mock portfolio data (in real implementation, this would come from demat API)
  const mockPortfolioData = {
    totalValue: 2847650,
    totalInvestment: 2450000,
    totalPnL: 397650,
    totalPnLPercent: 16.23,
    dayPnL: 12450,
    dayPnLPercent: 0.44,
    holdings: [
      {
        symbol: 'RELIANCE',
        name: 'Reliance Industries Ltd',
        quantity: 150,
        avgPrice: 2340.50,
        ltp: 2456.75,
        investment: 351075,
        currentValue: 368512.50,
        pnl: 17437.50,
        pnlPercent: 4.97,
        dayChange: 2.34,
        dayChangePercent: 0.10,
        sector: 'Energy'
      },
      {
        symbol: 'TCS',
        name: 'Tata Consultancy Services',
        quantity: 75,
        avgPrice: 3245.80,
        ltp: 3567.25,
        investment: 243435,
        currentValue: 267543.75,
        pnl: 24108.75,
        pnlPercent: 9.90,
        dayChange: -15.60,
        dayChangePercent: -0.43,
        sector: 'IT'
      },
      {
        symbol: 'HDFCBANK',
        name: 'HDFC Bank Ltd',
        quantity: 200,
        avgPrice: 1567.30,
        ltp: 1634.85,
        investment: 313460,
        currentValue: 326970,
        pnl: 13510,
        pnlPercent: 4.31,
        dayChange: 8.45,
        dayChangePercent: 0.52,
        sector: 'Banking'
      },
      {
        symbol: 'INFY',
        name: 'Infosys Ltd',
        quantity: 100,
        avgPrice: 1456.75,
        ltp: 1523.40,
        investment: 145675,
        currentValue: 152340,
        pnl: 6665,
        pnlPercent: 4.57,
        dayChange: -5.20,
        dayChangePercent: -0.34,
        sector: 'IT'
      },
      {
        symbol: 'ICICIBANK',
        name: 'ICICI Bank Ltd',
        quantity: 180,
        avgPrice: 945.60,
        ltp: 1023.75,
        investment: 170208,
        currentValue: 184275,
        pnl: 14067,
        pnlPercent: 8.26,
        dayChange: 12.30,
        dayChangePercent: 1.22,
        sector: 'Banking'
      }
    ],
    sectorAllocation: [
      { name: 'Banking', value: 35.8, color: '#3B82F6' },
      { name: 'IT', value: 29.2, color: '#10B981' },
      { name: 'Energy', value: 25.7, color: '#F59E0B' },
      { name: 'Others', value: 9.3, color: '#8B5CF6' }
    ],
    performanceData: [
      { date: '2024-01-01', value: 2450000 },
      { date: '2024-02-01', value: 2523000 },
      { date: '2024-03-01', value: 2487000 },
      { date: '2024-04-01', value: 2634000 },
      { date: '2024-05-01', value: 2712000 },
      { date: '2024-06-01', value: 2689000 },
      { date: '2024-07-01', value: 2847650 }
    ]
  };

  // Portfolio management functions
  const connectBroker = async (brokerId: string) => {
    setIsLoading(true);
    setSelectedBroker(brokerId);

    try {
      const response = await fetch('/api/portfolio/connect', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          brokerId,
          credentials: {
            // Use environment variables for real credentials
            apiKey: process.env.NEXT_PUBLIC_ANGEL_ONE_API_KEY || 'demo_api_key',
            clientCode: process.env.NEXT_PUBLIC_ANGEL_ONE_CLIENT_CODE || 'demo_client_code',
            password: process.env.NEXT_PUBLIC_ANGEL_ONE_PASSWORD || 'demo_password'
          },
          useRealAPI
        })
      });

      const data = await response.json();

      if (data.success) {
        setIsConnected(true);
        setPortfolioData(data.data);

        // Show success message with data source
        console.log(`Connected to ${brokerId} - ${data.isRealData ? 'Real Data' : 'Demo Data'}`);
      } else if (data.requiresOAuth) {
        // Handle OAuth flow for brokers like Zerodha
        console.log('OAuth required, redirecting to:', data.oauthUrl);
        // In real implementation, redirect to OAuth URL
        // window.location.href = data.oauthUrl;

        // For demo, fall back to mock data
        setIsConnected(true);
        setPortfolioData(mockPortfolioData);
      } else {
        throw new Error(data.error || 'Connection failed');
      }
    } catch (error) {
      console.error('Connection failed:', error);

      // Fallback to mock data
      setIsConnected(true);
      setPortfolioData(mockPortfolioData);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshPortfolio = async () => {
    setRefreshing(true);
    
    // Simulate refresh delay
    setTimeout(() => {
      // In real implementation, this would fetch latest data from broker API
      setPortfolioData({
        ...mockPortfolioData,
        dayPnL: mockPortfolioData.dayPnL + Math.random() * 1000 - 500,
        dayPnLPercent: mockPortfolioData.dayPnLPercent + (Math.random() * 0.5 - 0.25)
      });
      setRefreshing(false);
    }, 1500);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };



  useEffect(() => {
    // Auto-refresh portfolio data every 30 seconds when connected
    if (isConnected && portfolioData) {
      const interval = setInterval(() => {
        refreshPortfolio();
      }, 30000);
      
      return () => clearInterval(interval);
    }
  }, [isConnected, portfolioData]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Portfolio Dashboard</h1>
              <p className="text-slate-300">Monitor your demat account portfolio with real-time insights</p>
            </div>
            <div className="flex items-center gap-4">
              {isConnected && (
                <>
                  <Button
                    onClick={refreshPortfolio}
                    disabled={refreshing}
                    variant="outline"
                    className="border-slate-600 text-slate-300 hover:bg-slate-700"
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                  <Button
                    onClick={() => setShowValues(!showValues)}
                    variant="outline"
                    className="border-slate-600 text-slate-300 hover:bg-slate-700"
                  >
                    {showValues ? <EyeOff className="h-4 w-4 mr-2" /> : <Eye className="h-4 w-4 mr-2" />}
                    {showValues ? 'Hide' : 'Show'} Values
                  </Button>
                </>
              )}
            </div>
          </div>

          {!isConnected ? (
            /* Broker Connection Section */
            <div className="max-w-4xl mx-auto">
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardHeader className="text-center">
                  <CardTitle className="text-white flex items-center justify-center gap-2 text-2xl">
                    <Link className="h-6 w-6 text-blue-400" />
                    Connect Your Demat Account
                  </CardTitle>
                  <CardDescription className="text-slate-300 text-lg">
                    Securely connect your broker account to monitor your portfolio in real-time
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-8">
                  {/* Security Notice */}
                  <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <Shield className="h-5 w-5 text-blue-400 mt-0.5" />
                      <div>
                        <h4 className="text-blue-300 font-medium mb-1">Secure Connection</h4>
                        <p className="text-slate-300 text-sm">
                          We use bank-grade encryption and never store your login credentials. 
                          All connections are read-only and cannot execute trades.
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Broker Selection */}
                  <div>
                    <h3 className="text-white font-medium mb-4">Select Your Broker</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {supportedBrokers.map((broker) => (
                        <Card
                          key={broker.id}
                          className={`cursor-pointer transition-all duration-200 border-2 ${
                            selectedBroker === broker.id
                              ? 'border-blue-500 bg-blue-900/20'
                              : 'border-slate-600 bg-slate-700/30 hover:border-slate-500'
                          }`}
                          onClick={() => setSelectedBroker(broker.id)}
                        >
                          <CardContent className="p-4 text-center">
                            <div className="text-2xl mb-2">{broker.logo}</div>
                            <div className="text-white font-medium text-sm">{broker.name}</div>
                            <Badge 
                              variant="outline" 
                              className="mt-2 text-xs border-green-500/30 text-green-300"
                            >
                              {broker.status}
                            </Badge>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>

                  {/* API Mode Toggle */}
                  <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-yellow-300 font-medium mb-1">API Mode</h4>
                        <p className="text-slate-300 text-sm">
                          {useRealAPI ? 'Using real broker APIs (requires setup)' : 'Using demo data for testing'}
                        </p>
                      </div>
                      <Button
                        onClick={() => setUseRealAPI(!useRealAPI)}
                        variant="outline"
                        className={`border-yellow-500/30 ${useRealAPI ? 'bg-yellow-600 text-white' : 'text-yellow-300'}`}
                      >
                        {useRealAPI ? 'Real API' : 'Demo Mode'}
                      </Button>
                    </div>
                    {useRealAPI && (
                      <div className="mt-3 text-xs text-yellow-200">
                        ⚠️ Real API requires broker credentials. See setup guide for details.
                      </div>
                    )}
                  </div>

                  {/* Connect Button */}
                  {selectedBroker && (
                    <div className="text-center">
                      <Button
                        onClick={() => connectBroker(selectedBroker)}
                        disabled={isLoading}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 text-lg"
                      >
                        {isLoading ? (
                          <>
                            <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                            Connecting...
                          </>
                        ) : (
                          <>
                            <Link className="h-5 w-5 mr-2" />
                            Connect to {supportedBrokers.find(b => b.id === selectedBroker)?.name}
                          </>
                        )}
                      </Button>
                      <p className="text-slate-400 text-sm mt-3">
                        {useRealAPI
                          ? "You'll be redirected to your broker's secure login page"
                          : "Demo mode - no real credentials required"
                        }
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          ) : (
            /* Portfolio Dashboard */
            <div className="space-y-8">
              {/* Portfolio Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-sm">Total Value</p>
                        <p className="text-2xl font-bold text-white">
                          {showValues ? formatCurrency(portfolioData.totalValue) : '••••••'}
                        </p>
                      </div>
                      <Wallet className="h-8 w-8 text-blue-400" />
                    </div>
                    <div className="mt-4 flex items-center">
                      <TrendingUp className="h-4 w-4 text-green-400 mr-1" />
                      <span className="text-green-400 text-sm font-medium">
                        +{portfolioData.totalPnLPercent.toFixed(2)}%
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-sm">Total P&L</p>
                        <p className="text-2xl font-bold text-green-400">
                          {showValues ? formatCurrency(portfolioData.totalPnL) : '••••••'}
                        </p>
                      </div>
                      <TrendingUp className="h-8 w-8 text-green-400" />
                    </div>
                    <div className="mt-4 flex items-center">
                      <span className="text-slate-400 text-sm">
                        Investment: {showValues ? formatCurrency(portfolioData.totalInvestment) : '••••••'}
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-sm">Day P&L</p>
                        <p className={`text-2xl font-bold ${portfolioData.dayPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                          {showValues ? formatCurrency(portfolioData.dayPnL) : '••••••'}
                        </p>
                      </div>
                      {portfolioData.dayPnL >= 0 ?
                        <ArrowUpRight className="h-8 w-8 text-green-400" /> :
                        <ArrowDownRight className="h-8 w-8 text-red-400" />
                      }
                    </div>
                    <div className="mt-4 flex items-center">
                      <span className={`text-sm font-medium ${portfolioData.dayPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {portfolioData.dayPnL >= 0 ? '+' : ''}{portfolioData.dayPnLPercent.toFixed(2)}%
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-sm">Holdings</p>
                        <p className="text-2xl font-bold text-white">
                          {portfolioData.holdings.length}
                        </p>
                      </div>
                      <Building2 className="h-8 w-8 text-purple-400" />
                    </div>
                    <div className="mt-4 flex items-center">
                      <span className="text-slate-400 text-sm">
                        Active positions
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Main Content Tabs */}
              <Tabs defaultValue="holdings" className="space-y-6">
                <TabsList className="bg-slate-800/50 border-slate-700">
                  <TabsTrigger value="holdings" className="data-[state=active]:bg-blue-600">
                    Holdings
                  </TabsTrigger>
                  <TabsTrigger value="performance" className="data-[state=active]:bg-blue-600">
                    Performance
                  </TabsTrigger>
                  <TabsTrigger value="analytics" className="data-[state=active]:bg-blue-600">
                    Analytics
                  </TabsTrigger>
                </TabsList>

                {/* Holdings Tab */}
                <TabsContent value="holdings" className="space-y-6">
                  <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-white">Your Holdings</CardTitle>
                        <div className="flex items-center gap-2">
                          <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
                            <SelectTrigger className="w-24 bg-slate-700 border-slate-600 text-white">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1D">1D</SelectItem>
                              <SelectItem value="1W">1W</SelectItem>
                              <SelectItem value="1M">1M</SelectItem>
                              <SelectItem value="3M">3M</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {portfolioData.holdings.map((holding: any, index: number) => (
                          <div key={index} className="p-4 bg-slate-700/30 rounded-lg border border-slate-600">
                            <div className="grid grid-cols-1 md:grid-cols-6 gap-4 items-center">
                              {/* Stock Info */}
                              <div className="md:col-span-2">
                                <div className="flex items-center gap-3">
                                  <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                                    {holding.symbol.charAt(0)}
                                  </div>
                                  <div>
                                    <h4 className="text-white font-medium">{holding.symbol}</h4>
                                    <p className="text-slate-400 text-sm">{holding.name}</p>
                                    <Badge variant="outline" className="mt-1 text-xs border-blue-500/30 text-blue-300">
                                      {holding.sector}
                                    </Badge>
                                  </div>
                                </div>
                              </div>

                              {/* Quantity & Price */}
                              <div className="text-center">
                                <p className="text-white font-medium">{holding.quantity}</p>
                                <p className="text-slate-400 text-sm">Qty</p>
                              </div>

                              {/* LTP & Day Change */}
                              <div className="text-center">
                                <p className="text-white font-medium">₹{holding.ltp.toFixed(2)}</p>
                                <p className={`text-sm ${holding.dayChange >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                  {holding.dayChange >= 0 ? '+' : ''}₹{holding.dayChange.toFixed(2)} ({holding.dayChangePercent.toFixed(2)}%)
                                </p>
                              </div>

                              {/* Investment & Current Value */}
                              <div className="text-center">
                                <p className="text-white font-medium">
                                  {showValues ? formatCurrency(holding.currentValue) : '••••••'}
                                </p>
                                <p className="text-slate-400 text-sm">
                                  Invested: {showValues ? formatCurrency(holding.investment) : '••••••'}
                                </p>
                              </div>

                              {/* P&L */}
                              <div className="text-center">
                                <p className={`font-medium ${holding.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                  {showValues ? formatCurrency(holding.pnl) : '••••••'}
                                </p>
                                <p className={`text-sm ${holding.pnl >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                                  {holding.pnl >= 0 ? '+' : ''}{holding.pnlPercent.toFixed(2)}%
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Performance Tab */}
                <TabsContent value="performance" className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Portfolio Performance Chart */}
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white">Portfolio Performance</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <AreaChart data={portfolioData.performanceData}>
                            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                            <XAxis dataKey="date" stroke="#9CA3AF" />
                            <YAxis stroke="#9CA3AF" />
                            <Tooltip
                              contentStyle={{
                                backgroundColor: '#1F2937',
                                border: '1px solid #374151',
                                borderRadius: '8px'
                              }}
                            />
                            <Area
                              type="monotone"
                              dataKey="value"
                              stroke="#3B82F6"
                              fill="#3B82F6"
                              fillOpacity={0.3}
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    {/* Sector Allocation */}
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white">Sector Allocation</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <RechartsPieChart>
                            <Pie
                              data={portfolioData.sectorAllocation}
                              cx="50%"
                              cy="50%"
                              outerRadius={80}
                              fill="#8884d8"
                              dataKey="value"
                            >
                              {portfolioData.sectorAllocation.map((entry: any, index: number) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Pie>
                            <Tooltip />
                            <Legend />
                          </RechartsPieChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Analytics Tab */}
                <TabsContent value="analytics" className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* Top Performers */}
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <TrendingUp className="h-5 w-5 text-green-400" />
                          Top Performers
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {portfolioData.holdings
                            .sort((a: any, b: any) => b.pnlPercent - a.pnlPercent)
                            .slice(0, 3)
                            .map((holding: any, index: number) => (
                              <div key={index} className="flex items-center justify-between p-2 bg-slate-700/30 rounded">
                                <span className="text-white text-sm">{holding.symbol}</span>
                                <span className="text-green-400 text-sm font-medium">
                                  +{holding.pnlPercent.toFixed(2)}%
                                </span>
                              </div>
                            ))}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Risk Metrics */}
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <AlertTriangle className="h-5 w-5 text-yellow-400" />
                          Risk Metrics
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between">
                            <span className="text-slate-400">Portfolio Beta</span>
                            <span className="text-white">1.23</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">Sharpe Ratio</span>
                            <span className="text-white">0.87</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">Max Drawdown</span>
                            <span className="text-red-400">-8.5%</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-slate-400">Volatility</span>
                            <span className="text-white">15.2%</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Quick Actions */}
                    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-white flex items-center gap-2">
                          <Zap className="h-5 w-5 text-blue-400" />
                          Quick Actions
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                            <Download className="h-4 w-4 mr-2" />
                            Export Portfolio
                          </Button>
                          <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700">
                            <Settings className="h-4 w-4 mr-2" />
                            Portfolio Settings
                          </Button>
                          <Button variant="outline" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700">
                            <Bell className="h-4 w-4 mr-2" />
                            Set Alerts
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
