// Unified Broker Manager for handling multiple broker APIs

import { AngelOneAPI } from './angelone';
import { ZerodhaAPI } from './zerodha';

export type BrokerType = 'zerodha' | 'upstox' | 'angelone' | 'groww' | 'icicidirect' | 'hdfcsec' | 'kotak' | 'sharekhan';

export interface BrokerCredentials {
  brokerId: BrokerType;
  apiKey: string;
  apiSecret?: string;
  clientCode?: string;
  password?: string;
  accessToken?: string;
}

export interface UnifiedHolding {
  symbol: string;
  name: string;
  quantity: number;
  avgPrice: number;
  ltp: number;
  investment: number;
  currentValue: number;
  pnl: number;
  pnlPercent: number;
  dayChange: number;
  dayChangePercent: number;
  sector: string;
  isin?: string;
  exchange?: string;
}

export interface UnifiedPortfolio {
  totalValue: number;
  totalInvestment: number;
  totalPnL: number;
  totalPnLPercent: number;
  dayPnL: number;
  dayPnLPercent: number;
  holdings: UnifiedHolding[];
  sectorAllocation: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  lastUpdated: string;
  broker: BrokerType;
}

export class BrokerManager {
  private brokerInstances: Map<BrokerType, any> = new Map();

  constructor() {
    // Initialize broker instances as needed
  }

  async connectBroker(credentials: BrokerCredentials): Promise<boolean> {
    try {
      switch (credentials.brokerId) {
        case 'zerodha':
          return await this.connectZerodha(credentials);
        case 'angelone':
          return await this.connectAngelOne(credentials);
        case 'upstox':
          return await this.connectUpstox(credentials);
        default:
          throw new Error(`Broker ${credentials.brokerId} not yet implemented`);
      }
    } catch (error) {
      console.error(`Failed to connect to ${credentials.brokerId}:`, error);
      return false;
    }
  }

  private async connectZerodha(credentials: BrokerCredentials): Promise<boolean> {
    if (!credentials.apiKey || !credentials.apiSecret) {
      throw new Error('Zerodha requires API key and secret');
    }

    const zerodha = new ZerodhaAPI({
      apiKey: credentials.apiKey,
      apiSecret: credentials.apiSecret,
      accessToken: credentials.accessToken
    });

    // If no access token, need to go through OAuth flow
    if (!credentials.accessToken) {
      const { url } = zerodha.generateLoginURL(`${process.env.NEXT_PUBLIC_BASE_URL}/auth/zerodha/callback`);
      // Store the instance for later use after OAuth
      this.brokerInstances.set('zerodha', zerodha);
      // Return the login URL for frontend to handle
      throw new Error(`OAuth required: ${url}`);
    }

    // Test the connection
    try {
      await zerodha.getProfile();
      this.brokerInstances.set('zerodha', zerodha);
      return true;
    } catch (error) {
      throw new Error('Failed to authenticate with Zerodha');
    }
  }

  private async connectAngelOne(credentials: BrokerCredentials): Promise<boolean> {
    if (!credentials.apiKey || !credentials.clientCode || !credentials.password) {
      throw new Error('Angel One requires API key, client code, and password');
    }

    const angelOne = new AngelOneAPI({
      apiKey: credentials.apiKey,
      clientCode: credentials.clientCode,
      password: credentials.password
    });

    const success = await angelOne.login();
    if (success) {
      this.brokerInstances.set('angelone', angelOne);
      return true;
    }

    throw new Error('Failed to authenticate with Angel One');
  }

  private async connectUpstox(credentials: BrokerCredentials): Promise<boolean> {
    // Upstox implementation would go here
    throw new Error('Upstox integration not yet implemented');
  }

  async getPortfolio(brokerId: BrokerType): Promise<UnifiedPortfolio> {
    const brokerInstance = this.brokerInstances.get(brokerId);
    if (!brokerInstance) {
      throw new Error(`Not connected to ${brokerId}`);
    }

    try {
      const holdings = await brokerInstance.getHoldings();
      return this.transformToUnifiedPortfolio(holdings, brokerId);
    } catch (error) {
      console.error(`Failed to fetch portfolio from ${brokerId}:`, error);
      throw error;
    }
  }

  private transformToUnifiedPortfolio(holdings: any[], brokerId: BrokerType): UnifiedPortfolio {
    const totalInvestment = holdings.reduce((sum, holding) => sum + holding.investment, 0);
    const totalValue = holdings.reduce((sum, holding) => sum + holding.currentValue, 0);
    const totalPnL = totalValue - totalInvestment;
    const totalPnLPercent = (totalPnL / totalInvestment) * 100;
    const dayPnL = holdings.reduce((sum, holding) => sum + (holding.dayChange * holding.quantity), 0);
    const dayPnLPercent = (dayPnL / totalValue) * 100;

    // Calculate sector allocation
    const sectorMap = new Map<string, number>();
    holdings.forEach(holding => {
      const currentSectorValue = sectorMap.get(holding.sector) || 0;
      sectorMap.set(holding.sector, currentSectorValue + holding.currentValue);
    });

    const sectorAllocation = Array.from(sectorMap.entries()).map(([sector, value]) => ({
      name: sector,
      value: (value / totalValue) * 100,
      color: this.getSectorColor(sector)
    }));

    return {
      totalValue,
      totalInvestment,
      totalPnL,
      totalPnLPercent,
      dayPnL,
      dayPnLPercent,
      holdings,
      sectorAllocation,
      lastUpdated: new Date().toISOString(),
      broker: brokerId
    };
  }

  private getSectorColor(sector: string): string {
    const colorMap: { [key: string]: string } = {
      'Banking': '#3B82F6',
      'IT': '#10B981',
      'Energy': '#F59E0B',
      'FMCG': '#8B5CF6',
      'Telecom': '#EF4444',
      'Healthcare': '#06B6D4',
      'Auto': '#F97316',
      'Others': '#6B7280'
    };
    
    return colorMap[sector] || '#6B7280';
  }

  async refreshPortfolio(brokerId: BrokerType): Promise<UnifiedPortfolio> {
    // Same as getPortfolio but with cache invalidation
    return this.getPortfolio(brokerId);
  }

  async disconnectBroker(brokerId: BrokerType): Promise<boolean> {
    const brokerInstance = this.brokerInstances.get(brokerId);
    if (!brokerInstance) {
      return true;
    }

    try {
      if (typeof brokerInstance.logout === 'function') {
        await brokerInstance.logout();
      }
      this.brokerInstances.delete(brokerId);
      return true;
    } catch (error) {
      console.error(`Failed to disconnect from ${brokerId}:`, error);
      return false;
    }
  }

  isConnected(brokerId: BrokerType): boolean {
    return this.brokerInstances.has(brokerId);
  }

  getConnectedBrokers(): BrokerType[] {
    return Array.from(this.brokerInstances.keys());
  }

  // Static method to get broker requirements
  static getBrokerRequirements(brokerId: BrokerType): string[] {
    switch (brokerId) {
      case 'zerodha':
        return ['API Key', 'API Secret', 'OAuth Flow'];
      case 'angelone':
        return ['API Key', 'Client Code', 'Password'];
      case 'upstox':
        return ['API Key', 'API Secret', 'OAuth Flow'];
      default:
        return ['Not yet implemented'];
    }
  }

  // Static method to get broker documentation links
  static getBrokerDocumentation(brokerId: BrokerType): string {
    switch (brokerId) {
      case 'zerodha':
        return 'https://kite.trade/docs/connect/v3/';
      case 'angelone':
        return 'https://smartapi.angelbroking.com/docs';
      case 'upstox':
        return 'https://upstox.com/developer/api-documentation/';
      default:
        return '#';
    }
  }
}

// Singleton instance
export const brokerManager = new BrokerManager();
