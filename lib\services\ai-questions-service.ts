// AI Questions Service
// Generates dynamic, context-aware questions based on real market conditions

interface AIQuestion {
  id: string;
  question: string;
  category: 'market' | 'sector' | 'stock' | 'crypto' | 'technical' | 'fundamental' | 'news' | 'strategy';
  complexity: 'beginner' | 'intermediate' | 'advanced';
  context?: string;
  expectedAnswer?: string;
}

interface MarketContext {
  region: 'india' | 'global' | 'crypto';
  marketSentiment: 'bullish' | 'bearish' | 'neutral';
  topSectors: string[];
  topMovers: string[];
  majorNews: string[];
}

class AIQuestionsService {
  private baseQuestions = {
    india: {
      beginner: [
        {
          question: "What is the difference between Nifty 50 and Sensex?",
          category: 'market' as const,
          context: "Understanding Indian market indices"
        },
        {
          question: "Why do FII (Foreign Institutional Investor) flows matter for Indian markets?",
          category: 'market' as const,
          context: "Foreign investment impact"
        },
        {
          question: "What is the role of RBI in Indian stock markets?",
          category: 'market' as const,
          context: "Central bank influence"
        },
        {
          question: "How do monsoons affect Indian agricultural stocks?",
          category: 'sector' as const,
          context: "Seasonal factors in investing"
        },
        {
          question: "What is the significance of Budget announcements for markets?",
          category: 'news' as const,
          context: "Government policy impact"
        },
        {
          question: "Why are IT stocks considered export-oriented in India?",
          category: 'sector' as const,
          context: "Sector characteristics"
        },
        {
          question: "What is the difference between PSU and private sector banks?",
          category: 'sector' as const,
          context: "Banking sector basics"
        },
        {
          question: "How does rupee movement affect different Indian sectors?",
          category: 'market' as const,
          context: "Currency impact on markets"
        },
        {
          question: "What are the major stock exchanges in India?",
          category: 'market' as const,
          context: "Market infrastructure"
        },
        {
          question: "Why do pharma stocks perform well during global uncertainties?",
          category: 'sector' as const,
          context: "Defensive sector characteristics"
        }
      ],
      intermediate: [
        {
          question: "How does the rupee depreciation impact different sectors differently?",
          category: 'sector' as const,
          context: "Currency impact analysis"
        },
        {
          question: "Why are IT stocks considered defensive in Indian markets?",
          category: 'sector' as const,
          context: "Sector characteristics"
        },
        {
          question: "What factors drive the performance of PSU banks vs private banks?",
          category: 'sector' as const,
          context: "Banking sector analysis"
        },
        {
          question: "How do global commodity prices affect Indian metal stocks?",
          category: 'sector' as const,
          context: "Global linkages"
        },
        {
          question: "What is the impact of GST changes on FMCG companies?",
          category: 'sector' as const,
          context: "Tax policy effects"
        },
        {
          question: "How do quarterly earnings seasons affect Indian stock volatility?",
          category: 'fundamental' as const,
          context: "Earnings impact analysis"
        },
        {
          question: "What role does DII (Domestic Institutional Investor) buying play?",
          category: 'market' as const,
          context: "Institutional investment patterns"
        },
        {
          question: "How do crude oil price movements impact the Indian economy?",
          category: 'market' as const,
          context: "Commodity impact on markets"
        },
        {
          question: "What is the significance of India VIX in market timing?",
          category: 'technical' as const,
          context: "Volatility indicators"
        },
        {
          question: "How do auto sales numbers predict sector performance?",
          category: 'sector' as const,
          context: "Economic indicators and sectors"
        }
      ],
      advanced: [
        {
          question: "How do you analyze the impact of crude oil prices on Indian market sectors?",
          category: 'fundamental' as const,
          context: "Macro-economic analysis"
        },
        {
          question: "What are the key metrics to evaluate pharma companies for export potential?",
          category: 'fundamental' as const,
          context: "Sector-specific valuation"
        },
        {
          question: "How does the India VIX indicate market sentiment and trading opportunities?",
          category: 'technical' as const,
          context: "Volatility analysis"
        },
        {
          question: "What is the significance of block deals and bulk deals in price discovery?",
          category: 'technical' as const,
          context: "Market microstructure"
        },
        {
          question: "How do you construct a sector rotation strategy in Indian markets?",
          category: 'strategy' as const,
          context: "Portfolio management"
        }
      ]
    },
    global: {
      beginner: [
        {
          question: "What is the difference between S&P 500, Nasdaq, and Dow Jones?",
          category: 'market' as const,
          context: "US market indices"
        },
        {
          question: "How do Federal Reserve interest rate decisions affect stock markets?",
          category: 'market' as const,
          context: "Monetary policy impact"
        },
        {
          question: "What are earnings seasons and why do they matter?",
          category: 'fundamental' as const,
          context: "Corporate earnings cycle"
        },
        {
          question: "How do geopolitical events impact global markets?",
          category: 'news' as const,
          context: "Global risk factors"
        },
        {
          question: "What is the significance of GDP growth rates for stock markets?",
          category: 'market' as const,
          context: "Economic indicators"
        },
        {
          question: "Why do tech stocks often lead market movements?",
          category: 'sector' as const,
          context: "Technology sector influence"
        },
        {
          question: "What is the role of the US Dollar in global markets?",
          category: 'market' as const,
          context: "Currency impact on global trade"
        },
        {
          question: "How do unemployment numbers affect stock market sentiment?",
          category: 'market' as const,
          context: "Economic indicators"
        },
        {
          question: "What are blue-chip stocks and why are they considered safe?",
          category: 'stock' as const,
          context: "Investment fundamentals"
        },
        {
          question: "How do oil prices impact different global sectors?",
          category: 'sector' as const,
          context: "Commodity impact on sectors"
        }
      ],
      intermediate: [
        {
          question: "How do you analyze the impact of inflation on different asset classes?",
          category: 'fundamental' as const,
          context: "Inflation hedging strategies"
        },
        {
          question: "What drives the performance of growth vs value stocks in different market cycles?",
          category: 'strategy' as const,
          context: "Investment style analysis"
        },
        {
          question: "How do currency fluctuations affect multinational companies?",
          category: 'fundamental' as const,
          context: "Currency exposure analysis"
        },
        {
          question: "What are the key differences between developed and emerging market investing?",
          category: 'strategy' as const,
          context: "Global diversification"
        },
        {
          question: "How do you interpret yield curve movements for equity markets?",
          category: 'technical' as const,
          context: "Bond-equity relationships"
        }
      ],
      advanced: [
        {
          question: "How do you build a factor-based investment strategy using smart beta ETFs?",
          category: 'strategy' as const,
          context: "Quantitative investing"
        },
        {
          question: "What are the implications of central bank digital currencies (CBDCs) for financial markets?",
          category: 'market' as const,
          context: "Future of finance"
        },
        {
          question: "How do you analyze the impact of ESG factors on long-term stock performance?",
          category: 'fundamental' as const,
          context: "Sustainable investing"
        },
        {
          question: "What role does algorithmic trading play in modern market volatility?",
          category: 'technical' as const,
          context: "Market structure evolution"
        },
        {
          question: "How do you construct a global macro hedge fund strategy?",
          category: 'strategy' as const,
          context: "Advanced portfolio management"
        }
      ]
    },
    crypto: {
      beginner: [
        {
          question: "What is the difference between Bitcoin and Ethereum?",
          category: 'crypto' as const,
          context: "Cryptocurrency fundamentals"
        },
        {
          question: "How do crypto market cycles differ from traditional stock market cycles?",
          category: 'market' as const,
          context: "Crypto market dynamics"
        },
        {
          question: "What factors drive cryptocurrency prices?",
          category: 'fundamental' as const,
          context: "Crypto valuation basics"
        },
        {
          question: "How do regulatory announcements impact crypto markets?",
          category: 'news' as const,
          context: "Regulatory risk in crypto"
        },
        {
          question: "What is the significance of Bitcoin halving events?",
          category: 'fundamental' as const,
          context: "Bitcoin supply mechanics"
        },
        {
          question: "What are altcoins and how do they differ from Bitcoin?",
          category: 'crypto' as const,
          context: "Cryptocurrency categories"
        },
        {
          question: "How do crypto exchanges work and why do prices vary?",
          category: 'market' as const,
          context: "Crypto market structure"
        },
        {
          question: "What is market capitalization in cryptocurrency?",
          category: 'fundamental' as const,
          context: "Crypto valuation metrics"
        },
        {
          question: "Why is Bitcoin often called 'digital gold'?",
          category: 'crypto' as const,
          context: "Bitcoin value proposition"
        },
        {
          question: "What are stablecoins and why are they important?",
          category: 'crypto' as const,
          context: "Crypto ecosystem infrastructure"
        }
      ],
      intermediate: [
        {
          question: "How do you analyze the fundamentals of DeFi protocols?",
          category: 'fundamental' as const,
          context: "DeFi ecosystem analysis"
        },
        {
          question: "What is the impact of institutional adoption on crypto market stability?",
          category: 'market' as const,
          context: "Institutional crypto investing"
        },
        {
          question: "How do Layer 1 and Layer 2 blockchain solutions compete?",
          category: 'technical' as const,
          context: "Blockchain scalability"
        },
        {
          question: "What role do stablecoins play in the crypto ecosystem?",
          category: 'crypto' as const,
          context: "Crypto infrastructure"
        },
        {
          question: "How do you evaluate the tokenomics of a new cryptocurrency project?",
          category: 'fundamental' as const,
          context: "Token economics analysis"
        }
      ],
      advanced: [
        {
          question: "How do you construct a risk-managed crypto portfolio using derivatives?",
          category: 'strategy' as const,
          context: "Advanced crypto trading"
        },
        {
          question: "What are the implications of crypto ETF approvals for market structure?",
          category: 'market' as const,
          context: "Crypto market evolution"
        },
        {
          question: "How do you analyze cross-chain interoperability protocols for investment?",
          category: 'technical' as const,
          context: "Blockchain infrastructure investing"
        },
        {
          question: "What is the role of MEV (Maximal Extractable Value) in DeFi markets?",
          category: 'technical' as const,
          context: "Advanced DeFi mechanics"
        },
        {
          question: "How do you build a quantitative crypto trading strategy using on-chain data?",
          category: 'strategy' as const,
          context: "Crypto quantitative analysis"
        }
      ]
    }
  };

  generateContextualQuestions(marketContext: MarketContext, count: number = 12): AIQuestion[] {
    const { region, marketSentiment, topSectors, topMovers, majorNews } = marketContext;
    const baseQuestionsForRegion = this.baseQuestions[region];
    
    // Get base questions
    const allBaseQuestions = [
      ...baseQuestionsForRegion.beginner,
      ...baseQuestionsForRegion.intermediate,
      ...baseQuestionsForRegion.advanced
    ];

    // Generate dynamic questions based on current market conditions
    const dynamicQuestions = this.generateDynamicQuestions(marketContext);
    
    // Combine and shuffle
    const allQuestions = [...allBaseQuestions, ...dynamicQuestions];
    
    // Add IDs and complexity levels
    const questionsWithMetadata = allQuestions.map((q, index) => ({
      id: `${region}_${index}_${Date.now()}`,
      question: q.question,
      category: q.category,
      complexity: this.determineComplexity(q.question, q.category),
      context: q.context
    }));

    // Return shuffled selection
    return this.shuffleArray(questionsWithMetadata).slice(0, count);
  }

  private generateDynamicQuestions(context: MarketContext): Partial<AIQuestion>[] {
    const { region, marketSentiment, topSectors, topMovers, majorNews } = context;
    const dynamicQuestions: Partial<AIQuestion>[] = [];

    // Sentiment-based questions
    if (marketSentiment === 'bullish') {
      dynamicQuestions.push({
        question: `What factors are driving the current bullish sentiment in ${region} markets?`,
        category: 'market',
        context: "Current market analysis"
      });
      dynamicQuestions.push({
        question: `How long can the current bull run in ${region} markets sustain?`,
        category: 'strategy',
        context: "Market timing analysis"
      });
    } else if (marketSentiment === 'bearish') {
      dynamicQuestions.push({
        question: `What are the key risks causing bearish sentiment in ${region} markets?`,
        category: 'market',
        context: "Risk assessment"
      });
      dynamicQuestions.push({
        question: `How should investors position themselves during this bearish phase?`,
        category: 'strategy',
        context: "Defensive strategies"
      });
    }

    // Sector-specific questions
    topSectors.forEach(sector => {
      dynamicQuestions.push({
        question: `Why is the ${sector} sector outperforming in current market conditions?`,
        category: 'sector',
        context: "Sector rotation analysis"
      });
      dynamicQuestions.push({
        question: `What are the key risks to watch for in the ${sector} sector?`,
        category: 'sector',
        context: "Sector risk analysis"
      });
    });

    // Stock-specific questions
    topMovers.forEach(stock => {
      dynamicQuestions.push({
        question: `What's driving the significant price movement in ${stock} today?`,
        category: 'stock',
        context: "Individual stock analysis"
      });
    });

    // News-based questions
    if (majorNews.length > 0) {
      dynamicQuestions.push({
        question: `How should investors interpret the recent major news affecting ${region} markets?`,
        category: 'news',
        context: "News impact analysis"
      });
    }

    // Region-specific dynamic questions
    if (region === 'india') {
      dynamicQuestions.push({
        question: "How are current FII flows affecting market sentiment and sector rotation?",
        category: 'market',
        context: "Foreign investment flows"
      });
    } else if (region === 'global') {
      dynamicQuestions.push({
        question: "What's the current market consensus on Federal Reserve policy and its impact?",
        category: 'market',
        context: "Central bank policy"
      });
    } else if (region === 'crypto') {
      dynamicQuestions.push({
        question: "How are recent regulatory developments shaping crypto market dynamics?",
        category: 'crypto',
        context: "Regulatory environment"
      });
    }

    return dynamicQuestions;
  }

  private determineComplexity(question: string, category: string): 'beginner' | 'intermediate' | 'advanced' {
    const advancedKeywords = ['quantitative', 'derivatives', 'factor', 'macro', 'algorithmic', 'MEV', 'tokenomics'];
    const intermediateKeywords = ['analysis', 'strategy', 'impact', 'evaluate', 'construct', 'interpret'];
    
    const lowerQuestion = question.toLowerCase();
    
    if (advancedKeywords.some(keyword => lowerQuestion.includes(keyword))) {
      return 'advanced';
    }
    
    if (intermediateKeywords.some(keyword => lowerQuestion.includes(keyword))) {
      return 'intermediate';
    }
    
    return 'beginner';
  }

  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  // Get questions by specific criteria
  getQuestionsByCategory(region: 'india' | 'global' | 'crypto', category: string, count: number = 5): AIQuestion[] {
    const allQuestions = [
      ...this.baseQuestions[region].beginner,
      ...this.baseQuestions[region].intermediate,
      ...this.baseQuestions[region].advanced
    ];

    const filteredQuestions = allQuestions
      .filter(q => q.category === category)
      .map((q, index) => ({
        id: `${region}_${category}_${index}`,
        question: q.question,
        category: q.category,
        complexity: this.determineComplexity(q.question, q.category),
        context: q.context
      }));

    return this.shuffleArray(filteredQuestions).slice(0, count);
  }

  getQuestionsByComplexity(region: 'india' | 'global' | 'crypto', complexity: 'beginner' | 'intermediate' | 'advanced', count: number = 5): AIQuestion[] {
    const questionsForComplexity = this.baseQuestions[region][complexity];
    
    return questionsForComplexity
      .map((q, index) => ({
        id: `${region}_${complexity}_${index}`,
        question: q.question,
        category: q.category,
        complexity: complexity,
        context: q.context
      }))
      .slice(0, count);
  }
}

export const aiQuestionsService = new AIQuestionsService();
export type { AIQuestion, MarketContext };
