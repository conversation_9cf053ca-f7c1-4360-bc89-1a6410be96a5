// Market Insight Engine - Type Definitions

export interface MarketData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  volumeChange: number;
  marketCap?: number;
  sector?: string;
  exchange?: string;
}

export interface TechnicalIndicators {
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  sma20: number;
  sma50: number;
  bollinger: {
    upper: number;
    middle: number;
    lower: number;
  };
  volatility: number;
  momentum: 'bullish' | 'bearish' | 'neutral';
}

export interface TimeFrameAnalysis {
  timeframe: '1H' | '1D' | '1W';
  priceMovement: number;
  volumeChange: number;
  volatilitySpike: boolean;
  technicalSignal: 'buy' | 'sell' | 'hold';
  momentum: 'strong_bullish' | 'bullish' | 'neutral' | 'bearish' | 'strong_bearish';
  keyLevels: {
    support: number;
    resistance: number;
  };
}

export interface SectorAnalysis {
  sector: string;
  performance: number;
  topStocks: Array<{
    symbol: string;
    name: string;
    performance: number;
    volume: number;
    newsImpact: string;
    signal: 'opportunity' | 'caution' | 'neutral';
  }>;
  newsImpact: string;
  outlook: string;
}

export interface OpportunityRadar {
  type: 'opportunity' | 'risk';
  asset: {
    symbol: string;
    name: string;
    sector: string;
    price: number;
    change: number;
  };
  reason: string;
  confidence: number;
  aiExplanation: string;
  technicalReason: string;
  fundamentalReason?: string;
  newsReason?: string;
  timeHorizon: 'short' | 'medium' | 'long';
}

export interface RegionalMarketSummary {
  region: 'india' | 'global' | 'crypto';
  indices: Array<{
    name: string;
    symbol: string;
    value: number;
    change: number;
    changePercent: number;
  }>;
  topGainers: MarketData[];
  topLosers: MarketData[];
  sectorHeatmap: Array<{
    sector: string;
    performance: number;
    color: string;
  }>;
  summary: string;
  keyEvents: string[];
}

export interface NewsImpact {
  headline: string;
  summary: string;
  affectedAssets: string[];
  impactScore: number;
  sentiment: 'positive' | 'negative' | 'neutral';
  category: 'earnings' | 'regulation' | 'macro' | 'company' | 'sector';
  aiAnalysis: string;
  priceImpact: {
    symbol: string;
    expectedMove: number;
    confidence: number;
  }[];
}

export interface InsightQuestion {
  id: string;
  question: string;
  category: 'market' | 'sector' | 'stock' | 'crypto';
  complexity: 'beginner' | 'intermediate' | 'advanced';
  relatedAssets: string[];
  aiResponse?: string;
}

export interface MarketInsightEngine {
  timestamp: string;
  timeFrameAnalysis: {
    india: TimeFrameAnalysis[];
    global: TimeFrameAnalysis[];
    crypto: TimeFrameAnalysis[];
  };
  sectorAnalysis: SectorAnalysis[];
  opportunityRadar: OpportunityRadar[];
  regionalSummaries: RegionalMarketSummary[];
  newsImpacts: NewsImpact[];
  insightQuestions: InsightQuestion[];
  marketSentiment: {
    overall: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
    factors: string[];
  };
}

export interface FilterOptions {
  region?: 'india' | 'global' | 'crypto' | 'all';
  sector?: string;
  timeframe?: '1H' | '1D' | '1W' | '1M';
  assetClass?: 'equity' | 'crypto' | 'commodity' | 'forex';
  sentiment?: 'positive' | 'negative' | 'neutral';
  opportunity?: 'high' | 'medium' | 'low';
  risk?: 'high' | 'medium' | 'low';
}

export interface ChartConfig {
  symbol: string;
  timeframe: string;
  indicators: string[];
  overlays: string[];
  theme: 'light' | 'dark';
}

// API Response Types
export interface APIResponse<T> {
  success: boolean;
  data: T;
  error?: string;
  timestamp: string;
  cached?: boolean;
}

// Real-time Data Types
export interface RealTimeUpdate {
  type: 'price' | 'news' | 'technical' | 'sentiment';
  symbol: string;
  data: any;
  timestamp: string;
}

// AI Analysis Types
export interface AIAnalysisRequest {
  type: 'market_summary' | 'stock_analysis' | 'sector_outlook' | 'news_impact';
  data: any;
  context?: string;
}

export interface AIAnalysisResponse {
  analysis: string;
  confidence: number;
  keyPoints: string[];
  recommendations: string[];
  riskFactors: string[];
}
