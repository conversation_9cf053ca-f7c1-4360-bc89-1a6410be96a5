'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'

export default function TestRedirectPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/login')
    }
  }, [status, router])

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  if (status === 'unauthenticated') {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-white">Redirecting to login...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-900 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-6 mb-8">
          <h1 className="text-2xl font-bold text-green-400 mb-4">🎉 Redirect Test Successful!</h1>
          <div className="text-green-300 space-y-2">
            <p><strong>Status:</strong> {status}</p>
            <p><strong>User:</strong> {session?.user?.name}</p>
            <p><strong>Email:</strong> {session?.user?.email}</p>
            <p><strong>First Name:</strong> {session?.user?.firstName}</p>
            <p><strong>Role:</strong> {session?.user?.role}</p>
          </div>
        </div>

        <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-6">
          <h2 className="text-xl font-bold text-blue-400 mb-4">🔧 Test Instructions</h2>
          <div className="text-blue-300 space-y-2">
            <p>1. If you can see this page, the authentication is working!</p>
            <p>2. Try logging out and logging back in</p>
            <p>3. Check if you get redirected to the home page after login</p>
            <p>4. Navigate to <code className="bg-slate-800 px-2 py-1 rounded">http://localhost:3000</code> to see the home page</p>
          </div>
        </div>
      </div>
    </div>
  )
}
