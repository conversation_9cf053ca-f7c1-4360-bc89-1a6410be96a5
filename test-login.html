<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1e293b; color: white; }
        .container { max-width: 500px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input { width: 100%; padding: 10px; border: 1px solid #475569; background: #334155; color: white; border-radius: 5px; }
        button { background: #3b82f6; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #2563eb; }
        .result { margin-top: 20px; padding: 15px; border-radius: 5px; }
        .success { background: #065f46; border: 1px solid #10b981; }
        .error { background: #7f1d1d; border: 1px solid #ef4444; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test Authentication</h1>
        
        <form id="loginForm">
            <div class="form-group">
                <label>Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label>Password:</label>
                <input type="password" id="password" required>
            </div>
            
            <button type="submit">Test Login</button>
        </form>
        
        <div id="result"></div>
        
        <div style="margin-top: 30px;">
            <h3>🎯 Expected Flow:</h3>
            <ol>
                <li>Enter your credentials</li>
                <li>Click "Test Login"</li>
                <li>Should redirect to home page</li>
                <li>Should see welcome message</li>
            </ol>
            
            <h3>📋 Test Credentials:</h3>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> (your signup password)</p>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<div class="result">🔄 Testing login...</div>';
            
            try {
                const response = await fetch('/api/auth/signin/credentials', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        password: password,
                        callbackUrl: '/',
                        redirect: false
                    })
                });
                
                const result = await response.json();
                
                if (result.ok) {
                    resultDiv.innerHTML = '<div class="result success">✅ Login successful! Redirecting to home page...</div>';
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Login failed: ${result.error || 'Unknown error'}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Network error: ${error.message}</div>`;
            }
        });
    </script>
</body>
</html>
