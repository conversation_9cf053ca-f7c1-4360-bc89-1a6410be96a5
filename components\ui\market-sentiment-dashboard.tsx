"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  AlertTriangle,
  BarChart3,
  Globe,
  Bitcoin,
  IndianRupee,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';

interface MarketSentiment {
  market: 'indian' | 'foreign' | 'crypto';
  sentiment: 'bullish' | 'bearish' | 'neutral';
  score: number;
  confidence: number;
  trend: 'rising' | 'falling' | 'stable';
  volatility: 'low' | 'medium' | 'high';
  lastUpdated: Date;
}

interface MarketSentimentDashboardProps {
  sentiments: MarketSentiment[];
  marketIntelligence?: any;
  className?: string;
}

export function MarketSentimentDashboard({ 
  sentiments, 
  marketIntelligence,
  className = '' 
}: MarketSentimentDashboardProps) {
  const [selectedMarket, setSelectedMarket] = useState<'all' | 'indian' | 'foreign' | 'crypto'>('all');

  const getMarketIcon = (market: string) => {
    switch (market) {
      case 'indian': return <IndianRupee className="h-5 w-5" />;
      case 'foreign': return <Globe className="h-5 w-5" />;
      case 'crypto': return <Bitcoin className="h-5 w-5" />;
      default: return <BarChart3 className="h-5 w-5" />;
    }
  };

  const getMarketName = (market: string) => {
    switch (market) {
      case 'indian': return '🇮🇳 Indian Markets';
      case 'foreign': return '🌍 Global Markets';
      case 'crypto': return '💰 Crypto Markets';
      default: return 'All Markets';
    }
  };

  const getSentimentColor = (sentiment: string, score: number) => {
    if (sentiment === 'bullish') return 'text-green-400 bg-green-500/10 border-green-500/30';
    if (sentiment === 'bearish') return 'text-red-400 bg-red-500/10 border-red-500/30';
    return 'text-slate-400 bg-slate-500/10 border-slate-500/30';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'rising': return <ArrowUp className="h-4 w-4 text-green-400" />;
      case 'falling': return <ArrowDown className="h-4 w-4 text-red-400" />;
      default: return <Minus className="h-4 w-4 text-slate-400" />;
    }
  };

  const getVolatilityColor = (volatility: string) => {
    switch (volatility) {
      case 'high': return 'text-red-400 bg-red-500/10';
      case 'medium': return 'text-yellow-400 bg-yellow-500/10';
      case 'low': return 'text-green-400 bg-green-500/10';
      default: return 'text-slate-400 bg-slate-500/10';
    }
  };

  const calculateOverallSentiment = () => {
    if (sentiments.length === 0) return { sentiment: 'neutral', score: 0, confidence: 0 };
    
    const avgScore = sentiments.reduce((acc, s) => acc + s.score, 0) / sentiments.length;
    const avgConfidence = sentiments.reduce((acc, s) => acc + s.confidence, 0) / sentiments.length;
    
    let sentiment = 'neutral';
    if (avgScore > 0.2) sentiment = 'bullish';
    else if (avgScore < -0.2) sentiment = 'bearish';
    
    return { sentiment, score: avgScore, confidence: avgConfidence };
  };

  const overall = calculateOverallSentiment();

  const SentimentCard = ({ sentiment }: { sentiment: MarketSentiment }) => (
    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all duration-300">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getMarketIcon(sentiment.market)}
            <CardTitle className="text-white text-sm">
              {getMarketName(sentiment.market)}
            </CardTitle>
          </div>
          {getTrendIcon(sentiment.trend)}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Sentiment Score */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-slate-400">Sentiment</span>
            <Badge variant="outline" className={getSentimentColor(sentiment.sentiment, sentiment.score)}>
              {sentiment.sentiment.toUpperCase()}
            </Badge>
          </div>
          <div className="space-y-1">
            <Progress 
              value={((sentiment.score + 1) / 2) * 100} 
              className="h-2 bg-slate-700"
            />
            <div className="flex justify-between text-xs text-slate-400">
              <span>Bearish</span>
              <span className="text-white font-medium">
                {(sentiment.score * 100).toFixed(1)}%
              </span>
              <span>Bullish</span>
            </div>
          </div>
        </div>

        {/* Confidence Level */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-slate-400">Confidence</span>
            <span className="text-xs text-white">
              {(sentiment.confidence * 100).toFixed(1)}%
            </span>
          </div>
          <Progress 
            value={sentiment.confidence * 100} 
            className="h-1.5 bg-slate-700"
          />
        </div>

        {/* Volatility */}
        <div className="flex items-center justify-between">
          <span className="text-xs text-slate-400">Volatility</span>
          <Badge variant="outline" className={getVolatilityColor(sentiment.volatility)}>
            {sentiment.volatility.toUpperCase()}
          </Badge>
        </div>

        {/* Last Updated */}
        <div className="text-xs text-slate-500">
          Updated: {sentiment.lastUpdated.toLocaleTimeString()}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Market Sentiment */}
      <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Activity className="h-5 w-5 text-blue-400" />
            Overall Market Sentiment
          </CardTitle>
          <CardDescription className="text-slate-400">
            Real-time sentiment analysis across all markets
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-1">
                {overall.sentiment.toUpperCase()}
              </div>
              <div className="text-sm text-slate-400">Market Mood</div>
              <Badge variant="outline" className={getSentimentColor(overall.sentiment, overall.score)}>
                {(overall.score * 100).toFixed(1)}%
              </Badge>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-1">
                {(overall.confidence * 100).toFixed(1)}%
              </div>
              <div className="text-sm text-slate-400">Confidence</div>
              <Progress value={overall.confidence * 100} className="h-2 bg-slate-700 mt-2" />
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white mb-1">
                {sentiments.length}
              </div>
              <div className="text-sm text-slate-400">Markets Analyzed</div>
              <div className="text-xs text-slate-500 mt-1">
                Last updated: {new Date().toLocaleTimeString()}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Individual Market Sentiments */}
      <div>
        <h3 className="text-lg font-semibold text-white mb-4">Market-Specific Analysis</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {sentiments.map((sentiment) => (
            <SentimentCard key={sentiment.market} sentiment={sentiment} />
          ))}
        </div>
      </div>

      {/* Market Correlations */}
      {marketIntelligence?.marketCorrelations && (
        <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-purple-400" />
              Market Correlations
            </CardTitle>
            <CardDescription className="text-slate-400">
              Cross-market relationship analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-lg font-semibold text-white mb-1">
                  {(marketIntelligence.marketCorrelations.indianForeign * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-slate-400">Indian ↔ Global</div>
                <Progress 
                  value={Math.abs(marketIntelligence.marketCorrelations.indianForeign) * 100} 
                  className="h-2 bg-slate-700 mt-2" 
                />
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-white mb-1">
                  {(marketIntelligence.marketCorrelations.indianCrypto * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-slate-400">Indian ↔ Crypto</div>
                <Progress 
                  value={Math.abs(marketIntelligence.marketCorrelations.indianCrypto) * 100} 
                  className="h-2 bg-slate-700 mt-2" 
                />
              </div>
              <div className="text-center">
                <div className="text-lg font-semibold text-white mb-1">
                  {(marketIntelligence.marketCorrelations.foreignCrypto * 100).toFixed(1)}%
                </div>
                <div className="text-sm text-slate-400">Global ↔ Crypto</div>
                <Progress 
                  value={Math.abs(marketIntelligence.marketCorrelations.foreignCrypto) * 100} 
                  className="h-2 bg-slate-700 mt-2" 
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Risk Assessment */}
      {marketIntelligence?.riskAssessment && (
        <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-400" />
              Risk Assessment
            </CardTitle>
            <CardDescription className="text-slate-400">
              Current market risk evaluation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-slate-300">Risk Level</span>
                <Badge
                  variant="outline"
                  className={
                    marketIntelligence.riskAssessment?.overallRisk === 'high' || marketIntelligence.riskAssessment?.overallRisk === 'extreme'
                      ? 'text-red-400 bg-red-500/10 border-red-500/30'
                      : marketIntelligence.riskAssessment?.overallRisk === 'medium'
                      ? 'text-yellow-400 bg-yellow-500/10 border-yellow-500/30'
                      : 'text-green-400 bg-green-500/10 border-green-500/30'
                  }
                >
                  {marketIntelligence.riskAssessment?.overallRisk?.toUpperCase().replace('_', ' ') || 'MEDIUM'}
                </Badge>
              </div>
              
              {marketIntelligence.riskAssessment?.riskFactors && marketIntelligence.riskAssessment.riskFactors.length > 0 && (
                <div>
                  <div className="text-sm text-slate-400 mb-2">Risk Factors:</div>
                  <div className="space-y-1">
                    {marketIntelligence.riskAssessment.riskFactors.slice(0, 3).map((factor: any, index: number) => (
                      <div key={index} className="text-sm text-slate-300 flex items-center gap-2">
                        <div className="w-1 h-1 bg-orange-400 rounded-full"></div>
                        {typeof factor === 'string' ? factor : factor.description || 'Market risk factor'}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="bg-slate-700/50 p-3 rounded-lg">
                <div className="text-sm text-slate-400 mb-1">Recommendation:</div>
                <div className="text-sm text-slate-300">
                  {marketIntelligence.riskAssessment?.mitigationStrategies?.[0] || 'Monitor market conditions and maintain diversified portfolio'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
