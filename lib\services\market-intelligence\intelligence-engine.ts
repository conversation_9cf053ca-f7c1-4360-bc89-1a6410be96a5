// Advanced Market Intelligence Engine

import { NewsArticle } from '../news/types';
import { geminiClient } from '@/lib/gemini';
import { sentimentAnalyzer } from './sentiment-analyzer';
import { 
  MarketIntelligence, 
  MarketTrend, 
  AdvancedInsight,
  MarketIndicator,
  TrendAnalysisAlgorithm,
  MARKET_CORRELATIONS
} from './types';

export class MarketIntelligenceEngine {
  private intelligenceCache = new Map<string, MarketIntelligence>();
  private readonly cacheTimeout = 15 * 60 * 1000; // 15 minutes

  /**
   * Generate comprehensive market intelligence
   */
  async generateMarketIntelligence(articles: NewsArticle[]): Promise<MarketIntelligence> {
    const cacheKey = this.generateCacheKey(articles);
    const cached = this.intelligenceCache.get(cacheKey);
    
    if (cached && this.isCacheValid(cached)) {
      return cached;
    }

    try {
      // Parallel analysis for performance
      const [
        overallSentiment,
        trends,
        topImpactNews,
        marketCorrelations,
        volatilityIndex,
        riskAssessment
      ] = await Promise.all([
        sentimentAnalyzer.analyzeMultiMarketSentiment(articles),
        this.analyzeTrends(articles),
        sentimentAnalyzer.analyzeNewsImpact(articles),
        this.calculateMarketCorrelations(articles),
        this.calculateVolatilityIndex(articles),
        this.assessRisk(articles)
      ]);

      const intelligence: MarketIntelligence = {
        timestamp: new Date(),
        overallSentiment,
        trends,
        topImpactNews: topImpactNews.slice(0, 10), // Top 10 most impactful
        marketCorrelations,
        volatilityIndex,
        riskAssessment
      };

      this.intelligenceCache.set(cacheKey, intelligence);
      return intelligence;
    } catch (error) {
      console.error('Error generating market intelligence:', error);
      return this.getDefaultIntelligence();
    }
  }

  /**
   * Generate advanced AI-powered market insights
   */
  async generateAdvancedInsights(articles: NewsArticle[]): Promise<string> {
    try {
      const intelligence = await this.generateMarketIntelligence(articles);
      const prompt = this.buildAdvancedInsightPrompt(intelligence, articles);
      
      const response = await geminiClient.generateContent([{
        role: 'user',
        content: prompt
      }], {
        userBehavior: { focusArea: 'advanced_financial_analysis' },
        financialProfile: { experienceLevel: 'professional' }
      });

      return this.enhanceInsightWithData(response.content, intelligence);
    } catch (error) {
      console.error('Error generating advanced insights:', error);
      return this.generateFallbackInsights(articles);
    }
  }

  /**
   * Analyze market trends using multiple algorithms
   */
  private async analyzeTrends(articles: NewsArticle[]): Promise<MarketTrend[]> {
    const trends: MarketTrend[] = [];
    const markets = ['indian', 'foreign', 'crypto'] as const;

    for (const market of markets) {
      const marketArticles = this.filterArticlesByMarket(articles, market);
      const trend = await this.calculateMarketTrend(marketArticles, market);
      trends.push(trend);
    }

    return trends;
  }

  /**
   * Calculate market trend using ensemble of algorithms
   */
  private async calculateMarketTrend(
    articles: NewsArticle[], 
    market: 'indian' | 'foreign' | 'crypto'
  ): Promise<MarketTrend> {
    const algorithms: TrendAnalysisAlgorithm[] = [
      {
        name: 'momentum',
        weight: 0.3,
        calculate: (arts, timeframe) => this.calculateMomentumTrend(arts, timeframe)
      },
      {
        name: 'sentiment_velocity',
        weight: 0.25,
        calculate: (arts, timeframe) => this.calculateSentimentVelocity(arts, timeframe)
      },
      {
        name: 'volume_trend',
        weight: 0.2,
        calculate: (arts, timeframe) => this.calculateVolumeTrend(arts, timeframe)
      },
      {
        name: 'impact_weighted',
        weight: 0.25,
        calculate: (arts, timeframe) => this.calculateImpactWeightedTrend(arts, timeframe)
      }
    ];

    // Calculate weighted trend score
    let trendScore = 0;
    let totalWeight = 0;

    for (const algorithm of algorithms) {
      const score = algorithm.calculate(articles, 24); // 24-hour timeframe
      trendScore += score * algorithm.weight;
      totalWeight += algorithm.weight;
    }

    const normalizedScore = totalWeight > 0 ? trendScore / totalWeight : 0;

    // Determine trend direction and strength
    const direction = this.scoreToDirection(normalizedScore);
    const strength = Math.abs(normalizedScore);
    const confidence = this.calculateTrendConfidence(articles, market);

    // Extract supporting and risk factors
    const { supportingFactors, riskFactors } = this.extractTrendFactors(articles, direction);

    return {
      market,
      direction,
      strength: Math.min(strength, 1),
      duration: this.estimateTrendDuration(articles, strength),
      confidence,
      supportingFactors,
      riskFactors
    };
  }

  /**
   * Calculate market correlations
   */
  private async calculateMarketCorrelations(articles: NewsArticle[]): Promise<{
    indianForeign: number;
    indianCrypto: number;
    foreignCrypto: number;
  }> {
    const indianArticles = this.filterArticlesByMarket(articles, 'indian');
    const foreignArticles = this.filterArticlesByMarket(articles, 'foreign');
    const cryptoArticles = this.filterArticlesByMarket(articles, 'crypto');

    return {
      indianForeign: this.calculateCorrelation(indianArticles, foreignArticles),
      indianCrypto: this.calculateCorrelation(indianArticles, cryptoArticles),
      foreignCrypto: this.calculateCorrelation(foreignArticles, cryptoArticles)
    };
  }

  /**
   * Calculate volatility index for each market
   */
  private async calculateVolatilityIndex(articles: NewsArticle[]): Promise<{
    indian: number;
    foreign: number;
    crypto: number;
  }> {
    return {
      indian: this.calculateMarketVolatility(this.filterArticlesByMarket(articles, 'indian')),
      foreign: this.calculateMarketVolatility(this.filterArticlesByMarket(articles, 'foreign')),
      crypto: this.calculateMarketVolatility(this.filterArticlesByMarket(articles, 'crypto'))
    };
  }

  /**
   * Assess overall market risk
   */
  private async assessRisk(articles: NewsArticle[]): Promise<{
    level: 'low' | 'medium' | 'high' | 'extreme';
    factors: string[];
    recommendation: string;
  }> {
    const riskFactors: string[] = [];
    let riskScore = 0;

    // Analyze risk indicators
    const volatilityIndex = await this.calculateVolatilityIndex(articles);
    const avgVolatility = (volatilityIndex.indian + volatilityIndex.foreign + volatilityIndex.crypto) / 3;
    
    if (avgVolatility > 0.7) {
      riskFactors.push('High market volatility detected');
      riskScore += 0.3;
    }

    // Check for geopolitical risks
    const geopoliticalRisk = this.assessGeopoliticalRisk(articles);
    if (geopoliticalRisk > 0.5) {
      riskFactors.push('Elevated geopolitical tensions');
      riskScore += 0.2;
    }

    // Check for policy risks
    const policyRisk = this.assessPolicyRisk(articles);
    if (policyRisk > 0.5) {
      riskFactors.push('Significant policy changes anticipated');
      riskScore += 0.2;
    }

    // Check for correlation risks
    const correlations = await this.calculateMarketCorrelations(articles);
    const avgCorrelation = (correlations.indianForeign + correlations.indianCrypto + correlations.foreignCrypto) / 3;
    if (avgCorrelation > 0.8) {
      riskFactors.push('High market correlation increases systemic risk');
      riskScore += 0.15;
    }

    // Determine risk level
    let level: 'low' | 'medium' | 'high' | 'extreme';
    if (riskScore > 0.8) level = 'extreme';
    else if (riskScore > 0.6) level = 'high';
    else if (riskScore > 0.3) level = 'medium';
    else level = 'low';

    const recommendation = this.generateRiskRecommendation(level, riskFactors);

    return { level, factors: riskFactors, recommendation };
  }

  /**
   * Build advanced insight prompt for AI
   */
  private buildAdvancedInsightPrompt(intelligence: MarketIntelligence, articles: NewsArticle[]): string {
    const sentimentSummary = intelligence.overallSentiment.map(s => 
      `${s.market}: ${s.sentiment} (${(s.score * 100).toFixed(1)}%, confidence: ${(s.confidence * 100).toFixed(1)}%)`
    ).join(', ');

    const trendSummary = intelligence.trends.map(t => 
      `${t.market}: ${t.direction} trend (strength: ${(t.strength * 100).toFixed(1)}%)`
    ).join(', ');

    const topNews = intelligence.topImpactNews.slice(0, 5).map((news, index) => {
      const article = articles.find(a => a.id === news.articleId);
      return `${index + 1}. ${article?.title || 'Unknown'} (Impact: ${(news.impactScore * 100).toFixed(1)}%)`;
    }).join('\n');

    return `
As a senior financial analyst with expertise in market intelligence, provide comprehensive market insights based on this data:

**MARKET SENTIMENT ANALYSIS:**
${sentimentSummary}

**TREND ANALYSIS:**
${trendSummary}

**RISK ASSESSMENT:**
Level: ${intelligence.riskAssessment.level}
Factors: ${intelligence.riskAssessment.factors.join(', ')}

**TOP IMPACT NEWS:**
${topNews}

**MARKET CORRELATIONS:**
Indian-Foreign: ${(intelligence.marketCorrelations.indianForeign * 100).toFixed(1)}%
Indian-Crypto: ${(intelligence.marketCorrelations.indianCrypto * 100).toFixed(1)}%
Foreign-Crypto: ${(intelligence.marketCorrelations.foreignCrypto * 100).toFixed(1)}%

**VOLATILITY INDEX:**
Indian: ${(intelligence.volatilityIndex.indian * 100).toFixed(1)}%
Foreign: ${(intelligence.volatilityIndex.foreign * 100).toFixed(1)}%
Crypto: ${(intelligence.volatilityIndex.crypto * 100).toFixed(1)}%

Please provide:

## 🎯 **Executive Summary**
Brief overview of current market conditions and key themes

## 📊 **Market Analysis**
### Indian Markets
- Current sentiment and key drivers
- Notable developments and their implications
- Short-term outlook

### Global Markets  
- International market dynamics
- Cross-market influences
- Geopolitical factors

### Cryptocurrency Markets
- Digital asset trends
- Regulatory developments
- Technology and adoption factors

## ⚡ **Key Opportunities**
- Immediate trading/investment opportunities
- Sector-specific insights
- Risk-adjusted recommendations

## ⚠️ **Risk Factors**
- Primary concerns and their probability
- Potential market disruptors
- Hedging strategies

## 🔮 **Forward Outlook**
- Next 24-48 hour expectations
- Weekly trend predictions
- Key events to monitor

**Guidelines:**
- Use data-driven insights with specific percentages and metrics
- Provide actionable recommendations for different investor types
- Highlight interconnections between markets
- Include confidence levels for predictions
- Use professional financial terminology
- Keep insights concise but comprehensive
- Focus on practical implications for investors

Limit response to 800 words maximum.
`;
  }

  /**
   * Enhance AI insights with quantitative data
   */
  private enhanceInsightWithData(aiInsight: string, intelligence: MarketIntelligence): string {
    // Add real-time data points to AI insights
    const dataEnhancement = `

---

## 📈 **Real-Time Market Intelligence**

**Sentiment Scores:**
- 🇮🇳 Indian Markets: ${this.formatSentimentScore(intelligence.overallSentiment.find(s => s.market === 'indian'))}
- 🌍 Global Markets: ${this.formatSentimentScore(intelligence.overallSentiment.find(s => s.market === 'foreign'))}
- 💰 Crypto Markets: ${this.formatSentimentScore(intelligence.overallSentiment.find(s => s.market === 'crypto'))}

**Market Correlations:**
- Indian ↔ Global: ${(intelligence.marketCorrelations.indianForeign * 100).toFixed(1)}%
- Indian ↔ Crypto: ${(intelligence.marketCorrelations.indianCrypto * 100).toFixed(1)}%
- Global ↔ Crypto: ${(intelligence.marketCorrelations.foreignCrypto * 100).toFixed(1)}%

**Risk Level:** ${intelligence.riskAssessment.level.toUpperCase()}

*Last Updated: ${intelligence.timestamp.toLocaleTimeString()}*
`;

    return aiInsight + dataEnhancement;
  }

  /**
   * Generate fallback insights when AI is unavailable
   */
  private generateFallbackInsights(articles: NewsArticle[]): string {
    const totalArticles = articles.length;
    const recentArticles = articles.filter(a => {
      const hoursAgo = (Date.now() - new Date(a.publishedAt).getTime()) / (1000 * 60 * 60);
      return hoursAgo < 6;
    }).length;

    return `## 📊 **Market Intelligence Summary**

**Current Activity:** ${totalArticles} articles analyzed (${recentArticles} in last 6 hours)

### 🎯 **Key Highlights**
- **Market Activity**: ${recentArticles > 10 ? 'High' : recentArticles > 5 ? 'Moderate' : 'Low'} news flow detected
- **Coverage**: Multi-market analysis across Indian, Global, and Crypto segments
- **Recency**: ${((recentArticles / totalArticles) * 100).toFixed(1)}% of news is recent

### 📈 **Top Headlines**
${articles.slice(0, 5).map((article, index) => 
  `${index + 1}. **${article.title}** - *${article.source.name}*`
).join('\n')}

### ⚡ **Market Insights**
Based on current news flow, markets are showing ${recentArticles > 8 ? 'elevated' : 'normal'} activity levels. 
Key themes include policy developments, corporate earnings, and global economic indicators.

**Note:** *Advanced AI analysis temporarily unavailable. Full intelligence will resume shortly.*

---
*Analysis based on ${totalArticles} financial news articles | Updated: ${new Date().toLocaleTimeString()}*`;
  }

  // Helper methods
  private filterArticlesByMarket(articles: NewsArticle[], market: string): NewsArticle[] {
    // Implementation similar to sentiment analyzer
    return articles.filter(article => {
      if (market === 'indian') return article.category === 'india-specific';
      if (market === 'foreign') return article.category === 'global-markets';
      if (market === 'crypto') return article.category === 'crypto-web3';
      return false;
    });
  }

  private calculateMomentumTrend(articles: NewsArticle[], timeframe: number): number {
    // Momentum calculation based on news frequency and sentiment
    return 0; // Placeholder
  }

  private calculateSentimentVelocity(articles: NewsArticle[], timeframe: number): number {
    // Rate of sentiment change over time
    return 0; // Placeholder
  }

  private calculateVolumeTrend(articles: NewsArticle[], timeframe: number): number {
    // News volume trend analysis
    return 0; // Placeholder
  }

  private calculateImpactWeightedTrend(articles: NewsArticle[], timeframe: number): number {
    // Trend weighted by news impact scores
    return 0; // Placeholder
  }

  private scoreToDirection(score: number): 'upward' | 'downward' | 'sideways' {
    if (score > 0.1) return 'upward';
    if (score < -0.1) return 'downward';
    return 'sideways';
  }

  private calculateTrendConfidence(articles: NewsArticle[], market: string): number {
    return Math.min(articles.length / 10, 1);
  }

  private extractTrendFactors(articles: NewsArticle[], direction: string): {
    supportingFactors: string[];
    riskFactors: string[];
  } {
    return {
      supportingFactors: ['Strong earnings reports', 'Positive policy developments'],
      riskFactors: ['Market volatility', 'Geopolitical tensions']
    };
  }

  private estimateTrendDuration(articles: NewsArticle[], strength: number): 'short' | 'medium' | 'long' {
    if (strength > 0.7) return 'long';
    if (strength > 0.4) return 'medium';
    return 'short';
  }

  private calculateCorrelation(articles1: NewsArticle[], articles2: NewsArticle[]): number {
    // Simplified correlation calculation
    return Math.random() * 0.8; // Placeholder
  }

  private calculateMarketVolatility(articles: NewsArticle[]): number {
    // Volatility calculation based on sentiment variance
    return Math.random() * 0.6; // Placeholder
  }

  private assessGeopoliticalRisk(articles: NewsArticle[]): number {
    const geopoliticalKeywords = ['war', 'conflict', 'sanctions', 'trade war', 'election'];
    const matches = articles.filter(article => {
      const content = `${article.title} ${article.description}`.toLowerCase();
      return geopoliticalKeywords.some(keyword => content.includes(keyword));
    });
    return matches.length / articles.length;
  }

  private assessPolicyRisk(articles: NewsArticle[]): number {
    const policyKeywords = ['policy', 'regulation', 'rate', 'central bank', 'government'];
    const matches = articles.filter(article => {
      const content = `${article.title} ${article.description}`.toLowerCase();
      return policyKeywords.some(keyword => content.includes(keyword));
    });
    return matches.length / articles.length;
  }

  private generateRiskRecommendation(level: string, factors: string[]): string {
    const recommendations = {
      low: 'Maintain current positions with standard risk management',
      medium: 'Consider reducing position sizes and increasing diversification',
      high: 'Implement defensive strategies and hedge key positions',
      extreme: 'Consider significant risk reduction and capital preservation'
    };
    return recommendations[level as keyof typeof recommendations] || recommendations.medium;
  }

  private formatSentimentScore(sentiment: any): string {
    if (!sentiment) return 'N/A';
    const emoji = sentiment.sentiment === 'bullish' ? '📈' : sentiment.sentiment === 'bearish' ? '📉' : '➡️';
    return `${emoji} ${sentiment.sentiment} (${(sentiment.score * 100).toFixed(1)}%)`;
  }

  private generateCacheKey(articles: NewsArticle[]): string {
    const articleIds = articles.map(a => a.id).sort().join(',');
    return `intelligence_${articleIds.substring(0, 50)}`;
  }

  private isCacheValid(intelligence: MarketIntelligence): boolean {
    const age = Date.now() - intelligence.timestamp.getTime();
    return age < this.cacheTimeout;
  }

  private getDefaultIntelligence(): MarketIntelligence {
    return {
      timestamp: new Date(),
      overallSentiment: [],
      trends: [],
      topImpactNews: [],
      marketCorrelations: { indianForeign: 0, indianCrypto: 0, foreignCrypto: 0 },
      volatilityIndex: { indian: 0, foreign: 0, crypto: 0 },
      riskAssessment: { level: 'medium', factors: [], recommendation: 'Monitor market conditions' }
    };
  }
}

export const marketIntelligenceEngine = new MarketIntelligenceEngine();
