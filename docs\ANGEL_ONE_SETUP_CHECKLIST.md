# Angel One Real Connection Checklist

## 📋 **What You Need to Do (Step by Step)**

### **✅ Step 1: Check Prerequisites**

**Do you have an Angel One trading account?**
- [ ] Yes, I have an Angel One demat/trading account
- [ ] No → Create one at https://angelone.in first

**Is your account active?**
- [ ] Yes, I can login and trade
- [ ] No → Activate your account first

### **✅ Step 2: Register for SmartAPI**

**Go to Angel One SmartAPI Portal:**
1. [ ] Visit: https://smartapi.angelbroking.com/
2. [ ] Click "Register" button
3. [ ] Fill registration form with:
   - [ ] Your full name
   - [ ] Email address
   - [ ] Phone number
   - [ ] Angel One Client ID (your trading account ID)
   - [ ] PAN card number
4. [ ] Submit registration
5. [ ] Wait for approval (1-2 business days)

**After approval, you'll receive:**
- [ ] API Key (like: SmartAPI_12345678)
- [ ] Confirmation email with access details

### **✅ Step 3: Disable 2FA (Critical!)**

**This is the most important step:**
1. [ ] Login to Angel One app or website
2. [ ] Go to Profile/Settings
3. [ ] Find "Security Settings" or "Two-Factor Authentication"
4. [ ] Turn OFF 2FA/TOTP
5. [ ] Save changes
6. [ ] Confirm 2FA is disabled

**⚠️ Why this matters:** API doesn't work with 2FA enabled

### **✅ Step 4: Gather Your Credentials**

**You need these 3 pieces of information:**
- [ ] **API Key**: From SmartAPI registration (SmartAPI_12345678)
- [ ] **Client Code**: Your Angel One trading account ID (A123456)
- [ ] **Password**: Your Angel One trading password

**Write them down securely:**
```
API Key: ________________
Client Code: ________________
Password: ________________
```

### **✅ Step 5: Test Your Credentials**

**Use the test script I created:**
1. [ ] Open `test-angel-one-connection.js` file
2. [ ] Replace the placeholder values with your real credentials:
   ```javascript
   const API_KEY = 'your_actual_api_key_here';
   const CLIENT_CODE = 'your_actual_client_code_here';
   const PASSWORD = 'your_actual_trading_password_here';
   ```
3. [ ] Run the test: `node test-angel-one-connection.js`
4. [ ] Check if you see "✅ SUCCESS!" message

**Expected results:**
- [ ] ✅ Success: "Angel One API connection working!"
- [ ] ❌ TOTP Error: Go back to Step 3, disable 2FA
- [ ] ❌ Invalid Credentials: Check Step 4, verify credentials

### **✅ Step 6: Configure Environment Variables**

**Create `.env.local` file in your project root:**
```env
# Angel One SmartAPI Credentials
ANGEL_ONE_API_KEY=your_actual_api_key_here
ANGEL_ONE_CLIENT_CODE=your_actual_client_code_here
ANGEL_ONE_PASSWORD=your_actual_trading_password_here

# Enable real API
NEXT_PUBLIC_ANGEL_ONE_API_KEY=your_actual_api_key_here
NEXT_PUBLIC_ANGEL_ONE_CLIENT_CODE=your_actual_client_code_here
NEXT_PUBLIC_ANGEL_ONE_PASSWORD=your_actual_trading_password_here
NEXT_PUBLIC_ENABLE_REAL_API=true
```

**Replace placeholders with your actual values:**
- [ ] API Key updated
- [ ] Client Code updated  
- [ ] Password updated
- [ ] File saved as `.env.local`

### **✅ Step 7: Test Real Connection**

**In the portfolio application:**
1. [ ] Start development server: `npm run dev`
2. [ ] Visit: http://localhost:3000/portfolio
3. [ ] Toggle to "Real API" mode
4. [ ] Select Angel One broker
5. [ ] Click "Connect to Angel One"
6. [ ] Check for success or error messages

**Expected results:**
- [ ] ✅ Success: Your real portfolio data loads
- [ ] ❌ Error: Check error message and follow guidance

## 🚨 **Common Issues & Solutions**

### **Issue: "Invalid TOTP" Error**
**Solution:**
- [ ] Disable 2FA in Angel One account (Step 3)
- [ ] Wait 5 minutes after disabling
- [ ] Try connection again

### **Issue: "Invalid Credentials" Error**
**Solutions:**
- [ ] Verify API key from SmartAPI dashboard
- [ ] Check client code is your trading account ID
- [ ] Confirm password is your trading password (not login PIN)
- [ ] Ensure no extra spaces in credentials

### **Issue: "API Key Not Found" Error**
**Solutions:**
- [ ] Complete SmartAPI registration (Step 2)
- [ ] Wait for approval email
- [ ] Check spam folder for approval email

### **Issue: "Account Not Verified" Error**
**Solutions:**
- [ ] Wait for SmartAPI approval (can take 1-2 days)
- [ ] Contact Angel One support if delayed
- [ ] Ensure you provided correct Angel One account details

## 📞 **Need Help?**

### **Angel One Support:**
- **Phone**: 040-47 47 47 47
- **Email**: <EMAIL>
- **SmartAPI Support**: https://smartapi.angelbroking.com/

### **What to Ask Support:**
- "I need help with SmartAPI registration"
- "I'm getting TOTP errors with API access"
- "Please help me disable 2FA for API access"
- "I need API credentials for portfolio monitoring"

## ✅ **Success Checklist**

**When everything is working, you should see:**
- [ ] ✅ Test script shows "SUCCESS!" message
- [ ] ✅ Portfolio page connects without errors
- [ ] ✅ Your real holdings data appears
- [ ] ✅ Live portfolio values and P&L
- [ ] ✅ Actual stock quantities and prices

## 🎯 **Alternative: Demo Mode**

**If you can't get real API working immediately:**
- [ ] Keep toggle on "Demo Mode"
- [ ] Test all portfolio features with realistic mock data
- [ ] Set up real API when ready
- [ ] All features work the same way

## 📝 **Final Notes**

**Security:**
- Never share your API credentials
- Don't commit `.env.local` to version control
- Keep credentials secure and private

**Support:**
- Angel One support is very helpful
- SmartAPI registration is free
- API access is free for retail users

**Timeline:**
- SmartAPI registration: 1-2 business days
- 2FA disable: Immediate
- API testing: 5-10 minutes
- Full setup: 1-3 days total

**Ready to start? Begin with Step 1! 🚀**
