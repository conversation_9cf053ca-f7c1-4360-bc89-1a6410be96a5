// News filtering and sorting components

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  Filter, 
  Calendar as CalendarIcon, 
  SortDesc, 
  Clock, 
  TrendingUp, 
  Star,
  X
} from 'lucide-react';
import { format } from 'date-fns';
import { SearchFilters, NewsCategory } from '@/lib/services/news/types';

interface NewsFiltersProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  sortBy: 'publishedAt' | 'relevancy' | 'popularity';
  onSortChange: (sortBy: 'publishedAt' | 'relevancy' | 'popularity') => void;
  className?: string;
}

export function NewsFilters({ 
  filters, 
  onFiltersChange, 
  sortBy, 
  onSortChange,
  className = '' 
}: NewsFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);

  const updateFilters = (newFilters: Partial<SearchFilters>) => {
    onFiltersChange({ ...filters, ...newFilters });
  };

  const clearFilters = () => {
    onFiltersChange({});
  };

  const hasActiveFilters = Object.keys(filters).length > 0;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Filter Toggle and Sort */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
            className="border-slate-600 text-slate-300"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {hasActiveFilters && (
              <Badge variant="secondary" className="ml-2 bg-blue-500/20 text-blue-300">
                {Object.keys(filters).length}
              </Badge>
            )}
          </Button>
          
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-slate-400 hover:text-slate-300"
            >
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        {/* Sort Options */}
        <div className="flex items-center gap-2">
          <SortDesc className="h-4 w-4 text-slate-400" />
          <Select value={sortBy} onValueChange={onSortChange}>
            <SelectTrigger className="w-40 bg-slate-800 border-slate-600 text-slate-300">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-600">
              <SelectItem value="publishedAt" className="text-slate-300">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Latest First
                </div>
              </SelectItem>
              <SelectItem value="relevancy" className="text-slate-300">
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4" />
                  Most Relevant
                </div>
              </SelectItem>
              <SelectItem value="popularity" className="text-slate-300">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Most Popular
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Filter Panel */}
      {isOpen && (
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white text-sm">Filter Options</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            
            {/* Date Range Filter */}
            <div>
              <label className="text-sm font-medium text-slate-300 mb-2 block">
                Date Range
              </label>
              <div className="flex gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-slate-600 text-slate-300 justify-start"
                    >
                      <CalendarIcon className="h-4 w-4 mr-2" />
                      {filters.dateRange?.from ? format(filters.dateRange.from, 'MMM dd') : 'From'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0 bg-slate-800 border-slate-600">
                    <Calendar
                      mode="single"
                      selected={filters.dateRange?.from}
                      onSelect={(date) => updateFilters({
                        dateRange: {
                          from: date || new Date(),
                          to: filters.dateRange?.to || new Date()
                        }
                      })}
                      className="text-slate-300"
                    />
                  </PopoverContent>
                </Popover>
                
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-slate-600 text-slate-300 justify-start"
                    >
                      <CalendarIcon className="h-4 w-4 mr-2" />
                      {filters.dateRange?.to ? format(filters.dateRange.to, 'MMM dd') : 'To'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0 bg-slate-800 border-slate-600">
                    <Calendar
                      mode="single"
                      selected={filters.dateRange?.to}
                      onSelect={(date) => updateFilters({
                        dateRange: {
                          from: filters.dateRange?.from || new Date(),
                          to: date || new Date()
                        }
                      })}
                      className="text-slate-300"
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Sentiment Filter */}
            <div>
              <label className="text-sm font-medium text-slate-300 mb-2 block">
                Sentiment
              </label>
              <div className="flex gap-2">
                {['positive', 'neutral', 'negative'].map((sentiment) => (
                  <Button
                    key={sentiment}
                    variant={filters.sentiment === sentiment ? 'secondary' : 'outline'}
                    size="sm"
                    onClick={() => updateFilters({
                      sentiment: filters.sentiment === sentiment ? undefined : sentiment as any
                    })}
                    className={`border-slate-600 ${
                      filters.sentiment === sentiment 
                        ? sentiment === 'positive' ? 'bg-green-500/20 text-green-300 border-green-500/30' :
                          sentiment === 'negative' ? 'bg-red-500/20 text-red-300 border-red-500/30' :
                          'bg-slate-500/20 text-slate-300 border-slate-500/30'
                        : 'text-slate-300'
                    }`}
                  >
                    {sentiment.charAt(0).toUpperCase() + sentiment.slice(1)}
                  </Button>
                ))}
              </div>
            </div>

            {/* Relevance Score Filter */}
            <div>
              <label className="text-sm font-medium text-slate-300 mb-2 block">
                Minimum Relevance
              </label>
              <div className="flex gap-2">
                {[0.3, 0.5, 0.7, 0.9].map((score) => (
                  <Button
                    key={score}
                    variant={filters.minRelevanceScore === score ? 'secondary' : 'outline'}
                    size="sm"
                    onClick={() => updateFilters({
                      minRelevanceScore: filters.minRelevanceScore === score ? undefined : score
                    })}
                    className="border-slate-600 text-slate-300"
                  >
                    {Math.round(score * 100)}%
                  </Button>
                ))}
              </div>
            </div>

            {/* Quick Date Filters */}
            <div>
              <label className="text-sm font-medium text-slate-300 mb-2 block">
                Quick Filters
              </label>
              <div className="flex flex-wrap gap-2">
                {[
                  { label: 'Last Hour', hours: 1 },
                  { label: 'Last 6 Hours', hours: 6 },
                  { label: 'Last 24 Hours', hours: 24 },
                  { label: 'Last 3 Days', hours: 72 },
                  { label: 'Last Week', hours: 168 }
                ].map(({ label, hours }) => (
                  <Button
                    key={label}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const now = new Date();
                      const from = new Date(now.getTime() - hours * 60 * 60 * 1000);
                      updateFilters({
                        dateRange: { from, to: now }
                      });
                    }}
                    className="border-slate-600 text-slate-300 text-xs"
                  >
                    {label}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {filters.dateRange && (
            <Badge variant="secondary" className="bg-blue-500/20 text-blue-300">
              {format(filters.dateRange.from, 'MMM dd')} - {format(filters.dateRange.to, 'MMM dd')}
              <Button
                variant="ghost"
                size="sm"
                className="ml-1 h-4 w-4 p-0 text-blue-300 hover:text-blue-200"
                onClick={() => updateFilters({ dateRange: undefined })}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          
          {filters.sentiment && (
            <Badge 
              variant="secondary" 
              className={`${
                filters.sentiment === 'positive' ? 'bg-green-500/20 text-green-300' :
                filters.sentiment === 'negative' ? 'bg-red-500/20 text-red-300' :
                'bg-slate-500/20 text-slate-300'
              }`}
            >
              {filters.sentiment} sentiment
              <Button
                variant="ghost"
                size="sm"
                className="ml-1 h-4 w-4 p-0 hover:text-slate-200"
                onClick={() => updateFilters({ sentiment: undefined })}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
          
          {filters.minRelevanceScore && (
            <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">
              Min relevance: {Math.round(filters.minRelevanceScore * 100)}%
              <Button
                variant="ghost"
                size="sm"
                className="ml-1 h-4 w-4 p-0 text-purple-300 hover:text-purple-200"
                onClick={() => updateFilters({ minRelevanceScore: undefined })}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
