"use client";

import { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Brain, 
  Send, 
  Loader2, 
  MessageSquare, 
  Lightbulb,
  TrendingUp,
  BarChart3,
  Globe,
  ArrowLeft,
  Copy,
  CheckCircle
} from 'lucide-react';
import { useRouter } from 'next/navigation';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

export default function AIChatPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [copied, setCopied] = useState(false);
  const [hasAutoSent, setHasAutoSent] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isMountedRef = useRef(true);

  // Get pre-prompted question from URL params
  const prePromptedQuestion = searchParams.get('question');
  const questionCategory = searchParams.get('category');
  const questionComplexity = searchParams.get('complexity');
  const region = searchParams.get('region');

  // Debug logging
  useEffect(() => {
    console.log('🔍 URL Parameters:', {
      prePromptedQuestion,
      questionCategory,
      questionComplexity,
      region,
      hasAutoSent,
      messagesLength: messages.length,
      isLoading
    });

    // Also populate the input field with the question for manual testing
    if (prePromptedQuestion && !hasAutoSent) {
      setInputMessage(prePromptedQuestion);
    }
  }, [prePromptedQuestion, questionCategory, questionComplexity, region, hasAutoSent, messages.length, isLoading]);

  // Auto-send function that doesn't depend on handleSendMessage
  const autoSendMessage = async (messageText: string) => {
    console.log('🚀 Auto-sending pre-prompted question:', messageText.substring(0, 50) + '...');

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'user',
      content: messageText,
      timestamp: new Date()
    };

    setMessages([userMessage]);
    setIsLoading(true);

    try {
      // Call AI API to get response
      const response = await fetch('/api/ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: messageText,
          category: questionCategory,
          complexity: questionComplexity,
          region: region,
          context: 'market_insights'
        }),
      });

      const data = await response.json();

      if (data.success) {
        const aiMessage: ChatMessage = {
          id: `ai-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          type: 'ai',
          content: data.response,
          timestamp: new Date()
        };
        if (isMountedRef.current) {
          setMessages(prev => [...prev, aiMessage]);
        }
      } else {
        throw new Error(data.error || 'Failed to get AI response');
      }
    } catch (error) {
      console.error('Error getting AI response:', error);
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type: 'ai',
        content: 'I apologize, but I encountered an error while processing your question. Please try again or rephrase your question.',
        timestamp: new Date()
      };
      if (isMountedRef.current) {
        setMessages(prev => [...prev, errorMessage]);
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    if (prePromptedQuestion && !hasAutoSent && messages.length === 0 && !isLoading) {
      // Only auto-send once and if no messages exist yet and not currently loading
      setHasAutoSent(true);
      // Use a small delay to ensure component is fully mounted
      const timeoutId = setTimeout(() => {
        autoSendMessage(prePromptedQuestion);
      }, 500);

      // Cleanup timeout on unmount
      return () => clearTimeout(timeoutId);
    }
  }, [prePromptedQuestion, hasAutoSent, messages.length, isLoading]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Cleanup function to mark component as unmounted
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (messageText?: string) => {
    const message = messageText || inputMessage.trim();
    if (!message || isLoading) return; // Prevent duplicate calls when loading

    // Check if this exact message already exists to prevent duplicates
    const messageExists = messages.some(msg => msg.content === message && msg.type === 'user');
    if (messageExists) {
      console.log('🚫 Message already exists, skipping duplicate');
      return;
    }

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'user',
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      // Call AI API to get response
      const response = await fetch('/api/ai-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: message,
          category: questionCategory,
          complexity: questionComplexity,
          region: region,
          context: 'market_insights'
        }),
      });

      const data = await response.json();

      if (data.success) {
        const aiMessage: ChatMessage = {
          id: `ai-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          type: 'ai',
          content: data.response,
          timestamp: new Date()
        };
        if (isMountedRef.current) {
          setMessages(prev => [...prev, aiMessage]);
        }
      } else {
        throw new Error(data.error || 'Failed to get AI response');
      }
    } catch (error) {
      console.error('Error getting AI response:', error);
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type: 'ai',
        content: 'I apologize, but I encountered an error while processing your question. Please try again or rephrase your question.',
        timestamp: new Date()
      };
      if (isMountedRef.current) {
        setMessages(prev => [...prev, errorMessage]);
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
      }
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'market': return <TrendingUp className="h-4 w-4" />;
      case 'sector': return <BarChart3 className="h-4 w-4" />;
      case 'global': return <Globe className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'beginner': return 'bg-green-600';
      case 'intermediate': return 'bg-yellow-600';
      case 'advanced': return 'bg-red-600';
      default: return 'bg-blue-600';
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <div className="border-b border-slate-800 bg-slate-900/95 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                onClick={() => router.back()}
                variant="ghost"
                size="sm"
                className="text-slate-400 hover:text-white"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Insights
              </Button>
              
              <div>
                <h1 className="text-xl font-bold text-white flex items-center gap-2">
                  <Brain className="h-5 w-5 text-blue-400" />
                  AI Market Analysis
                </h1>
                {questionCategory && (
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className="text-xs border-slate-600">
                      {getCategoryIcon(questionCategory)}
                      <span className="ml-1 capitalize">{questionCategory}</span>
                    </Badge>
                    {questionComplexity && (
                      <Badge className={`text-xs ${getComplexityColor(questionComplexity)}`}>
                        {questionComplexity}
                      </Badge>
                    )}
                    {region && (
                      <Badge variant="secondary" className="text-xs">
                        {region.toUpperCase()}
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Chat Container */}
      <div className="max-w-4xl mx-auto px-4 py-6">
        <Card className="bg-slate-800 border-slate-700 h-[calc(100vh-200px)] flex flex-col">
          {/* Messages Area */}
          <CardContent className="flex-1 overflow-y-auto p-6 space-y-4">
            {messages.length === 0 && !isLoading && (
              <div className="text-center py-12">
                <Brain className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">AI Market Analysis Assistant</h3>
                <p className="text-slate-400">
                  Ask me anything about markets, stocks, sectors, or trading strategies.
                </p>
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg p-4 ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-slate-700 text-slate-100'
                  }`}
                >
                  <div className="flex items-start gap-2">
                    {message.type === 'ai' && (
                      <Brain className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
                    )}
                    <div className="flex-1">
                      <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs opacity-70">
                          {message.timestamp.toLocaleTimeString()}
                        </span>
                        {message.type === 'ai' && (
                          <Button
                            onClick={() => copyToClipboard(message.content)}
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 opacity-70 hover:opacity-100"
                          >
                            {copied ? (
                              <CheckCircle className="h-3 w-3" />
                            ) : (
                              <Copy className="h-3 w-3" />
                            )}
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-slate-700 rounded-lg p-4 flex items-center gap-2">
                  <Brain className="h-5 w-5 text-blue-400" />
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-slate-300">Analyzing your question...</span>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </CardContent>

          {/* Input Area */}
          <div className="border-t border-slate-700 p-4">
            <div className="flex gap-2">
              <Input
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me about markets, stocks, or trading strategies..."
                className="flex-1 bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                disabled={isLoading}
              />
              <Button
                onClick={() => handleSendMessage()}
                disabled={isLoading || !inputMessage.trim()}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
