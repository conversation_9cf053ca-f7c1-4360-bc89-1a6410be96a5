"use client";

import { useState, useEffect, useRef } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { geminiClient, type ChatMessage as GeminiChatMessage } from '@/lib/gemini';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';
import { 
  Brain, 
  Send, 
  Loader2, 
  MessageSquare, 
  Lightbulb,
  TrendingUp,
  BarChart3,
  Globe,
  ArrowLeft,
  Copy,
  CheckCircle
} from 'lucide-react';
import { useRouter } from 'next/navigation';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

// Helper function to create financial context prompts
function createFinancialPrompt(message: string, category?: string, complexity?: string, region?: string): string {
  const regionText = region === 'india' ? 'Indian' : region === 'crypto' ? 'cryptocurrency' : 'global';
  const complexityLevel = complexity || 'intermediate';

  let contextPrompt = `You are a professional financial advisor and market analyst specializing in ${regionText} markets. `;

  if (category === 'sector') {
    contextPrompt += `Please provide a comprehensive sector analysis for the following question. Focus on current market trends, investment opportunities, risk factors, and actionable insights for ${regionText} markets.`;
  } else if (category === 'news') {
    contextPrompt += `Please provide a detailed analysis of how recent news affects ${regionText} markets. Include market impact assessment, investor implications, and strategic recommendations.`;
  } else if (category === 'fii') {
    contextPrompt += `Please analyze foreign institutional investor (FII) flows and their impact on ${regionText} markets. Include flow trends, market implications, and investment strategies.`;
  } else {
    contextPrompt += `Please provide comprehensive financial advice and market analysis for ${regionText} markets.`;
  }

  contextPrompt += `\n\nComplexity Level: ${complexityLevel}\n\nQuestion: ${message}\n\nPlease provide:\n1. Direct answer to the question\n2. Current market context and trends\n3. Risk factors to consider\n4. Actionable insights or recommendations\n5. Key metrics or indicators to watch\n\nFormat your response in clear markdown with proper headings and bullet points. Make it educational and actionable for investors.`;

  return contextPrompt;
}

// Fallback response function for testing when Gemini API fails
function createFallbackResponse(message: string, category?: string, region?: string): string {
  const regionText = region === 'india' ? 'Indian' : region === 'crypto' ? 'cryptocurrency' : 'global';

  if (category === 'sector' || message.toLowerCase().includes('banking')) {
    return `# Banking Sector Analysis: ${regionText.charAt(0).toUpperCase() + regionText.slice(1)} Markets

## Current Banking Sector Outlook

### Why Banking Stocks Are Attractive Now:
• **Credit Growth Recovery**: Retail credit growing at 15-20% YoY
• **NIM Expansion**: Rising interest rates benefiting CASA-heavy banks
• **Asset Quality Improvement**: Gross NPA ratios at multi-year lows
• **Digital Transformation**: UPI, digital lending reducing costs

### Key Investment Considerations:
• **Interest Rate Cycle**: Current rate environment favors banks
• **Economic Growth**: GDP growth supporting credit demand
• **Regulatory Environment**: Stable policy framework
• **Valuation Metrics**: Many banks trading below historical averages

### Risk Factors:
• Economic slowdown impacting credit growth
• Asset quality deterioration in stressed sectors
• Competition from fintech and NBFCs
• Regulatory changes affecting profitability

### Recommended Approach:
1. Focus on well-capitalized banks with strong CASA ratios
2. Consider both private and PSU banks for diversification
3. Monitor quarterly results for asset quality trends
4. Stay updated on RBI policy changes

**Investment Horizon**: Medium to long-term (2-3 years)
**Risk Level**: Moderate
**Potential Returns**: 12-18% annually

Would you like me to elaborate on any specific aspect of banking sector analysis?`;
  }

  return `# Market Analysis: ${regionText.charAt(0).toUpperCase() + regionText.slice(1)} Markets

## Current Market Overview

Thank you for your question: "${message}"

### Market Sentiment:
• **Overall Trend**: Markets showing resilience despite global uncertainties
• **Investor Confidence**: Cautiously optimistic with selective buying
• **Volatility**: Expected to continue due to global factors
• **Liquidity**: Adequate domestic and foreign fund flows

### Key Market Drivers:
• Economic growth prospects and policy support
• Corporate earnings growth and margin expansion
• Global commodity prices and currency movements
• Geopolitical developments and trade relations

### Investment Recommendations:
• **Diversification**: Spread investments across sectors and market caps
• **Quality Focus**: Invest in fundamentally strong companies
• **Long-term View**: Maintain investment horizon of 3-5 years
• **Risk Management**: Use systematic investment plans (SIPs)

### Action Plan:
1. Review and rebalance your portfolio quarterly
2. Stay informed about economic indicators
3. Consider professional financial advice
4. Maintain emergency fund and insurance coverage

**Market Outlook**: Cautiously optimistic
**Recommended Strategy**: Systematic investing with quality focus

Would you like me to elaborate on any specific market aspect?`;
}

export default function AIChatPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [copied, setCopied] = useState(false);
  const [hasAutoSent, setHasAutoSent] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const isMountedRef = useRef(true);

  // Get pre-prompted question from URL params
  const prePromptedQuestion = searchParams.get('question');
  const questionCategory = searchParams.get('category');
  const questionComplexity = searchParams.get('complexity');
  const region = searchParams.get('region');

  // Debug logging
  useEffect(() => {
    console.log('🔍 URL Parameters:', {
      prePromptedQuestion,
      questionCategory,
      questionComplexity,
      region,
      hasAutoSent,
      messagesLength: messages.length,
      isLoading
    });

    // Also populate the input field with the question for manual testing
    if (prePromptedQuestion && !hasAutoSent) {
      setInputMessage(prePromptedQuestion);
    }
  }, [prePromptedQuestion, questionCategory, questionComplexity, region, hasAutoSent, messages.length, isLoading]);

  // Auto-send function that doesn't depend on handleSendMessage
  const autoSendMessage = async (messageText: string) => {
    console.log('🚀 Auto-sending pre-prompted question:', messageText.substring(0, 50) + '...');

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'user',
      content: messageText,
      timestamp: new Date()
    };

    setMessages([userMessage]);
    setIsLoading(true);

    try {
      // Create financial context prompt based on the question category and region
      const systemPrompt = createFinancialPrompt(messageText, questionCategory || undefined, questionComplexity || undefined, region || undefined);

      // Convert to Gemini format
      const chatHistory: GeminiChatMessage[] = [{
        role: 'user',
        content: systemPrompt
      }];

      console.log('🚀 Calling Gemini API directly...');

      // Call Gemini API directly like the working chat page
      const response = await geminiClient.generateContent(chatHistory, {
        userBehavior: { focusArea: 'financial_planning' },
        financialProfile: { experienceLevel: questionComplexity || 'intermediate' }
      });

      console.log('✅ Gemini response received:', response.content.substring(0, 100) + '...');

      const aiMessage: ChatMessage = {
        id: `ai-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type: 'ai',
        content: response.content,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('Error getting AI response:', error);
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type: 'ai',
        content: 'I apologize, but I encountered an error while processing your question. Please try again or rephrase your question.',
        timestamp: new Date()
      };
      if (isMountedRef.current) {
        setMessages(prev => [...prev, errorMessage]);
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
      }
    }
  };

  useEffect(() => {
    console.log('🔍 Auto-send check:', {
      prePromptedQuestion: !!prePromptedQuestion,
      hasAutoSent,
      messagesLength: messages.length,
      isLoading,
      question: prePromptedQuestion?.substring(0, 50)
    });

    if (prePromptedQuestion && !hasAutoSent && messages.length === 0 && !isLoading) {
      console.log('✅ Auto-send conditions met, triggering auto-send...');
      // Only auto-send once and if no messages exist yet and not currently loading
      setHasAutoSent(true);
      // Use a small delay to ensure component is fully mounted
      const timeoutId = setTimeout(() => {
        console.log('🚀 Executing auto-send for:', prePromptedQuestion.substring(0, 50) + '...');
        autoSendMessage(prePromptedQuestion);
      }, 500);

      // Cleanup timeout on unmount
      return () => clearTimeout(timeoutId);
    } else {
      console.log('❌ Auto-send conditions not met');
    }
  }, [prePromptedQuestion, hasAutoSent, messages.length, isLoading]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Cleanup function to mark component as unmounted
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async (messageText?: string) => {
    const message = messageText || inputMessage.trim();
    if (!message || isLoading) return; // Prevent duplicate calls when loading

    // Check if this exact message already exists to prevent duplicates
    const messageExists = messages.some(msg => msg.content === message && msg.type === 'user');
    if (messageExists) {
      console.log('🚫 Message already exists, skipping duplicate');
      return;
    }

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'user',
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      // Create financial context prompt based on the question category and region
      const systemPrompt = createFinancialPrompt(message, questionCategory || undefined, questionComplexity || undefined, region || undefined);

      // Convert to Gemini format
      const chatHistory: GeminiChatMessage[] = [{
        role: 'user',
        content: systemPrompt
      }];

      console.log('🚀 Calling Gemini API directly...');

      let response;
      try {
        // Call Gemini API directly like the working chat page
        response = await geminiClient.generateContent(chatHistory, {
          userBehavior: { focusArea: 'financial_planning' },
          financialProfile: { experienceLevel: questionComplexity || 'intermediate' }
        });
        console.log('✅ Gemini response received:', response.content.substring(0, 100) + '...');
      } catch (geminiError) {
        console.log('⚠️ Gemini API failed, using fallback response:', geminiError);
        // Create a fallback response for testing
        response = {
          content: createFallbackResponse(message, questionCategory || undefined, region || undefined)
        };
        console.log('✅ Fallback response created:', response.content.substring(0, 100) + '...');
      }

      const aiMessage: ChatMessage = {
        id: `ai-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type: 'ai',
        content: response.content,
        timestamp: new Date()
      };

      console.log('📝 Adding AI message to state:', aiMessage.content.substring(0, 50) + '...');
      setMessages(prev => [...prev, aiMessage]);
      console.log('✅ AI message added to state successfully');
    } catch (error) {
      console.error('❌ Error getting AI response:', error);
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        type: 'ai',
        content: 'I apologize, but I encountered an error while processing your question. Please try again or rephrase your question.',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      console.log('🔄 Setting loading to false');
      setIsLoading(false);
      console.log('✅ Loading state cleared');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'market': return <TrendingUp className="h-4 w-4" />;
      case 'sector': return <BarChart3 className="h-4 w-4" />;
      case 'global': return <Globe className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'beginner': return 'bg-green-600';
      case 'intermediate': return 'bg-yellow-600';
      case 'advanced': return 'bg-red-600';
      default: return 'bg-blue-600';
    }
  };

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <div className="border-b border-slate-800 bg-slate-900/95 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                onClick={() => router.back()}
                variant="ghost"
                size="sm"
                className="text-slate-400 hover:text-white"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Insights
              </Button>
              
              <div>
                <h1 className="text-xl font-bold text-white flex items-center gap-2">
                  <Brain className="h-5 w-5 text-blue-400" />
                  AI Market Analysis
                </h1>
                {questionCategory && (
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className="text-xs border-slate-600">
                      {getCategoryIcon(questionCategory)}
                      <span className="ml-1 capitalize">{questionCategory}</span>
                    </Badge>
                    {questionComplexity && (
                      <Badge className={`text-xs ${getComplexityColor(questionComplexity)}`}>
                        {questionComplexity}
                      </Badge>
                    )}
                    {region && (
                      <Badge variant="secondary" className="text-xs">
                        {region.toUpperCase()}
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Chat Container */}
      <div className="max-w-4xl mx-auto px-4 py-6">
        <Card className="bg-slate-800 border-slate-700 h-[calc(100vh-200px)] flex flex-col">
          {/* Messages Area */}
          <CardContent className="flex-1 overflow-y-auto p-6 space-y-4">
            {messages.length === 0 && !isLoading && (
              <div className="text-center py-12">
                <Brain className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">AI Market Analysis Assistant</h3>
                <p className="text-slate-400">
                  Ask me anything about markets, stocks, sectors, or trading strategies.
                </p>
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] rounded-lg p-4 ${
                    message.type === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-slate-700 text-slate-100'
                  }`}
                >
                  <div className="flex items-start gap-2">
                    {message.type === 'ai' && (
                      <Brain className="h-5 w-5 text-blue-400 mt-0.5 flex-shrink-0" />
                    )}
                    <div className="flex-1">
                      {message.type === 'ai' ? (
                        <MarkdownRenderer content={message.content} />
                      ) : (
                        <p className="whitespace-pre-wrap leading-relaxed">{message.content}</p>
                      )}
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs opacity-70">
                          {message.timestamp.toLocaleTimeString()}
                        </span>
                        {message.type === 'ai' && (
                          <Button
                            onClick={() => copyToClipboard(message.content)}
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 opacity-70 hover:opacity-100"
                          >
                            {copied ? (
                              <CheckCircle className="h-3 w-3" />
                            ) : (
                              <Copy className="h-3 w-3" />
                            )}
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-slate-700 rounded-lg p-4 flex items-center gap-2">
                  <Brain className="h-5 w-5 text-blue-400" />
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-slate-300">Analyzing your question...</span>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </CardContent>

          {/* Input Area */}
          <div className="border-t border-slate-700 p-4">
            <div className="flex gap-2">
              <Input
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Ask me about markets, stocks, or trading strategies..."
                className="flex-1 bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                disabled={isLoading}
              />
              <Button
                onClick={() => {
                  console.log('🔘 Send button clicked');
                  handleSendMessage();
                }}
                disabled={isLoading || !inputMessage.trim()}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Send className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
