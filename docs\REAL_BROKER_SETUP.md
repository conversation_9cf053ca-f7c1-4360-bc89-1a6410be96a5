# Real Broker Integration Setup Guide

## Quick Start: Enable Real Broker APIs

### Step 1: Choose Your Broker

**Recommended for Beginners: Angel One SmartAPI (Free)**
- ✅ Free API access for retail users
- ✅ Simple authentication (no OAuth)
- ✅ Good documentation
- ✅ Active support

**For Advanced Users: Zerodha Kite Connect**
- ⚠️ ₹2,000/month subscription
- ✅ Most comprehensive API
- ✅ Excellent documentation
- ✅ Large developer community

### Step 2: Register for API Access

#### Angel One SmartAPI (Recommended)
1. **Visit**: https://smartapi.angelbroking.com/
2. **Register**: Create developer account
3. **Get Credentials**:
   - API Key
   - Client Code (your trading account ID)
   - Password (your trading password)

#### Zerodha Kite Connect
1. **Visit**: https://developers.kite.trade/
2. **Register**: Create developer account
3. **Create App**: Get API key and secret
4. **Subscribe**: Pay ₹2,000/month

### Step 3: Configure Environment Variables

Create a `.env.local` file in your project root:

```env
# Angel One SmartAPI
ANGEL_ONE_API_KEY=your_api_key_here
ANGEL_ONE_CLIENT_CODE=your_client_code_here
ANGEL_ONE_PASSWORD=your_password_here

# Zerodha Kite Connect
ZERODHA_API_KEY=your_api_key_here
ZERODHA_API_SECRET=your_api_secret_here

# Application Settings
NEXT_PUBLIC_BASE_URL=http://localhost:3000
ENABLE_REAL_BROKER_API=true
```

### Step 4: Update Portfolio Page

Add real API toggle to your portfolio page:

```typescript
// In app/portfolio/page.tsx
const connectBroker = async (brokerId: string) => {
  setIsLoading(true);
  setSelectedBroker(brokerId);
  
  // Check if real API is enabled
  const useRealAPI = process.env.NEXT_PUBLIC_ENABLE_REAL_API === 'true';
  
  try {
    const response = await fetch('/api/portfolio/connect', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        brokerId,
        credentials: {
          // For Angel One
          apiKey: process.env.NEXT_PUBLIC_ANGEL_ONE_API_KEY,
          clientCode: process.env.NEXT_PUBLIC_ANGEL_ONE_CLIENT_CODE,
          password: process.env.NEXT_PUBLIC_ANGEL_ONE_PASSWORD,
          // For Zerodha (OAuth flow)
          // apiKey: process.env.NEXT_PUBLIC_ZERODHA_API_KEY,
          // apiSecret: process.env.NEXT_PUBLIC_ZERODHA_API_SECRET
        },
        useRealAPI
      })
    });

    const data = await response.json();
    
    if (data.success) {
      setIsConnected(true);
      setPortfolioData(data.data);
    } else if (data.requiresOAuth) {
      // Redirect to OAuth URL for Zerodha
      window.location.href = data.oauthUrl;
    } else {
      throw new Error(data.error);
    }
  } catch (error) {
    console.error('Connection failed:', error);
    // Fallback to mock data
    setPortfolioData(mockPortfolioData);
  } finally {
    setIsLoading(false);
  }
};
```

### Step 5: Test Real Connection

1. **Start Development Server**:
   ```bash
   npm run dev
   ```

2. **Navigate to Portfolio**: http://localhost:3000/portfolio

3. **Select Angel One**: Click on Angel One broker card

4. **Connect**: Click "Connect to Angel One"

5. **Verify**: Check if real portfolio data loads

### Step 6: Handle OAuth (For Zerodha)

Create OAuth callback handler:

```typescript
// app/auth/zerodha/callback/page.tsx
'use client';

import { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

export default function ZerodhaCallback() {
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const requestToken = searchParams.get('request_token');
    const status = searchParams.get('status');

    if (status === 'success' && requestToken) {
      // Exchange request token for access token
      fetch('/api/auth/zerodha/token', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ requestToken })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          // Redirect back to portfolio with success
          router.push('/portfolio?connected=zerodha');
        } else {
          router.push('/portfolio?error=auth_failed');
        }
      });
    } else {
      router.push('/portfolio?error=auth_cancelled');
    }
  }, []);

  return (
    <div className="min-h-screen bg-slate-900 flex items-center justify-center">
      <div className="text-white text-center">
        <h1 className="text-2xl mb-4">Connecting to Zerodha...</h1>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
      </div>
    </div>
  );
}
```

## Security Best Practices

### 1. Environment Variables
- Never commit API keys to version control
- Use different keys for development/production
- Rotate keys regularly

### 2. Token Storage
- Store access tokens encrypted in database
- Implement token refresh logic
- Set appropriate expiration times

### 3. Error Handling
- Never expose API keys in error messages
- Log errors securely
- Implement rate limiting

## Troubleshooting

### Common Issues

#### "Authentication Failed"
- **Check**: API credentials are correct
- **Verify**: Environment variables are loaded
- **Test**: API credentials directly with broker

#### "OAuth Required"
- **For Zerodha**: Implement OAuth callback handler
- **Check**: Redirect URL matches registered URL
- **Verify**: API key and secret are correct

#### "Rate Limit Exceeded"
- **Implement**: Request throttling
- **Add**: Caching layer
- **Check**: API usage limits

#### "Network Error"
- **Check**: Internet connectivity
- **Verify**: Broker API status
- **Implement**: Retry logic with exponential backoff

### Debug Mode

Enable debug logging:

```env
DEBUG_BROKER_API=true
```

This will log all API requests and responses for troubleshooting.

## Production Deployment

### 1. Database Setup
- Set up PostgreSQL/MySQL for token storage
- Implement user authentication
- Create broker connection tables

### 2. Security
- Use HTTPS only
- Implement CSRF protection
- Add request signing
- Set up monitoring

### 3. Scaling
- Implement Redis caching
- Add load balancing
- Set up error monitoring
- Configure auto-scaling

## Support

### Getting Help
1. **Check Documentation**: Broker API docs
2. **Community Forums**: Broker developer communities
3. **Support Tickets**: Contact broker support
4. **GitHub Issues**: Report integration bugs

### Useful Links
- **Angel One Support**: https://smartapi.angelbroking.com/docs
- **Zerodha Support**: https://kite.trade/forum/
- **Upstox Support**: https://upstox.com/developer/

## Next Steps

1. **Start with Angel One**: Easiest to implement
2. **Add Error Handling**: Robust error management
3. **Implement Caching**: Reduce API calls
4. **Add More Brokers**: Expand broker support
5. **Production Deploy**: Scale for real users
