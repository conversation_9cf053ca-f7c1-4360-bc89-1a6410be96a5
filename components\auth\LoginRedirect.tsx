'use client'

import { useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'

interface LoginRedirectProps {
  redirectTo?: string
}

export default function LoginRedirect({ redirectTo = '/' }: LoginRedirectProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === 'authenticated' && session) {
      console.log('🔄 User authenticated, redirecting to:', redirectTo)
      // Use multiple redirect methods to ensure it works
      setTimeout(() => {
        router.push(redirectTo)
        router.refresh()
      }, 100)
      
      // Fallback redirect
      setTimeout(() => {
        if (window.location.pathname !== redirectTo) {
          window.location.href = redirectTo
        }
      }, 1000)
    }
  }, [status, session, redirectTo, router])

  if (status === 'authenticated') {
    return (
      <div className="fixed inset-0 bg-slate-900 flex items-center justify-center z-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-white">Redirecting to dashboard...</p>
        </div>
      </div>
    )
  }

  return null
}
