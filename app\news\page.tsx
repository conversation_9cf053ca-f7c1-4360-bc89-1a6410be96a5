"use client";

import { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import { NewsGridSkeleton } from '@/components/ui/news-skeleton';
import { NewsErrorBoundary, NewsLoadingState, NewsEmptyState, NetworkStatus } from '@/components/ui/news-error-boundary';
import { NewsFilters } from '@/components/ui/news-filters';
import { 
  Search, 
  RefreshCw, 
  Clock, 
  ExternalLink,
  TrendingUp,
  Globe,
  Bitcoin,
  Building2,
  Landmark,
  Newspaper,
  Filter,
  SortDesc,
  Loader2
} from 'lucide-react';
import { newsService, NewsArticle, NewsCategory, NEWS_CATEGORIES, SearchFilters } from '@/lib/services/news';

export default function NewsPage() {
  const [selectedCategory, setSelectedCategory] = useState<NewsCategory>('india-specific');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<NewsArticle[]>([]);
  const [categoryNews, setCategoryNews] = useState<Record<NewsCategory, NewsArticle[]>>({
    'india-specific': [],
    'global-markets': [],
    'crypto-web3': [],
    'stocks-companies': [],
    'macro-policy': [],
    'general': []
  });
  const [trendingNews, setTrendingNews] = useState<NewsArticle[]>([]);
  const [marketInsights, setMarketInsights] = useState<string>('');
  const [keyThemes, setKeyThemes] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});
  const [sortBy, setSortBy] = useState<'publishedAt' | 'relevancy' | 'popularity'>('publishedAt');
  const [error, setError] = useState<string | null>(null);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Load search suggestions when query changes
  useEffect(() => {
    if (searchQuery.length > 2) {
      loadSearchSuggestions();
    } else {
      setSearchSuggestions([]);
      setShowSuggestions(false);
    }
  }, [searchQuery]);

  const loadInitialData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Load trending news and insights
      const trending = await newsService.getTrendingNews();
      setTrendingNews(trending.articles);
      setMarketInsights(trending.insights);
      setKeyThemes(trending.themes);

      // Load news for current category
      await loadCategoryNews(selectedCategory);

      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error loading initial data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load news data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCategoryNews = async (category: NewsCategory) => {
    try {
      const response = await newsService.getNewsByCategory(category, true);
      if (response.status === 'ok') {
        setCategoryNews(prev => ({
          ...prev,
          [category]: response.articles
        }));
      }
    } catch (error) {
      console.error(`Error loading ${category} news:`, error);
    }
  };

  const loadSearchSuggestions = async () => {
    try {
      const suggestions = await newsService.getSearchSuggestions(searchQuery);
      setSearchSuggestions(suggestions);
      setShowSuggestions(suggestions.length > 0);
    } catch (error) {
      console.error('Error loading search suggestions:', error);
    }
  };

  const handleSearch = async (query?: string) => {
    const searchTerm = query || searchQuery;
    if (!searchTerm.trim()) return;

    setIsSearching(true);
    setShowSuggestions(false);
    setError(null);

    try {
      const response = await newsService.searchNews(searchTerm, {
        ...searchFilters,
        minRelevanceScore: searchFilters.minRelevanceScore || 0.3
      }, true);

      if (response.status === 'ok') {
        setSearchResults(response.articles);
      } else {
        setError(response.message || 'Search failed');
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Error searching news:', error);
      setError(error instanceof Error ? error.message : 'Search failed');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleCategoryChange = async (category: NewsCategory) => {
    setSelectedCategory(category);
    
    // Load category news if not already loaded
    if (categoryNews[category].length === 0) {
      await loadCategoryNews(category);
    }
  };

  const handleRefresh = () => {
    loadInitialData();
  };

  const getCategoryIcon = (category: NewsCategory) => {
    const icons = {
      'india-specific': '🇮🇳',
      'global-markets': '🌍',
      'crypto-web3': '💰',
      'stocks-companies': '📊',
      'macro-policy': '🏦',
      'general': '📰'
    };
    return icons[category];
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const NewsCard = ({ article }: { article: NewsArticle }) => (
    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all duration-300">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Badge variant="outline" className="text-xs">
                {getCategoryIcon(article.category)} {NEWS_CATEGORIES[article.category].name.replace(/^.{2}\s/, '')}
              </Badge>
              <span className="text-xs text-slate-400">{formatTimeAgo(article.publishedAt)}</span>
            </div>
            <CardTitle className="text-white text-sm leading-tight line-clamp-2">
              {article.title}
            </CardTitle>
          </div>
          {article.urlToImage && (
            <img 
              src={article.urlToImage} 
              alt="" 
              className="w-16 h-16 rounded-lg object-cover flex-shrink-0"
              onError={(e) => {
                (e.target as HTMLImageElement).style.display = 'none';
              }}
            />
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-slate-300 line-clamp-3 mb-3">
          {article.summary || article.description}
        </p>
        
        {article.tags && article.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {article.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs bg-slate-700 text-slate-300">
                {tag}
              </Badge>
            ))}
          </div>
        )}
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-xs text-slate-400">{article.source.name}</span>
            {article.sentiment && (
              <Badge 
                variant="outline" 
                className={`text-xs ${
                  article.sentiment === 'positive' ? 'border-green-500/30 text-green-300' :
                  article.sentiment === 'negative' ? 'border-red-500/30 text-red-300' :
                  'border-slate-500/30 text-slate-300'
                }`}
              >
                {article.sentiment}
              </Badge>
            )}
          </div>
          <Button 
            size="sm" 
            variant="ghost" 
            className="text-blue-400 hover:text-blue-300 p-1"
            onClick={() => window.open(article.url, '_blank')}
          >
            <ExternalLink className="h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <NewsErrorBoundary>
      <NetworkStatus />
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
        <div className="container mx-auto px-4 py-8">
          <div className="space-y-8">
          
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold text-white">Financial News Intelligence</h1>
              <p className="text-slate-300">Real-time financial news with AI-powered insights and analysis</p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-green-500/10 text-green-300 border-green-500/30">
                <Clock className="h-3 w-3 mr-1" />
                Updated: {lastUpdated.toLocaleTimeString()}
              </Badge>
              <Button 
                size="sm" 
                variant="outline"
                onClick={handleRefresh}
                disabled={isLoading}
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </div>

          {/* Search Section */}
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Search className="h-5 w-5 text-blue-400" />
                Search Financial News
              </CardTitle>
              <CardDescription className="text-slate-400">
                Search for specific topics like "inflation in India" or "TCS earnings"
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative">
                <div className="flex gap-2">
                  <div className="flex-1 relative">
                    <Input
                      placeholder="Search financial news..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                      className="bg-slate-700 border-slate-600 text-white"
                    />
                    
                    {/* Search Suggestions */}
                    {showSuggestions && searchSuggestions.length > 0 && (
                      <div className="absolute top-full left-0 right-0 mt-1 bg-slate-800 border border-slate-600 rounded-md shadow-lg z-10">
                        {searchSuggestions.map((suggestion, index) => (
                          <button
                            key={index}
                            className="w-full text-left px-3 py-2 text-sm text-slate-300 hover:bg-slate-700 first:rounded-t-md last:rounded-b-md"
                            onClick={() => {
                              setSearchQuery(suggestion);
                              handleSearch(suggestion);
                            }}
                          >
                            {suggestion}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                  <Button 
                    onClick={() => handleSearch()}
                    disabled={isSearching || !searchQuery.trim()}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {isSearching ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Search className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Search Filters */}
              <NewsFilters
                filters={searchFilters}
                onFiltersChange={setSearchFilters}
                sortBy={sortBy}
                onSortChange={setSortBy}
                className="mt-4"
              />

              {/* Search Results */}
              {error && (
                <div className="mt-6">
                  <NewsEmptyState
                    title="Search Error"
                    description={error}
                    onRetry={() => handleSearch()}
                  />
                </div>
              )}

              {!error && searchResults.length > 0 && (
                <div className="mt-6">
                  <h3 className="text-lg font-semibold text-white mb-4">
                    Search Results ({searchResults.length})
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {searchResults.slice(0, 9).map((article) => (
                      <NewsCard key={article.id} article={article} />
                    ))}
                  </div>
                </div>
              )}

              {!error && searchQuery && searchResults.length === 0 && !isSearching && (
                <div className="mt-6">
                  <NewsEmptyState
                    title="No Results Found"
                    description={`No news found for "${searchQuery}". Try different keywords or adjust your filters.`}
                    onRetry={() => handleSearch()}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Market Insights */}
          {marketInsights && (
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-400" />
                  Market Insights
                </CardTitle>
                <CardDescription className="text-slate-400">
                  AI-generated analysis of current market trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-slate-300 leading-relaxed">{marketInsights}</p>

                {keyThemes.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-white mb-2">Key Themes:</h4>
                    <div className="flex flex-wrap gap-2">
                      {keyThemes.map((theme, index) => (
                        <Badge key={index} variant="secondary" className="bg-blue-500/20 text-blue-300">
                          {theme}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* News Categories */}
          <Tabs value={selectedCategory} onValueChange={(value) => handleCategoryChange(value as NewsCategory)} className="space-y-6">
            <TabsList className="bg-slate-800 border-slate-700 grid grid-cols-3 lg:grid-cols-6 w-full">
              <TabsTrigger value="india-specific" className="data-[state=active]:bg-slate-700 text-xs">
                🇮🇳 India
              </TabsTrigger>
              <TabsTrigger value="global-markets" className="data-[state=active]:bg-slate-700 text-xs">
                🌍 Global
              </TabsTrigger>
              <TabsTrigger value="crypto-web3" className="data-[state=active]:bg-slate-700 text-xs">
                💰 Crypto
              </TabsTrigger>
              <TabsTrigger value="stocks-companies" className="data-[state=active]:bg-slate-700 text-xs">
                📊 Stocks
              </TabsTrigger>
              <TabsTrigger value="macro-policy" className="data-[state=active]:bg-slate-700 text-xs">
                🏦 Policy
              </TabsTrigger>
              <TabsTrigger value="general" className="data-[state=active]:bg-slate-700 text-xs">
                📰 General
              </TabsTrigger>
            </TabsList>

            {Object.entries(NEWS_CATEGORIES).map(([categoryKey, categoryConfig]) => (
              <TabsContent key={categoryKey} value={categoryKey} className="space-y-6">
                <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <span className="text-xl">{categoryConfig.icon}</span>
                      {categoryConfig.name}
                    </CardTitle>
                    <CardDescription className="text-slate-400">
                      {categoryConfig.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {isLoading ? (
                      <NewsGridSkeleton count={6} />
                    ) : error ? (
                      <NewsEmptyState
                        title="Failed to Load News"
                        description={error}
                        onRetry={() => loadCategoryNews(categoryKey as NewsCategory)}
                      />
                    ) : categoryNews[categoryKey as NewsCategory].length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {categoryNews[categoryKey as NewsCategory].map((article) => (
                          <NewsCard key={article.id} article={article} />
                        ))}
                      </div>
                    ) : (
                      <NewsEmptyState
                        title="No News Available"
                        description={`No ${categoryConfig.name.replace(/^.{2}\s/, '').toLowerCase()} news found. Try refreshing or check back later.`}
                        onRetry={() => loadCategoryNews(categoryKey as NewsCategory)}
                      />
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>

          {/* Trending News Section */}
          {trendingNews.length > 0 && (
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-orange-400" />
                  Trending Financial News
                </CardTitle>
                <CardDescription className="text-slate-400">
                  Most popular financial stories right now
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {trendingNews.slice(0, 6).map((article) => (
                    <NewsCard key={article.id} article={article} />
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
    </NewsErrorBoundary>
  );
}
