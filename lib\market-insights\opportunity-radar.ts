// Opportunity & Risk Radar Engine with AI-Powered Analysis

import { OpportunityRadar, MarketData, TechnicalIndicators } from './types';
import { TechnicalAnalysisEngine } from './technical-analysis';

export class OpportunityRadarEngine {
  
  // Opportunity Detection Criteria
  static readonly OPPORTUNITY_CRITERIA = {
    OVERSOLD_RSI: { threshold: 30, weight: 0.25 },
    VOLUME_SPIKE: { threshold: 150, weight: 0.20 }, // 150% of average volume
    POSITIVE_MACD: { weight: 0.15 },
    SUPPORT_BOUNCE: { weight: 0.20 },
    NEWS_CATALYST: { weight: 0.20 }
  };
  
  // Risk Detection Criteria
  static readonly RISK_CRITERIA = {
    OVERBOUGHT_RSI: { threshold: 70, weight: 0.25 },
    HIGH_VOLATILITY: { threshold: 40, weight: 0.20 },
    NEGATIVE_DIVERGENCE: { weight: 0.20 },
    RESISTANCE_REJECTION: { weight: 0.15 },
    FUNDAMENTAL_RISK: { weight: 0.20 }
  };
  
  // Identify High-Opportunity Assets
  static identifyOpportunities(
    marketData: MarketData[],
    technicalData: Map<string, TechnicalIndicators>,
    newsData: Map<string, any>
  ): OpportunityRadar[] {
    const opportunities: OpportunityRadar[] = [];
    
    for (const asset of marketData) {
      const technical = technicalData.get(asset.symbol);
      const news = newsData.get(asset.symbol);
      
      if (!technical) continue;
      
      const opportunityScore = this.calculateOpportunityScore(asset, technical, news);
      
      if (opportunityScore.score >= 0.6) { // 60% threshold
        const radar: OpportunityRadar = {
          type: 'opportunity',
          asset: {
            symbol: asset.symbol,
            name: asset.name,
            sector: asset.sector || 'Unknown',
            price: asset.price,
            change: asset.changePercent
          },
          reason: opportunityScore.primaryReason,
          confidence: opportunityScore.score * 100,
          aiExplanation: this.generateOpportunityExplanation(asset, technical, opportunityScore),
          technicalReason: opportunityScore.technicalReason,
          fundamentalReason: opportunityScore.fundamentalReason,
          newsReason: opportunityScore.newsReason,
          timeHorizon: this.determineTimeHorizon(opportunityScore)
        };
        
        opportunities.push(radar);
      }
    }
    
    // Sort by confidence and return top 3
    return opportunities
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 3);
  }
  
  // Identify High-Risk Assets
  static identifyRisks(
    marketData: MarketData[],
    technicalData: Map<string, TechnicalIndicators>,
    newsData: Map<string, any>
  ): OpportunityRadar[] {
    const risks: OpportunityRadar[] = [];
    
    for (const asset of marketData) {
      const technical = technicalData.get(asset.symbol);
      const news = newsData.get(asset.symbol);
      
      if (!technical) continue;
      
      const riskScore = this.calculateRiskScore(asset, technical, news);
      
      if (riskScore.score >= 0.6) { // 60% threshold
        const radar: OpportunityRadar = {
          type: 'risk',
          asset: {
            symbol: asset.symbol,
            name: asset.name,
            sector: asset.sector || 'Unknown',
            price: asset.price,
            change: asset.changePercent
          },
          reason: riskScore.primaryReason,
          confidence: riskScore.score * 100,
          aiExplanation: this.generateRiskExplanation(asset, technical, riskScore),
          technicalReason: riskScore.technicalReason,
          fundamentalReason: riskScore.fundamentalReason,
          newsReason: riskScore.newsReason,
          timeHorizon: this.determineTimeHorizon(riskScore)
        };
        
        risks.push(radar);
      }
    }
    
    // Sort by confidence and return top 3
    return risks
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 3);
  }
  
  // Calculate Opportunity Score
  static calculateOpportunityScore(
    asset: MarketData,
    technical: TechnicalIndicators,
    news: any
  ): {
    score: number;
    primaryReason: string;
    technicalReason: string;
    fundamentalReason?: string;
    newsReason?: string;
  } {
    let score = 0;
    const reasons: string[] = [];
    let technicalReason = "";
    let fundamentalReason = "";
    let newsReason = "";
    
    // RSI Oversold Check
    if (technical.rsi < this.OPPORTUNITY_CRITERIA.OVERSOLD_RSI.threshold) {
      score += this.OPPORTUNITY_CRITERIA.OVERSOLD_RSI.weight;
      reasons.push("Oversold RSI");
      technicalReason += `RSI at ${technical.rsi.toFixed(1)} indicates oversold conditions. `;
    }
    
    // Volume Spike Check
    if (asset.volumeChange && asset.volumeChange > this.OPPORTUNITY_CRITERIA.VOLUME_SPIKE.threshold) {
      score += this.OPPORTUNITY_CRITERIA.VOLUME_SPIKE.weight;
      reasons.push("Volume spike");
      technicalReason += `Volume spike of ${asset.volumeChange.toFixed(0)}% suggests institutional interest. `;
    }
    
    // MACD Positive Check
    if (technical.macd.macd > technical.macd.signal && technical.macd.histogram > 0) {
      score += this.OPPORTUNITY_CRITERIA.POSITIVE_MACD.weight;
      reasons.push("Bullish MACD");
      technicalReason += "MACD showing bullish crossover with positive momentum. ";
    }
    
    // Support Bounce Check
    if (asset.changePercent > 1 && technical.rsi < 50) {
      score += this.OPPORTUNITY_CRITERIA.SUPPORT_BOUNCE.weight;
      reasons.push("Support bounce");
      technicalReason += "Price bouncing from support levels with oversold RSI. ";
    }
    
    // News Catalyst Check
    if (news && news.sentiment === 'positive') {
      score += this.OPPORTUNITY_CRITERIA.NEWS_CATALYST.weight;
      reasons.push("Positive news");
      newsReason = `Positive news catalyst: ${news.summary || 'Recent positive developments'}`;
    }
    
    // Fundamental Checks (simplified)
    if (asset.sector === 'IT' && asset.changePercent < -2) {
      fundamentalReason = "Quality IT stock at attractive valuation after recent correction";
    } else if (asset.sector === 'Banking' && technical.rsi < 40) {
      fundamentalReason = "Banking stock oversold despite strong fundamentals";
    }
    
    return {
      score,
      primaryReason: reasons.length > 0 ? reasons.join(", ") : "Technical setup",
      technicalReason: technicalReason.trim(),
      fundamentalReason: fundamentalReason || undefined,
      newsReason: newsReason || undefined
    };
  }
  
  // Calculate Risk Score
  static calculateRiskScore(
    asset: MarketData,
    technical: TechnicalIndicators,
    news: any
  ): {
    score: number;
    primaryReason: string;
    technicalReason: string;
    fundamentalReason?: string;
    newsReason?: string;
  } {
    let score = 0;
    const reasons: string[] = [];
    let technicalReason = "";
    let fundamentalReason = "";
    let newsReason = "";
    
    // RSI Overbought Check
    if (technical.rsi > this.RISK_CRITERIA.OVERBOUGHT_RSI.threshold) {
      score += this.RISK_CRITERIA.OVERBOUGHT_RSI.weight;
      reasons.push("Overbought RSI");
      technicalReason += `RSI at ${technical.rsi.toFixed(1)} indicates overbought conditions. `;
    }
    
    // High Volatility Check
    if (technical.volatility > this.RISK_CRITERIA.HIGH_VOLATILITY.threshold) {
      score += this.RISK_CRITERIA.HIGH_VOLATILITY.weight;
      reasons.push("High volatility");
      technicalReason += `High volatility at ${technical.volatility.toFixed(1)}% suggests increased risk. `;
    }
    
    // Negative MACD Divergence
    if (technical.macd.macd < technical.macd.signal && technical.macd.histogram < 0) {
      score += this.RISK_CRITERIA.NEGATIVE_DIVERGENCE.weight;
      reasons.push("Bearish MACD");
      technicalReason += "MACD showing bearish divergence with negative momentum. ";
    }
    
    // Resistance Rejection
    if (asset.changePercent < -2 && technical.rsi > 60) {
      score += this.RISK_CRITERIA.RESISTANCE_REJECTION.weight;
      reasons.push("Resistance rejection");
      technicalReason += "Price rejected at resistance levels despite overbought conditions. ";
    }
    
    // News Risk Check
    if (news && news.sentiment === 'negative') {
      score += 0.20; // News weight
      reasons.push("Negative news");
      newsReason = `Negative news impact: ${news.summary || 'Recent negative developments'}`;
    }
    
    // Fundamental Risk Checks
    if (asset.changePercent > 10 && technical.rsi > 80) {
      fundamentalReason = "Parabolic move with extreme overbought conditions - high reversal risk";
    } else if (asset.sector === 'Banking' && asset.changePercent > 5) {
      fundamentalReason = "Banking sector rally may face regulatory headwinds";
    }
    
    return {
      score,
      primaryReason: reasons.length > 0 ? reasons.join(", ") : "Technical risk",
      technicalReason: technicalReason.trim(),
      fundamentalReason: fundamentalReason || undefined,
      newsReason: newsReason || undefined
    };
  }
  
  // Generate AI Opportunity Explanation
  static generateOpportunityExplanation(
    asset: MarketData,
    technical: TechnicalIndicators,
    score: any
  ): string {
    const explanations = [
      `${asset.name} (${asset.symbol}) presents a compelling opportunity with ${score.score > 0.8 ? 'high' : 'moderate'} confidence.`,
      
      // Technical explanation
      score.technicalReason,
      
      // Price action context
      asset.changePercent > 0 
        ? `Current upward momentum of ${asset.changePercent.toFixed(2)}% suggests early reversal signs.`
        : `Despite recent decline of ${Math.abs(asset.changePercent).toFixed(2)}%, technical indicators suggest oversold bounce potential.`,
      
      // Risk-reward context
      `Risk-reward appears favorable with technical support levels providing downside protection.`,
      
      // Sector context
      asset.sector ? `${asset.sector} sector dynamics support the opportunity thesis.` : "",
      
      // Time horizon
      score.score > 0.8 ? "Consider for short to medium-term positions." : "Monitor for confirmation before entry."
    ];
    
    return explanations.filter(Boolean).join(" ");
  }
  
  // Generate AI Risk Explanation
  static generateRiskExplanation(
    asset: MarketData,
    technical: TechnicalIndicators,
    score: any
  ): string {
    const explanations = [
      `${asset.name} (${asset.symbol}) shows elevated risk factors requiring caution.`,
      
      // Technical explanation
      score.technicalReason,
      
      // Price action context
      asset.changePercent > 0 
        ? `Recent gains of ${asset.changePercent.toFixed(2)}% may have pushed valuation to unsustainable levels.`
        : `Continued decline of ${Math.abs(asset.changePercent).toFixed(2)}% suggests underlying weakness.`,
      
      // Risk context
      `Multiple risk indicators align, suggesting potential for further downside or consolidation.`,
      
      // Sector context
      asset.sector ? `${asset.sector} sector headwinds add to individual stock risks.` : "",
      
      // Recommendation
      score.score > 0.8 ? "Consider reducing exposure or avoiding new positions." : "Monitor closely for risk mitigation."
    ];
    
    return explanations.filter(Boolean).join(" ");
  }
  
  // Determine Time Horizon
  static determineTimeHorizon(score: any): 'short' | 'medium' | 'long' {
    if (score.score > 0.8) return 'short'; // High confidence - short term
    if (score.score > 0.6) return 'medium'; // Moderate confidence - medium term
    return 'long'; // Lower confidence - long term view
  }
  
  // Get Combined Radar
  static getCombinedRadar(
    marketData: MarketData[],
    technicalData: Map<string, TechnicalIndicators>,
    newsData: Map<string, any>
  ): OpportunityRadar[] {
    const opportunities = this.identifyOpportunities(marketData, technicalData, newsData);
    const risks = this.identifyRisks(marketData, technicalData, newsData);
    
    return [...opportunities, ...risks];
  }
}
