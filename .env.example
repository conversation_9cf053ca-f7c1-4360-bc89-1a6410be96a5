# Environment Variables Template
# Copy this file to .env.local and fill in your actual values

# Angel One SmartAPI Credentials
# Get these from: https://smartapi.angelbroking.com/
ANGEL_ONE_API_KEY=i
ANGEL_ONE_API_SECRET=ii
ANGEL_ONE_CLIENT_CODE=i
ANGEL_ONE_PASSWORD=i

# Public environment variables (for frontend)
NEXT_PUBLIC_ANGEL_ONE_API_KEY=ii
NEXT_PUBLIC_ANGEL_ONE_CLIENT_CODE=ii
NEXT_PUBLIC_ANGEL_ONE_PASSWORD=ii
NEXT_PUBLIC_ENABLE_REAL_API=true

# Zerodha Kite Connect Credentials  
# Get these from: https://developers.kite.trade/
ZERODHA_API_KEY=your_zerodha_api_key_here
ZERODHA_API_SECRET=your_zerodha_api_secret_here

# Upstox API Credentials
# Get these from: https://upstox.com/developer/
UPSTOX_API_KEY=your_upstox_api_key_here
UPSTOX_API_SECRET=your_upstox_api_secret_here

# Application Settings
NEXT_PUBLIC_BASE_URL=http://localhost:3000
NEXT_PUBLIC_ENABLE_REAL_API=false

# API Keys for Market Data
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# Database (for production)
DATABASE_URL=postgresql://username:password@localhost:5432/fintech_db

# Security
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000
