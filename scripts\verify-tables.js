const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function verifyTables() {
  try {
    console.log('🔍 Verifying database tables...')
    
    // Check if all required tables exist
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `
    
    console.log('📊 Found tables:')
    tables.forEach(table => {
      console.log(`  ✅ ${table.table_name}`)
    })
    
    // Check users table
    try {
      const userCount = await prisma.user.count()
      console.log(`\n👥 Users table: ${userCount} users registered`)
    } catch (error) {
      console.log('❌ Users table issue:', error.message)
    }
    
    // Check user activities
    try {
      const activityCount = await prisma.userActivity.count()
      console.log(`📝 User activities: ${activityCount} activities logged`)
    } catch (error) {
      console.log('❌ User activities table issue:', error.message)
    }
    
    console.log('\n✅ Database verification completed!')
    
  } catch (error) {
    console.error('❌ Database verification failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

verifyTables()
