"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Brain, MessageCircle, Mic, PieChart, Target, Lightbulb, BarChart3, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';

export default function Home() {
  const [currentInsight, setCurrentInsight] = useState(0);
  const [isLoaded, setIsLoaded] = useState(false);
  const { data: session, status } = useSession();

  const dailyInsights = [
    "Market volatility creates opportunities for disciplined investors",
    "Diversification is your shield against uncertainty",
    "Time in the market beats timing the market",
    "Emergency funds provide peace of mind and financial flexibility"
  ];

  useEffect(() => {
    setIsLoaded(true);
    const interval = setInterval(() => {
      setCurrentInsight((prev) => (prev + 1) % dailyInsights.length);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20" />
        <div className="relative container mx-auto px-4 py-16 sm:py-24">
          {/* Welcome Message for Logged-in Users */}
          {session?.user && (
            <div className="mb-8 text-center">
              <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-4 max-w-md mx-auto">
                <div className="flex items-center justify-center gap-2 text-green-400">
                  <CheckCircle className="h-5 w-5" />
                  <span className="font-medium">
                    Welcome back, {session.user.firstName || session.user.name}!
                  </span>
                </div>
                <p className="text-sm text-green-300 mt-1">
                  Successfully logged in to your FinTech Pro dashboard
                </p>
              </div>
            </div>
          )}

          <div className="text-center space-y-8">
            <div className="space-y-4">
              <Badge variant="outline" className="bg-blue-500/10 text-blue-300 border-blue-500/30">
                AI-Powered Financial Intelligence
              </Badge>
              <h1 className="text-4xl sm:text-6xl font-bold text-white">
                Your Personal
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">
                  {" "}Finance Strategist
                </span>
              </h1>
              <p className="text-xl text-slate-300 max-w-3xl mx-auto">
                Unlock the inner world of finance with our AI assistant. Get personalized guidance, 
                real-time analysis, and strategic insights that adapt to your unique financial journey.
              </p>
            </div>

            {/* Daily Insight Carousel */}
            <div className="bg-slate-800/50 backdrop-blur-sm rounded-lg p-6 max-w-2xl mx-auto border border-slate-700">
              <div className="flex items-center gap-2 mb-3">
                <Lightbulb className="h-5 w-5 text-yellow-400" />
                <span className="text-sm font-medium text-yellow-400">Daily Insight</span>
              </div>
              <p className={`text-lg text-slate-200 transition-opacity duration-500 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
                "{dailyInsights[currentInsight]}"
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/chat">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                  <MessageCircle className="mr-2 h-5 w-5" />
                  Start AI Chat
                </Button>
              </Link>
              <Link href="/dashboard">
                <Button size="lg" variant="outline" className="border-slate-600 text-slate-200 hover:bg-slate-800 px-8 py-3">
                  <BarChart3 className="mr-2 h-5 w-5" />
                  View Dashboard
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Features Grid */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all duration-300">
            <CardHeader>
              <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mb-4">
                <MessageCircle className="h-6 w-6 text-blue-400" />
              </div>
              <CardTitle className="text-white">AI Chat Assistant</CardTitle>
              <CardDescription className="text-slate-400">
                Conversational AI powered by Gemini that guides you through complex financial concepts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/chat">
                <Button variant="ghost" className="text-blue-400 hover:text-blue-300 p-0">
                  Start Conversation →
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all duration-300">
            <CardHeader>
              <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mb-4">
                <Mic className="h-6 w-6 text-purple-400" />
              </div>
              <CardTitle className="text-white">Voice Intelligence</CardTitle>
              <CardDescription className="text-slate-400">
                Speak naturally and get instant voice responses for hands-free financial planning
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/voice">
                <Button variant="ghost" className="text-purple-400 hover:text-purple-300 p-0">
                  Try Voice Mode →
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all duration-300">
            <CardHeader>
              <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mb-4">
                <PieChart className="h-6 w-6 text-green-400" />
              </div>
              <CardTitle className="text-white">Portfolio Simulation</CardTitle>
              <CardDescription className="text-slate-400">
                Test investment strategies with advanced simulation and risk analysis tools
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/portfolio">
                <Button variant="ghost" className="text-green-400 hover:text-green-300 p-0">
                  Simulate Portfolio →
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all duration-300">
            <CardHeader>
              <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center mb-4">
                <Brain className="h-6 w-6 text-yellow-400" />
              </div>
              <CardTitle className="text-white">Adaptive Learning</CardTitle>
              <CardDescription className="text-slate-400">
                Personalized recommendations that evolve with your financial knowledge and goals
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/learning">
                <Button variant="ghost" className="text-yellow-400 hover:text-yellow-300 p-0">
                  View Progress →
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all duration-300">
            <CardHeader>
              <div className="w-12 h-12 bg-red-500/20 rounded-lg flex items-center justify-center mb-4">
                <Target className="h-6 w-6 text-red-400" />
              </div>
              <CardTitle className="text-white">Risk Analysis</CardTitle>
              <CardDescription className="text-slate-400">
                Comprehensive risk assessment with real-time market data and scenario modeling
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/risk-analysis">
                <Button variant="ghost" className="text-red-400 hover:text-red-300 p-0">
                  Analyze Risk →
                </Button>
              </Link>
            </CardContent>
          </Card>

          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all duration-300">
            <CardHeader>
              <div className="w-12 h-12 bg-indigo-500/20 rounded-lg flex items-center justify-center mb-4">
                <TrendingUp className="h-6 w-6 text-indigo-400" />
              </div>
              <CardTitle className="text-white">Market Insights</CardTitle>
              <CardDescription className="text-slate-400">
                Daily market updates, trends analysis, and strategic investment opportunities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/insights">
                <Button variant="ghost" className="text-indigo-400 hover:text-indigo-300 p-0">
                  View Insights →
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* CTA Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-2xl p-8 border border-slate-700 backdrop-blur-sm">
          <div className="text-center space-y-6">
            <h2 className="text-3xl font-bold text-white">
              Ready to Transform Your Financial Future?
            </h2>
            <p className="text-lg text-slate-300 max-w-2xl mx-auto">
              Join thousands of users who have already discovered the power of AI-driven financial planning. 
              Your personalized financial strategist is waiting.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/chat">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3">
                  Get Started Free
                </Button>
              </Link>
              <Link href="/demo">
                <Button size="lg" variant="outline" className="border-slate-600 text-slate-200 hover:bg-slate-800 px-8 py-3">
                  Watch Demo
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}