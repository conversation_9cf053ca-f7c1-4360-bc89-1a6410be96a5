// News Deduplication Service

import { NewsArticle, NewsCategory } from './types';

export class NewsDeduplicationService {
  private seenArticles = new Set<string>();
  private articleFingerprints = new Map<string, string>();

  /**
   * Remove duplicate articles from a list
   */
  deduplicateArticles(articles: NewsArticle[]): NewsArticle[] {
    const uniqueArticles: NewsArticle[] = [];
    const seenFingerprints = new Set<string>();

    for (const article of articles) {
      const fingerprint = this.generateArticleFingerprint(article);
      
      if (!seenFingerprints.has(fingerprint)) {
        seenFingerprints.add(fingerprint);
        uniqueArticles.push(article);
      }
    }

    return uniqueArticles;
  }

  /**
   * Filter articles to ensure category relevance and uniqueness
   * Guarantees minimum 9 articles per category
   */
  filterAndDeduplicateForCategory(
    articles: NewsArticle[],
    category: NewsCategory,
    maxArticles: number = 20,
    minArticles: number = 9
  ): NewsArticle[] {
    // First, filter for category relevance with strict criteria
    let relevantArticles = this.filterByCategoryRelevance(articles, category);

    // If we don't have enough articles, try with relaxed criteria
    if (relevantArticles.length < minArticles) {
      relevantArticles = this.filterByCategoryRelevanceRelaxed(articles, category);
    }

    // Then deduplicate
    const uniqueArticles = this.deduplicateArticles(relevantArticles);

    // Sort by relevance and recency
    const sortedArticles = this.sortByRelevanceAndRecency(uniqueArticles, category);

    // Ensure we have at least minArticles
    const finalArticles = sortedArticles.slice(0, maxArticles);

    // If still not enough, pad with best available articles
    if (finalArticles.length < minArticles) {
      const additionalArticles = this.getAdditionalArticlesForCategory(
        articles,
        category,
        finalArticles,
        minArticles - finalArticles.length
      );
      finalArticles.push(...additionalArticles);
    }

    return finalArticles.slice(0, maxArticles);
  }

  /**
   * Generate a fingerprint for article similarity detection
   */
  private generateArticleFingerprint(article: NewsArticle): string {
    // Normalize title for comparison
    const normalizedTitle = article.title
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ')
      .trim();

    // Use first 50 characters of normalized title + source
    const titlePart = normalizedTitle.substring(0, 50);
    const sourcePart = article.source.name.toLowerCase();
    
    return `${titlePart}_${sourcePart}`;
  }

  /**
   * Filter articles by category relevance with strict criteria
   */
  private filterByCategoryRelevance(articles: NewsArticle[], category: NewsCategory): NewsArticle[] {
    return articles.filter(article => {
      const content = `${article.title} ${article.description}`.toLowerCase();

      switch (category) {
        case 'india-specific':
          return this.isIndianRelevant(content);
        case 'global-markets':
          return this.isGlobalRelevant(content) && !this.isIndianRelevant(content);
        case 'crypto-web3':
          return this.isCryptoRelevant(content);
        case 'stocks-companies':
          return this.isStockRelevant(content);
        case 'macro-policy':
          return this.isMacroRelevant(content);
        default:
          return true;
      }
    });
  }

  /**
   * Filter articles by category relevance with relaxed criteria
   */
  private filterByCategoryRelevanceRelaxed(articles: NewsArticle[], category: NewsCategory): NewsArticle[] {
    return articles.filter(article => {
      const content = `${article.title} ${article.description}`.toLowerCase();

      switch (category) {
        case 'india-specific':
          return this.isIndianRelevantRelaxed(content);
        case 'global-markets':
          return this.isGlobalRelevantRelaxed(content);
        case 'crypto-web3':
          return this.isCryptoRelevantRelaxed(content);
        case 'stocks-companies':
          return this.isStockRelevantRelaxed(content);
        case 'macro-policy':
          return this.isMacroRelevantRelaxed(content);
        default:
          return true;
      }
    });
  }

  /**
   * Get additional articles to meet minimum count requirement
   */
  private getAdditionalArticlesForCategory(
    articles: NewsArticle[],
    category: NewsCategory,
    existingArticles: NewsArticle[],
    needed: number
  ): NewsArticle[] {
    const existingIds = new Set(existingArticles.map(a => a.id));
    const additionalArticles: NewsArticle[] = [];

    // Try to find articles that match the category but weren't included
    const remainingArticles = articles.filter(article => !existingIds.has(article.id));

    for (const article of remainingArticles) {
      if (additionalArticles.length >= needed) break;

      const content = `${article.title} ${article.description}`.toLowerCase();
      let isRelevant = false;

      switch (category) {
        case 'india-specific':
          isRelevant = this.hasAnyIndianKeywords(content);
          break;
        case 'global-markets':
          isRelevant = this.hasAnyGlobalKeywords(content);
          break;
        case 'crypto-web3':
          isRelevant = this.hasAnyCryptoKeywords(content);
          break;
        case 'stocks-companies':
          isRelevant = this.hasAnyStockKeywords(content);
          break;
        case 'macro-policy':
          isRelevant = this.hasAnyMacroKeywords(content);
          break;
        default:
          isRelevant = true;
      }

      if (isRelevant) {
        additionalArticles.push(article);
      }
    }

    return additionalArticles;
  }

  /**
   * Check if article is relevant to Indian markets
   */
  private isIndianRelevant(content: string): boolean {
    const indianKeywords = [
      'rbi', 'reserve bank of india', 'sensex', 'nifty', 'bse', 'nse',
      'indian', 'india', 'rupee', 'inr', 'mumbai', 'delhi',
      'tcs', 'infosys', 'hdfc', 'icici', 'sbi', 'reliance', 'adani',
      'sebi', 'gst', 'fii', 'dii', 'indian banking', 'indian markets',
      'indian economy', 'indian stocks', 'indian companies'
    ];

    const requiredMatches = 2; // Need at least 2 Indian keywords
    const matches = indianKeywords.filter(keyword => content.includes(keyword)).length;

    return matches >= requiredMatches;
  }

  /**
   * Relaxed Indian relevance check (only 1 keyword required)
   */
  private isIndianRelevantRelaxed(content: string): boolean {
    const indianKeywords = [
      'rbi', 'reserve bank of india', 'sensex', 'nifty', 'bse', 'nse',
      'indian', 'india', 'rupee', 'inr', 'mumbai', 'delhi', 'bangalore',
      'tcs', 'infosys', 'hdfc', 'icici', 'sbi', 'reliance', 'adani',
      'sebi', 'gst', 'fii', 'dii', 'indian banking', 'indian markets',
      'indian economy', 'indian stocks', 'indian companies', 'bajaj',
      'tata', 'mahindra', 'wipro', 'asian paints', 'itc'
    ];

    return indianKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * Check for any Indian keywords (most relaxed)
   */
  private hasAnyIndianKeywords(content: string): boolean {
    const keywords = ['india', 'indian', 'mumbai', 'delhi', 'rupee', 'sensex', 'nifty'];
    return keywords.some(keyword => content.includes(keyword));
  }

  /**
   * Check if article is relevant to global markets (but not Indian)
   */
  private isGlobalRelevant(content: string): boolean {
    const globalKeywords = [
      'fed', 'federal reserve', 'dow jones', 's&p 500', 'nasdaq', 'wall street',
      'dollar', 'usd', 'euro', 'pound', 'yen', 'ecb', 'fomc',
      'apple', 'microsoft', 'amazon', 'google', 'tesla',
      'global markets', 'international', 'worldwide', 'us markets',
      'european markets', 'asian markets', 'china', 'japan', 'europe'
    ];

    const matches = globalKeywords.filter(keyword => content.includes(keyword)).length;
    return matches >= 1;
  }

  /**
   * Relaxed global relevance check
   */
  private isGlobalRelevantRelaxed(content: string): boolean {
    const globalKeywords = [
      'fed', 'federal reserve', 'dow jones', 's&p 500', 'nasdaq', 'wall street',
      'dollar', 'usd', 'euro', 'pound', 'yen', 'ecb', 'fomc',
      'apple', 'microsoft', 'amazon', 'google', 'tesla', 'meta',
      'global markets', 'international', 'worldwide', 'us markets',
      'european markets', 'asian markets', 'china', 'japan', 'europe',
      'ftse', 'dax', 'cac', 'nikkei', 'hang seng'
    ];

    return globalKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * Check for any global keywords
   */
  private hasAnyGlobalKeywords(content: string): boolean {
    const keywords = ['global', 'international', 'us', 'usa', 'europe', 'china', 'dollar'];
    return keywords.some(keyword => content.includes(keyword));
  }

  /**
   * Check if article is relevant to crypto/web3
   */
  private isCryptoRelevant(content: string): boolean {
    const cryptoKeywords = [
      'bitcoin', 'ethereum', 'crypto', 'cryptocurrency', 'blockchain', 'defi',
      'web3', 'nft', 'btc', 'eth', 'binance', 'coinbase', 'altcoin',
      'digital currency', 'smart contract', 'mining', 'staking'
    ];

    const matches = cryptoKeywords.filter(keyword => content.includes(keyword)).length;
    return matches >= 1;
  }

  /**
   * Relaxed crypto relevance check
   */
  private isCryptoRelevantRelaxed(content: string): boolean {
    const cryptoKeywords = [
      'bitcoin', 'ethereum', 'crypto', 'cryptocurrency', 'blockchain', 'defi',
      'web3', 'nft', 'btc', 'eth', 'binance', 'coinbase', 'altcoin',
      'digital currency', 'smart contract', 'mining', 'staking',
      'solana', 'cardano', 'polygon', 'chainlink', 'token', 'coin'
    ];

    return cryptoKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * Check for any crypto keywords
   */
  private hasAnyCryptoKeywords(content: string): boolean {
    const keywords = ['crypto', 'bitcoin', 'blockchain', 'digital', 'token'];
    return keywords.some(keyword => content.includes(keyword));
  }

  /**
   * Check if article is relevant to stocks/companies
   */
  private isStockRelevant(content: string): boolean {
    const stockKeywords = [
      'earnings', 'quarterly', 'revenue', 'profit', 'stock', 'share', 'ipo',
      'dividend', 'market cap', 'pe ratio', 'eps', 'quarterly results',
      'financial results', 'annual report', 'guidance', 'outlook'
    ];

    const matches = stockKeywords.filter(keyword => content.includes(keyword)).length;
    return matches >= 1;
  }

  /**
   * Relaxed stock relevance check
   */
  private isStockRelevantRelaxed(content: string): boolean {
    const stockKeywords = [
      'earnings', 'quarterly', 'revenue', 'profit', 'stock', 'share', 'ipo',
      'dividend', 'market cap', 'pe ratio', 'eps', 'quarterly results',
      'financial results', 'annual report', 'guidance', 'outlook',
      'company', 'corporate', 'business', 'sales', 'growth'
    ];

    return stockKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * Check for any stock keywords
   */
  private hasAnyStockKeywords(content: string): boolean {
    const keywords = ['stock', 'company', 'earnings', 'profit', 'business'];
    return keywords.some(keyword => content.includes(keyword));
  }

  /**
   * Check if article is relevant to macro/policy
   */
  private isMacroRelevant(content: string): boolean {
    const macroKeywords = [
      'inflation', 'gdp', 'monetary policy', 'fiscal policy', 'interest rate',
      'economic growth', 'recession', 'recovery', 'unemployment', 'trade',
      'government policy', 'central bank', 'economic data', 'economic indicators'
    ];

    const matches = macroKeywords.filter(keyword => content.includes(keyword)).length;
    return matches >= 1;
  }

  /**
   * Relaxed macro relevance check
   */
  private isMacroRelevantRelaxed(content: string): boolean {
    const macroKeywords = [
      'inflation', 'gdp', 'monetary policy', 'fiscal policy', 'interest rate',
      'economic growth', 'recession', 'recovery', 'unemployment', 'trade',
      'government policy', 'central bank', 'economic data', 'economic indicators',
      'economy', 'economic', 'policy', 'government', 'budget'
    ];

    return macroKeywords.some(keyword => content.includes(keyword));
  }

  /**
   * Check for any macro keywords
   */
  private hasAnyMacroKeywords(content: string): boolean {
    const keywords = ['economic', 'policy', 'inflation', 'gdp', 'government'];
    return keywords.some(keyword => content.includes(keyword));
  }

  /**
   * Sort articles by relevance and recency
   */
  private sortByRelevanceAndRecency(articles: NewsArticle[], category: NewsCategory): NewsArticle[] {
    return articles.sort((a, b) => {
      // Calculate relevance score
      const aRelevance = this.calculateCategoryRelevance(a, category);
      const bRelevance = this.calculateCategoryRelevance(b, category);
      
      // Calculate recency score (newer is better)
      const now = Date.now();
      const aRecency = 1 - (now - new Date(a.publishedAt).getTime()) / (24 * 60 * 60 * 1000); // 24 hour decay
      const bRecency = 1 - (now - new Date(b.publishedAt).getTime()) / (24 * 60 * 60 * 1000);
      
      // Combined score (70% relevance, 30% recency)
      const aScore = aRelevance * 0.7 + Math.max(0, aRecency) * 0.3;
      const bScore = bRelevance * 0.7 + Math.max(0, bRecency) * 0.3;
      
      return bScore - aScore;
    });
  }

  /**
   * Calculate relevance score for a specific category
   */
  private calculateCategoryRelevance(article: NewsArticle, category: NewsCategory): number {
    const content = `${article.title} ${article.description}`.toLowerCase();
    let score = 0;

    switch (category) {
      case 'india-specific':
        score = this.calculateIndianRelevanceScore(content);
        break;
      case 'global-markets':
        score = this.calculateGlobalRelevanceScore(content);
        break;
      case 'crypto-web3':
        score = this.calculateCryptoRelevanceScore(content);
        break;
      case 'stocks-companies':
        score = this.calculateStockRelevanceScore(content);
        break;
      case 'macro-policy':
        score = this.calculateMacroRelevanceScore(content);
        break;
      default:
        score = 0.5;
    }

    return Math.min(score, 1.0);
  }

  /**
   * Calculate Indian market relevance score
   */
  private calculateIndianRelevanceScore(content: string): number {
    const highValueKeywords = ['rbi', 'sensex', 'nifty', 'indian markets', 'indian banking'];
    const mediumValueKeywords = ['india', 'indian', 'rupee', 'bse', 'nse', 'mumbai'];
    const companyKeywords = ['tcs', 'infosys', 'hdfc', 'icici', 'sbi', 'reliance'];

    let score = 0;
    
    // High value keywords (0.3 each)
    highValueKeywords.forEach(keyword => {
      if (content.includes(keyword)) score += 0.3;
    });
    
    // Medium value keywords (0.2 each)
    mediumValueKeywords.forEach(keyword => {
      if (content.includes(keyword)) score += 0.2;
    });
    
    // Company keywords (0.1 each)
    companyKeywords.forEach(keyword => {
      if (content.includes(keyword)) score += 0.1;
    });

    return Math.min(score, 1.0);
  }

  /**
   * Calculate global market relevance score
   */
  private calculateGlobalRelevanceScore(content: string): number {
    const highValueKeywords = ['fed', 'federal reserve', 'dow jones', 's&p 500', 'nasdaq'];
    const mediumValueKeywords = ['dollar', 'usd', 'wall street', 'global markets'];
    const companyKeywords = ['apple', 'microsoft', 'amazon', 'google', 'tesla'];

    let score = 0;
    
    highValueKeywords.forEach(keyword => {
      if (content.includes(keyword)) score += 0.3;
    });
    
    mediumValueKeywords.forEach(keyword => {
      if (content.includes(keyword)) score += 0.2;
    });
    
    companyKeywords.forEach(keyword => {
      if (content.includes(keyword)) score += 0.1;
    });

    // Penalty for Indian content in global category
    if (this.isIndianRelevant(content)) {
      score *= 0.3;
    }

    return Math.min(score, 1.0);
  }

  /**
   * Calculate crypto relevance score
   */
  private calculateCryptoRelevanceScore(content: string): number {
    const highValueKeywords = ['bitcoin', 'ethereum', 'cryptocurrency', 'blockchain'];
    const mediumValueKeywords = ['crypto', 'defi', 'web3', 'nft', 'btc', 'eth'];

    let score = 0;
    
    highValueKeywords.forEach(keyword => {
      if (content.includes(keyword)) score += 0.4;
    });
    
    mediumValueKeywords.forEach(keyword => {
      if (content.includes(keyword)) score += 0.2;
    });

    return Math.min(score, 1.0);
  }

  /**
   * Calculate stock relevance score
   */
  private calculateStockRelevanceScore(content: string): number {
    const keywords = ['earnings', 'quarterly', 'revenue', 'profit', 'stock', 'ipo'];
    let score = 0;
    
    keywords.forEach(keyword => {
      if (content.includes(keyword)) score += 0.2;
    });

    return Math.min(score, 1.0);
  }

  /**
   * Calculate macro relevance score
   */
  private calculateMacroRelevanceScore(content: string): number {
    const keywords = ['inflation', 'gdp', 'monetary policy', 'interest rate', 'economic'];
    let score = 0;
    
    keywords.forEach(keyword => {
      if (content.includes(keyword)) score += 0.2;
    });

    return Math.min(score, 1.0);
  }

  /**
   * Reset seen articles (call this when fetching fresh news)
   */
  reset(): void {
    this.seenArticles.clear();
    this.articleFingerprints.clear();
  }
}

export const newsDeduplicationService = new NewsDeduplicationService();
