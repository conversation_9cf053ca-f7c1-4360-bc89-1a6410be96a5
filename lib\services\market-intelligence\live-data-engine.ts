// Live Market Data Engine - Real-world Integration

import { 
  LiveMarketData, 
  MarketIndex, 
  CurrencyData, 
  CommodityData, 
  BondData, 
  MarketStatus,
  TechnicalIndicators,
  TechnicalData,
  EconomicIndicators,
  EconomicData
} from './types';

export class LiveMarketDataEngine {
  private cache = new Map<string, any>();
  private cacheTimeout = 5 * 60 * 1000; // 5 minutes for live data
  private lastUpdate = new Map<string, number>();

  /**
   * Get comprehensive live market data
   */
  async getLiveMarketData(): Promise<LiveMarketData> {
    try {
      const [indices, currencies, commodities, bonds, marketStatus] = await Promise.all([
        this.getMarketIndices(),
        this.getCurrencyData(),
        this.getCommodityData(),
        this.getBondData(),
        this.getMarketStatus()
      ]);

      return {
        indices,
        currencies,
        commodities,
        bonds,
        marketStatus,
        lastUpdated: new Date()
      };
    } catch (error) {
      console.error('Error fetching live market data:', error);
      return this.getFallbackMarketData();
    }
  }

  /**
   * Get real-time market indices with technical analysis
   */
  private async getMarketIndices(): Promise<MarketIndex[]> {
    const cacheKey = 'market_indices';
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      // In production, integrate with real APIs like Alpha Vantage, Yahoo Finance, or Bloomberg
      const indices = await this.fetchIndicesFromAPI();
      this.setCachedData(cacheKey, indices);
      return indices;
    } catch (error) {
      console.warn('Using fallback market indices data');
      return this.getFallbackIndices();
    }
  }

  /**
   * Fetch indices from external API (placeholder for real implementation)
   */
  private async fetchIndicesFromAPI(): Promise<MarketIndex[]> {
    // TODO: Integrate with real market data APIs
    // Example APIs: Alpha Vantage, Yahoo Finance, IEX Cloud, Polygon.io
    
    // For now, return realistic simulated data
    return this.getRealtimeSimulatedIndices();
  }

  /**
   * Get realtime simulated indices (realistic market data)
   */
  private getRealtimeSimulatedIndices(): Promise<MarketIndex[]> {
    const now = new Date();
    const marketHour = now.getHours();
    const isMarketOpen = marketHour >= 9 && marketHour <= 15; // Indian market hours
    
    // Simulate realistic market movements
    const baseValues = {
      sensex: 65000,
      nifty: 19500,
      sp500: 4500,
      nasdaq: 14000,
      dow: 35000
    };

    const volatility = isMarketOpen ? 0.02 : 0.005; // Higher volatility during market hours
    
    return Promise.resolve([
      {
        symbol: 'SENSEX',
        name: 'BSE Sensex',
        value: this.simulatePrice(baseValues.sensex, volatility),
        change: this.simulateChange(baseValues.sensex, volatility),
        changePercent: this.simulatePercentChange(),
        volume: this.simulateVolume(100000000, 500000000),
        high52w: 67000,
        low52w: 58000,
        trend: this.determineTrend()
      },
      {
        symbol: 'NIFTY',
        name: 'NSE Nifty 50',
        value: this.simulatePrice(baseValues.nifty, volatility),
        change: this.simulateChange(baseValues.nifty, volatility),
        changePercent: this.simulatePercentChange(),
        volume: this.simulateVolume(50000000, 200000000),
        high52w: 20100,
        low52w: 17500,
        trend: this.determineTrend()
      },
      {
        symbol: 'SPX',
        name: 'S&P 500',
        value: this.simulatePrice(baseValues.sp500, volatility * 0.8),
        change: this.simulateChange(baseValues.sp500, volatility * 0.8),
        changePercent: this.simulatePercentChange(),
        volume: this.simulateVolume(3000000000, 6000000000),
        high52w: 4800,
        low52w: 3800,
        trend: this.determineTrend()
      },
      {
        symbol: 'IXIC',
        name: 'NASDAQ Composite',
        value: this.simulatePrice(baseValues.nasdaq, volatility * 1.2),
        change: this.simulateChange(baseValues.nasdaq, volatility * 1.2),
        changePercent: this.simulatePercentChange(),
        volume: this.simulateVolume(4000000000, 8000000000),
        high52w: 16000,
        low52w: 11000,
        trend: this.determineTrend()
      },
      {
        symbol: 'DJI',
        name: 'Dow Jones Industrial Average',
        value: this.simulatePrice(baseValues.dow, volatility * 0.7),
        change: this.simulateChange(baseValues.dow, volatility * 0.7),
        changePercent: this.simulatePercentChange(),
        volume: this.simulateVolume(200000000, 500000000),
        high52w: 37000,
        low52w: 32000,
        trend: this.determineTrend()
      }
    ]);
  }

  /**
   * Get real-time currency data
   */
  private async getCurrencyData(): Promise<CurrencyData[]> {
    const cacheKey = 'currency_data';
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      // In production, use real forex APIs like Fixer.io, CurrencyAPI, or OANDA
      const currencies = this.getRealtimeCurrencyData();
      this.setCachedData(cacheKey, currencies);
      return currencies;
    } catch (error) {
      console.warn('Using fallback currency data');
      return this.getFallbackCurrencies();
    }
  }

  /**
   * Get realtime currency data (simulated but realistic)
   */
  private getRealtimeCurrencyData(): CurrencyData[] {
    const baseRates = {
      'USD/INR': 83.25,
      'EUR/INR': 90.50,
      'GBP/INR': 105.75,
      'JPY/INR': 0.56,
      'EUR/USD': 1.087,
      'GBP/USD': 1.270
    };

    return Object.entries(baseRates).map(([pair, baseRate]) => ({
      pair,
      rate: this.simulatePrice(baseRate, 0.01),
      change: this.simulateChange(baseRate, 0.01),
      changePercent: this.simulatePercentChange(),
      trend: this.determineCurrencyTrend(),
      volatility: this.simulateVolatility()
    }));
  }

  /**
   * Get commodity data
   */
  private async getCommodityData(): Promise<CommodityData[]> {
    return [
      {
        symbol: 'GOLD',
        name: 'Gold',
        price: this.simulatePrice(2000, 0.02),
        change: this.simulateChange(2000, 0.02),
        changePercent: this.simulatePercentChange(),
        trend: this.determineTrend()
      },
      {
        symbol: 'CRUDE',
        name: 'Crude Oil',
        price: this.simulatePrice(75, 0.03),
        change: this.simulateChange(75, 0.03),
        changePercent: this.simulatePercentChange(),
        trend: this.determineTrend()
      },
      {
        symbol: 'SILVER',
        name: 'Silver',
        price: this.simulatePrice(24, 0.025),
        change: this.simulateChange(24, 0.025),
        changePercent: this.simulatePercentChange(),
        trend: this.determineTrend()
      }
    ];
  }

  /**
   * Get bond data
   */
  private async getBondData(): Promise<BondData[]> {
    return [
      {
        name: '10Y Indian Government Bond',
        yield: this.simulatePrice(7.25, 0.005),
        change: this.simulateChange(7.25, 0.005),
        duration: 10,
        rating: 'AAA'
      },
      {
        name: '10Y US Treasury',
        yield: this.simulatePrice(4.50, 0.008),
        change: this.simulateChange(4.50, 0.008),
        duration: 10,
        rating: 'AAA'
      }
    ];
  }

  /**
   * Get current market status
   */
  private async getMarketStatus(): Promise<MarketStatus> {
    const now = new Date();
    const istHour = now.getHours();
    const utcHour = now.getUTCHours();

    return {
      indian: this.getIndianMarketStatus(istHour),
      us: this.getUSMarketStatus(utcHour),
      european: this.getEuropeanMarketStatus(utcHour),
      crypto: 'open'
    };
  }

  /**
   * Helper methods for market simulation
   */
  private simulatePrice(basePrice: number, volatility: number): number {
    const randomFactor = (Math.random() - 0.5) * 2 * volatility;
    return Math.round((basePrice * (1 + randomFactor)) * 100) / 100;
  }

  private simulateChange(basePrice: number, volatility: number): number {
    const randomFactor = (Math.random() - 0.5) * 2 * volatility;
    return Math.round((basePrice * randomFactor) * 100) / 100;
  }

  private simulatePercentChange(): number {
    return Math.round(((Math.random() - 0.5) * 4) * 100) / 100; // -2% to +2%
  }

  private simulateVolume(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min) + min);
  }

  private simulateVolatility(): number {
    return Math.round((Math.random() * 0.5 + 0.1) * 100) / 100; // 0.1 to 0.6
  }

  private determineTrend(): 'bullish' | 'bearish' | 'neutral' {
    const rand = Math.random();
    if (rand < 0.4) return 'bullish';
    if (rand < 0.8) return 'neutral';
    return 'bearish';
  }

  private determineCurrencyTrend(): 'strengthening' | 'weakening' | 'stable' {
    const rand = Math.random();
    if (rand < 0.35) return 'strengthening';
    if (rand < 0.7) return 'stable';
    return 'weakening';
  }

  private getIndianMarketStatus(hour: number): 'open' | 'closed' | 'pre_market' | 'after_hours' {
    if (hour >= 9 && hour < 15.5) return 'open';
    if (hour >= 8 && hour < 9) return 'pre_market';
    if (hour >= 15.5 && hour < 18) return 'after_hours';
    return 'closed';
  }

  private getUSMarketStatus(utcHour: number): 'open' | 'closed' | 'pre_market' | 'after_hours' {
    // US market hours in UTC (approximate)
    if (utcHour >= 14.5 && utcHour < 21) return 'open';
    if (utcHour >= 13 && utcHour < 14.5) return 'pre_market';
    if (utcHour >= 21 && utcHour < 24) return 'after_hours';
    return 'closed';
  }

  private getEuropeanMarketStatus(utcHour: number): 'open' | 'closed' | 'pre_market' | 'after_hours' {
    if (utcHour >= 8 && utcHour < 16.5) return 'open';
    if (utcHour >= 7 && utcHour < 8) return 'pre_market';
    if (utcHour >= 16.5 && utcHour < 19) return 'after_hours';
    return 'closed';
  }

  /**
   * Cache management
   */
  private getCachedData(key: string): any {
    const lastUpdate = this.lastUpdate.get(key) || 0;
    if (Date.now() - lastUpdate < this.cacheTimeout) {
      return this.cache.get(key);
    }
    return null;
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, data);
    this.lastUpdate.set(key, Date.now());
  }

  /**
   * Fallback data methods
   */
  private getFallbackMarketData(): LiveMarketData {
    return {
      indices: this.getFallbackIndices(),
      currencies: this.getFallbackCurrencies(),
      commodities: [],
      bonds: [],
      marketStatus: {
        indian: 'closed',
        us: 'closed',
        european: 'closed',
        crypto: 'open'
      },
      lastUpdated: new Date()
    };
  }

  private getFallbackIndices(): MarketIndex[] {
    return [
      {
        symbol: 'SENSEX',
        name: 'BSE Sensex',
        value: 65000,
        change: 150,
        changePercent: 0.23,
        volume: 150000000,
        high52w: 67000,
        low52w: 58000,
        trend: 'neutral'
      }
    ];
  }

  private getFallbackCurrencies(): CurrencyData[] {
    return [
      {
        pair: 'USD/INR',
        rate: 83.25,
        change: 0.15,
        changePercent: 0.18,
        trend: 'stable',
        volatility: 0.25
      }
    ];
  }
}

export const liveMarketDataEngine = new LiveMarketDataEngine();
