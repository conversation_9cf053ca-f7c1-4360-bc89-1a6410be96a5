// Smart Keyword Matching with Semantic Similarity

import { SegmentKeyword, SegmentEntity } from './types';

export class SmartKeywordMatcher {
  private semanticCache = new Map<string, number>();
  private entityCache = new Map<string, any[]>();

  /**
   * Advanced keyword matching with context awareness and semantic similarity
   */
  async matchKeywords(
    text: string, 
    keywords: SegmentKeyword[], 
    options: {
      useSemanticSimilarity?: boolean;
      contextWindow?: number;
      fuzzyThreshold?: number;
    } = {}
  ): Promise<{
    matches: KeywordMatch[];
    totalScore: number;
    contextualMatches: number;
  }> {
    const {
      useSemanticSimilarity = true,
      contextWindow = 50,
      fuzzyThreshold = 0.7
    } = options;

    const matches: KeywordMatch[] = [];
    let totalScore = 0;
    let contextualMatches = 0;

    const normalizedText = this.normalizeText(text);
    const words = normalizedText.split(/\s+/);

    for (const keyword of keywords) {
      const keywordMatches = await this.findKeywordMatches(
        normalizedText,
        words,
        keyword,
        {
          useSemanticSimilarity,
          contextWindow,
          fuzzyThreshold
        }
      );

      if (keywordMatches.length > 0) {
        const bestMatch = keywordMatches.reduce((best, current) => 
          current.confidence > best.confidence ? current : best
        );

        matches.push(bestMatch);
        totalScore += bestMatch.score;

        if (bestMatch.hasContext) {
          contextualMatches++;
        }
      }
    }

    return {
      matches,
      totalScore: Math.min(totalScore, keywords.length),
      contextualMatches
    };
  }

  /**
   * Find all matches for a specific keyword
   */
  private async findKeywordMatches(
    text: string,
    words: string[],
    keyword: SegmentKeyword,
    options: any
  ): Promise<KeywordMatch[]> {
    const matches: KeywordMatch[] = [];

    // 1. Exact match
    const exactMatch = this.findExactMatch(text, keyword);
    if (exactMatch) {
      matches.push(exactMatch);
    }

    // 2. Synonym matches
    if (keyword.synonyms) {
      for (const synonym of keyword.synonyms) {
        const synonymMatch = this.findSynonymMatch(text, keyword, synonym);
        if (synonymMatch) {
          matches.push(synonymMatch);
        }
      }
    }

    // 3. Fuzzy matches
    const fuzzyMatches = this.findFuzzyMatches(words, keyword, options.fuzzyThreshold);
    matches.push(...fuzzyMatches);

    // 4. Semantic similarity matches (if enabled)
    if (options.useSemanticSimilarity) {
      const semanticMatches = await this.findSemanticMatches(text, keyword);
      matches.push(...semanticMatches);
    }

    // 5. Context-aware matches
    const contextMatches = this.findContextMatches(text, keyword, options.contextWindow);
    matches.push(...contextMatches);

    return matches;
  }

  /**
   * Find exact keyword matches
   */
  private findExactMatch(text: string, keyword: SegmentKeyword): KeywordMatch | null {
    const term = keyword.term.toLowerCase();
    const index = text.indexOf(term);

    if (index !== -1) {
      const hasContext = this.checkContext(text, keyword, index);
      
      return {
        keyword: keyword.term,
        matchedText: term,
        confidence: 1.0,
        score: keyword.weight,
        position: index,
        matchType: 'exact',
        hasContext
      };
    }

    return null;
  }

  /**
   * Find synonym matches
   */
  private findSynonymMatch(text: string, keyword: SegmentKeyword, synonym: string): KeywordMatch | null {
    const synonymLower = synonym.toLowerCase();
    const index = text.indexOf(synonymLower);

    if (index !== -1) {
      const hasContext = this.checkContext(text, keyword, index);
      
      return {
        keyword: keyword.term,
        matchedText: synonym,
        confidence: 0.9,
        score: keyword.weight * 0.9,
        position: index,
        matchType: 'synonym',
        hasContext
      };
    }

    return null;
  }

  /**
   * Find fuzzy matches using edit distance
   */
  private findFuzzyMatches(words: string[], keyword: SegmentKeyword, threshold: number): KeywordMatch[] {
    const matches: KeywordMatch[] = [];
    const keywordWords = keyword.term.toLowerCase().split(/\s+/);

    for (let i = 0; i < words.length; i++) {
      for (const keywordWord of keywordWords) {
        if (keywordWord.length < 4) continue; // Skip short words for fuzzy matching

        const similarity = this.calculateStringSimilarity(words[i], keywordWord);
        
        if (similarity >= threshold) {
          matches.push({
            keyword: keyword.term,
            matchedText: words[i],
            confidence: similarity,
            score: keyword.weight * similarity,
            position: i,
            matchType: 'fuzzy',
            hasContext: false
          });
        }
      }
    }

    return matches;
  }

  /**
   * Find semantic similarity matches
   */
  private async findSemanticMatches(text: string, keyword: SegmentKeyword): Promise<KeywordMatch[]> {
    const matches: KeywordMatch[] = [];
    
    // Get semantic embeddings for keyword and text segments
    const semanticTerms = await this.getSemanticSimilarTerms(keyword.term);
    
    for (const term of semanticTerms) {
      if (text.includes(term.word)) {
        matches.push({
          keyword: keyword.term,
          matchedText: term.word,
          confidence: term.similarity,
          score: keyword.weight * term.similarity,
          position: text.indexOf(term.word),
          matchType: 'semantic',
          hasContext: false
        });
      }
    }

    return matches;
  }

  /**
   * Find context-aware matches
   */
  private findContextMatches(text: string, keyword: SegmentKeyword, contextWindow: number): KeywordMatch[] {
    const matches: KeywordMatch[] = [];

    if (!keyword.context) return matches;

    // Look for context terms near the main keyword
    const keywordIndex = text.indexOf(keyword.term.toLowerCase());
    if (keywordIndex === -1) return matches;

    const startPos = Math.max(0, keywordIndex - contextWindow);
    const endPos = Math.min(text.length, keywordIndex + keyword.term.length + contextWindow);
    const contextText = text.substring(startPos, endPos);

    for (const contextTerm of keyword.context) {
      if (contextText.includes(contextTerm.toLowerCase())) {
        matches.push({
          keyword: keyword.term,
          matchedText: `${keyword.term} (with context: ${contextTerm})`,
          confidence: 0.8,
          score: keyword.weight * 0.3, // Bonus for context
          position: keywordIndex,
          matchType: 'contextual',
          hasContext: true
        });
      }
    }

    return matches;
  }

  /**
   * Check if keyword has relevant context
   */
  private checkContext(text: string, keyword: SegmentKeyword, position: number): boolean {
    if (!keyword.context) return false;

    const contextWindow = 100;
    const startPos = Math.max(0, position - contextWindow);
    const endPos = Math.min(text.length, position + keyword.term.length + contextWindow);
    const surroundingText = text.substring(startPos, endPos);

    return keyword.context.some(contextTerm => 
      surroundingText.includes(contextTerm.toLowerCase())
    );
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    const maxLength = Math.max(str1.length, str2.length);
    return maxLength === 0 ? 1 : 1 - matrix[str2.length][str1.length] / maxLength;
  }

  /**
   * Get semantically similar terms (simplified implementation)
   */
  private async getSemanticSimilarTerms(term: string): Promise<{ word: string; similarity: number }[]> {
    const cacheKey = term.toLowerCase();
    
    if (this.semanticCache.has(cacheKey)) {
      return this.parseSemanticCache(this.semanticCache.get(cacheKey)!);
    }

    // Simplified semantic similarity - in production, use word embeddings or API
    const semanticMappings: Record<string, string[]> = {
      'bitcoin': ['btc', 'cryptocurrency', 'digital currency', 'crypto'],
      'stock': ['equity', 'share', 'security', 'investment'],
      'market': ['trading', 'exchange', 'bourse', 'marketplace'],
      'profit': ['earnings', 'income', 'revenue', 'gains'],
      'loss': ['deficit', 'decline', 'drop', 'fall'],
      'growth': ['expansion', 'increase', 'rise', 'surge'],
      'inflation': ['price rise', 'cost increase', 'monetary expansion'],
      'recession': ['downturn', 'contraction', 'slump', 'depression'],
      'federal reserve': ['fed', 'central bank', 'monetary authority'],
      'rbi': ['reserve bank', 'central bank india', 'monetary authority india']
    };

    const similarTerms = semanticMappings[term.toLowerCase()] || [];
    const result = similarTerms.map(word => ({
      word,
      similarity: 0.7 + Math.random() * 0.2 // Simulated similarity score
    }));

    this.semanticCache.set(cacheKey, result.length);
    return result;
  }

  /**
   * Parse semantic cache (placeholder)
   */
  private parseSemanticCache(value: number): { word: string; similarity: number }[] {
    return []; // Simplified implementation
  }

  /**
   * Normalize text for better matching
   */
  private normalizeText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ') // Remove punctuation
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }

  /**
   * Advanced entity matching with fuzzy logic
   */
  async matchEntities(text: string, entities: SegmentEntity[]): Promise<{
    matches: EntityMatch[];
    totalScore: number;
  }> {
    const matches: EntityMatch[] = [];
    let totalScore = 0;

    const normalizedText = this.normalizeText(text);

    for (const entity of entities) {
      const entityMatches = await this.findEntityMatches(normalizedText, entity);
      
      if (entityMatches.length > 0) {
        const bestMatch = entityMatches.reduce((best, current) => 
          current.confidence > best.confidence ? current : best
        );

        matches.push(bestMatch);
        totalScore += bestMatch.score;
      }
    }

    return { matches, totalScore };
  }

  /**
   * Find matches for a specific entity
   */
  private async findEntityMatches(text: string, entity: SegmentEntity): Promise<EntityMatch[]> {
    const matches: EntityMatch[] = [];

    // Check main entity name
    if (text.includes(entity.name.toLowerCase())) {
      matches.push({
        entity: entity.name,
        matchedText: entity.name,
        confidence: 1.0,
        score: entity.weight,
        type: entity.type,
        matchType: 'exact'
      });
    }

    // Check aliases
    for (const alias of entity.aliases) {
      if (text.includes(alias.toLowerCase())) {
        matches.push({
          entity: entity.name,
          matchedText: alias,
          confidence: 0.9,
          score: entity.weight * 0.9,
          type: entity.type,
          matchType: 'alias'
        });
      }
    }

    // Fuzzy matching for entity names
    const words = text.split(/\s+/);
    const entityWords = entity.name.toLowerCase().split(/\s+/);

    for (const word of words) {
      for (const entityWord of entityWords) {
        if (entityWord.length > 3) {
          const similarity = this.calculateStringSimilarity(word, entityWord);
          if (similarity > 0.8) {
            matches.push({
              entity: entity.name,
              matchedText: word,
              confidence: similarity,
              score: entity.weight * similarity,
              type: entity.type,
              matchType: 'fuzzy'
            });
          }
        }
      }
    }

    return matches;
  }
}

// Types for matching results
interface KeywordMatch {
  keyword: string;
  matchedText: string;
  confidence: number;
  score: number;
  position: number;
  matchType: 'exact' | 'synonym' | 'fuzzy' | 'semantic' | 'contextual';
  hasContext: boolean;
}

interface EntityMatch {
  entity: string;
  matchedText: string;
  confidence: number;
  score: number;
  type: string;
  matchType: 'exact' | 'alias' | 'fuzzy';
}

export const smartKeywordMatcher = new SmartKeywordMatcher();
