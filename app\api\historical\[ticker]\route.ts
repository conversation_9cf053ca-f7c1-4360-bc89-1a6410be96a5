import { NextRequest, NextResponse } from 'next/server';

// Alpha Vantage API configuration
const ALPHA_VANTAGE_API_KEY = process.env.ALPHA_VANTAGE_API_KEY || 'demo';
const ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query';

interface HistoricalDataPoint {
  date: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  rsi?: number;
  macd?: number;
  sma20?: number;
  sma50?: number;
}

interface HistoricalResponse {
  symbol: string;
  timeframe: string;
  data: HistoricalDataPoint[];
  indicators: {
    rsi: number[];
    macd: number[];
    sma20: number[];
    sma50: number[];
  };
  patterns: string[];
  keyLevels: {
    support: number[];
    resistance: number[];
  };
}

export async function GET(
  request: NextRequest,
  { params }: { params: { ticker: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '1Y';
    const ticker = params.ticker.toUpperCase();
    
    // Fetch historical data
    let historicalData = await fetchHistoricalData(ticker, timeframe);
    
    // If API fails, generate mock data
    if (!historicalData) {
      historicalData = generateMockHistoricalData(ticker, timeframe);
    }
    
    // Calculate technical indicators
    const dataWithIndicators = calculateTechnicalIndicators(historicalData.data);
    
    // Detect patterns and key levels
    const patterns = detectPatterns(dataWithIndicators);
    const keyLevels = findKeyLevels(dataWithIndicators);
    
    const response: HistoricalResponse = {
      symbol: ticker,
      timeframe: timeframe,
      data: dataWithIndicators,
      indicators: extractIndicators(dataWithIndicators),
      patterns: patterns,
      keyLevels: keyLevels
    };
    
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching historical data:', error);
    
    // Return mock data as fallback
    const mockData = generateMockHistoricalData(params.ticker.toUpperCase(), '1Y');
    const response: HistoricalResponse = {
      symbol: params.ticker.toUpperCase(),
      timeframe: '1Y',
      data: mockData.data,
      indicators: extractIndicators(mockData.data),
      patterns: ['Ascending Triangle', 'Support at $145'],
      keyLevels: {
        support: [145.50, 142.30],
        resistance: [158.75, 162.40]
      }
    };
    
    return NextResponse.json(response);
  }
}

async function fetchHistoricalData(ticker: string, timeframe: string): Promise<{ data: HistoricalDataPoint[] } | null> {
  try {
    let apiFunction = 'TIME_SERIES_DAILY';
    let outputSize = 'compact'; // Last 100 data points
    
    // Determine API function based on timeframe
    switch (timeframe) {
      case '1D':
        apiFunction = 'TIME_SERIES_INTRADAY';
        break;
      case '5D':
      case '1M':
        apiFunction = 'TIME_SERIES_DAILY';
        outputSize = 'compact';
        break;
      case '3M':
      case '1Y':
        apiFunction = 'TIME_SERIES_DAILY';
        outputSize = 'full';
        break;
      case '5Y':
      case '10Y':
        apiFunction = 'TIME_SERIES_WEEKLY';
        outputSize = 'full';
        break;
    }
    
    const url = `${ALPHA_VANTAGE_BASE_URL}?function=${apiFunction}&symbol=${ticker}&outputsize=${outputSize}&apikey=${ALPHA_VANTAGE_API_KEY}`;
    
    if (apiFunction === 'TIME_SERIES_INTRADAY') {
      url += '&interval=5min';
    }
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error('Alpha Vantage API error');
    }
    
    const data = await response.json();
    
    // Parse the response based on the API function
    let timeSeries: any = {};
    if (apiFunction === 'TIME_SERIES_INTRADAY') {
      timeSeries = data['Time Series (5min)'] || {};
    } else if (apiFunction === 'TIME_SERIES_DAILY') {
      timeSeries = data['Time Series (Daily)'] || {};
    } else if (apiFunction === 'TIME_SERIES_WEEKLY') {
      timeSeries = data['Weekly Time Series'] || {};
    }
    
    if (Object.keys(timeSeries).length === 0) {
      throw new Error('No time series data available');
    }
    
    // Convert to our format
    const historicalData: HistoricalDataPoint[] = Object.entries(timeSeries)
      .map(([date, values]: [string, any]) => ({
        date: date,
        open: parseFloat(values['1. open']),
        high: parseFloat(values['2. high']),
        low: parseFloat(values['3. low']),
        close: parseFloat(values['4. close']),
        volume: parseInt(values['5. volume'] || '0')
      }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    
    return { data: historicalData };
  } catch (error) {
    console.error('Historical data fetch error:', error);
    return null;
  }
}

function generateMockHistoricalData(ticker: string, timeframe: string): { data: HistoricalDataPoint[] } {
  const data: HistoricalDataPoint[] = [];
  const today = new Date();
  let days = 252; // 1 year of trading days
  
  // Adjust days based on timeframe
  switch (timeframe) {
    case '1D':
      days = 1;
      break;
    case '5D':
      days = 5;
      break;
    case '1M':
      days = 22;
      break;
    case '3M':
      days = 66;
      break;
    case '1Y':
      days = 252;
      break;
    case '5Y':
      days = 1260;
      break;
    case '10Y':
      days = 2520;
      break;
  }
  
  let currentPrice = 100 + Math.random() * 100; // Start between $100-$200
  
  for (let i = days; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    
    // Skip weekends
    if (date.getDay() === 0 || date.getDay() === 6) {
      continue;
    }
    
    // Generate realistic price movement
    const volatility = 0.02; // 2% daily volatility
    const drift = 0.0003; // Slight upward drift
    const change = (Math.random() - 0.5) * volatility + drift;
    
    const open = currentPrice;
    const close = currentPrice * (1 + change);
    const high = Math.max(open, close) * (1 + Math.random() * 0.01);
    const low = Math.min(open, close) * (1 - Math.random() * 0.01);
    const volume = Math.floor(Math.random() * 10000000) + 1000000;
    
    data.push({
      date: date.toISOString().split('T')[0],
      open: parseFloat(open.toFixed(2)),
      high: parseFloat(high.toFixed(2)),
      low: parseFloat(low.toFixed(2)),
      close: parseFloat(close.toFixed(2)),
      volume: volume
    });
    
    currentPrice = close;
  }
  
  return { data };
}

function calculateTechnicalIndicators(data: HistoricalDataPoint[]): HistoricalDataPoint[] {
  const result = [...data];
  
  // Calculate RSI (14-period)
  for (let i = 14; i < result.length; i++) {
    const gains: number[] = [];
    const losses: number[] = [];
    
    for (let j = i - 13; j <= i; j++) {
      const change = result[j].close - result[j - 1].close;
      if (change > 0) {
        gains.push(change);
        losses.push(0);
      } else {
        gains.push(0);
        losses.push(Math.abs(change));
      }
    }
    
    const avgGain = gains.reduce((a, b) => a + b, 0) / 14;
    const avgLoss = losses.reduce((a, b) => a + b, 0) / 14;
    const rs = avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));
    
    result[i].rsi = parseFloat(rsi.toFixed(2));
  }
  
  // Calculate Simple Moving Averages
  for (let i = 19; i < result.length; i++) {
    const sma20 = result.slice(i - 19, i + 1).reduce((sum, point) => sum + point.close, 0) / 20;
    result[i].sma20 = parseFloat(sma20.toFixed(2));
  }
  
  for (let i = 49; i < result.length; i++) {
    const sma50 = result.slice(i - 49, i + 1).reduce((sum, point) => sum + point.close, 0) / 50;
    result[i].sma50 = parseFloat(sma50.toFixed(2));
  }
  
  return result;
}

function extractIndicators(data: HistoricalDataPoint[]) {
  return {
    rsi: data.map(d => d.rsi || 0).filter(v => v > 0),
    macd: data.map(d => d.macd || 0),
    sma20: data.map(d => d.sma20 || 0).filter(v => v > 0),
    sma50: data.map(d => d.sma50 || 0).filter(v => v > 0)
  };
}

function detectPatterns(data: HistoricalDataPoint[]): string[] {
  const patterns: string[] = [];
  
  if (data.length < 20) return patterns;
  
  const recent = data.slice(-20);
  const highs = recent.map(d => d.high);
  const lows = recent.map(d => d.low);
  
  // Simple pattern detection
  const isUptrend = recent[recent.length - 1].close > recent[0].close;
  const volatility = Math.max(...highs) - Math.min(...lows);
  
  if (isUptrend && volatility < recent[0].close * 0.1) {
    patterns.push('Ascending Triangle');
  }
  
  const supportLevel = Math.min(...lows);
  patterns.push(`Support at $${supportLevel.toFixed(2)}`);
  
  const resistanceLevel = Math.max(...highs);
  patterns.push(`Resistance at $${resistanceLevel.toFixed(2)}`);
  
  return patterns;
}

function findKeyLevels(data: HistoricalDataPoint[]) {
  if (data.length < 20) {
    return { support: [], resistance: [] };
  }
  
  const recent = data.slice(-50); // Last 50 data points
  const highs = recent.map(d => d.high);
  const lows = recent.map(d => d.low);
  
  // Find support and resistance levels
  const support = [Math.min(...lows), ...lows.filter(l => lows.filter(x => Math.abs(x - l) < l * 0.01).length > 2)];
  const resistance = [Math.max(...highs), ...highs.filter(h => highs.filter(x => Math.abs(x - h) < h * 0.01).length > 2)];
  
  return {
    support: [...new Set(support)].sort((a, b) => b - a).slice(0, 3),
    resistance: [...new Set(resistance)].sort((a, b) => a - b).slice(0, 3)
  };
}
