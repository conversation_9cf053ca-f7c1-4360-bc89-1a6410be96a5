/** @type {import('next').NextConfig} */
const nextConfig = {
  // Removed 'output: export' to enable API routes and server-side functionality
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    unoptimized: true,
    domains: ['localhost', 'example.com'] // Add domains for external images if needed
  },
  // Enable experimental features for better performance
  experimental: {
    serverComponentsExternalPackages: ['@google/generative-ai']
  },
  // Environment variables configuration
  env: {
    NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001',
  }
};

module.exports = nextConfig;
