const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function testLogin() {
  try {
    console.log('🔍 Testing login process...')
    
    // First, let's see what users exist
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        password: true
      }
    })
    
    console.log(`\n📊 Found ${users.length} users in database`)
    
    if (users.length === 0) {
      console.log('❌ No users found! Please sign up first.')
      return
    }
    
    // Test login with the first user
    const testUser = users[0]
    console.log(`\n🧪 Testing login for: ${testUser.email}`)
    
    if (!testUser.password) {
      console.log('❌ User has no password set!')
      return
    }
    
    // Try to simulate the login process
    console.log('🔐 Simulating login process...')
    
    // Step 1: Find user by email (this is what happens in auth.ts)
    const foundUser = await prisma.user.findUnique({
      where: {
        email: testUser.email
      }
    })
    
    if (!foundUser) {
      console.log('❌ User not found by email lookup')
      return
    }
    
    console.log('✅ User found by email lookup')
    console.log(`   ID: ${foundUser.id}`)
    console.log(`   Email: ${foundUser.email}`)
    console.log(`   Has password: ${foundUser.password ? 'Yes' : 'No'}`)
    
    // Step 2: Test password comparison
    if (foundUser.password) {
      console.log('\n🔐 Testing password verification...')
      
      // We need to know what password was used during signup
      // Let's try some common test passwords
      const commonPasswords = [
        'password123',
        'Password123', 
        'SecurePass123',
        'Test123',
        'test123',
        'TestPassword123'
      ]
      
      let passwordFound = false
      for (const testPassword of commonPasswords) {
        try {
          const isValid = await bcrypt.compare(testPassword, foundUser.password)
          if (isValid) {
            console.log(`✅ Password verified! The password is: ${testPassword}`)
            passwordFound = true
            break
          }
        } catch (error) {
          console.log(`❌ Error testing password ${testPassword}:`, error.message)
        }
      }
      
      if (!passwordFound) {
        console.log('❌ None of the common passwords worked')
        console.log('💡 Try logging in with the exact password you used during signup')
      }
    }
    
    console.log('\n✅ Login test completed!')
    
  } catch (error) {
    console.error('❌ Login test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testLogin()
