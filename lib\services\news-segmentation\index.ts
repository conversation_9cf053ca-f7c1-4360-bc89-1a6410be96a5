// Main News Segmentation Service

export * from './types';
export * from './segmentation-engine';
export * from './smart-matcher';

import { NewsArticle } from '../news/types';
import { segmentationEngine } from './segmentation-engine';
import { smartKeywordMatcher } from './smart-matcher';
import { newsDeduplicationService } from '../news/deduplication';
import { SegmentationResult, ADVANCED_NEWS_SEGMENTS } from './types';

class NewsSegmentationService {
  /**
   * Get highly relevant articles for a specific market segment
   */
  async getSegmentedNews(
    articles: NewsArticle[],
    segmentId: string,
    options: {
      minRelevanceScore?: number;
      maxArticles?: number;
      sortBy?: 'relevance' | 'time' | 'impact';
    } = {}
  ): Promise<{
    articles: NewsArticle[];
    segmentations: SegmentationResult[];
    totalRelevant: number;
    averageRelevance: number;
  }> {
    const {
      minRelevanceScore = 0.2, // Lower default threshold
      maxArticles = 20,
      sortBy = 'relevance'
    } = options;

    try {
      // Reset deduplication for fresh segmentation
      newsDeduplicationService.reset();

      // Segment all articles
      const segmentations = await segmentationEngine.segmentArticles(articles);
      
      // Filter for the requested segment
      let relevantSegmentations = segmentations.filter(seg =>
        seg.primarySegment === segmentId &&
        seg.relevanceScore >= minRelevanceScore
      );

      // If no articles found with the threshold, try with a lower threshold
      if (relevantSegmentations.length === 0 && minRelevanceScore > 0.1) {
        console.warn(`No articles found for segment ${segmentId} with threshold ${minRelevanceScore}, trying lower threshold`);
        relevantSegmentations = segmentations.filter(seg =>
          seg.primarySegment === segmentId &&
          seg.relevanceScore >= 0.1
        );
      }

      // If still no articles, try to find any articles that have some relevance to the segment
      if (relevantSegmentations.length === 0) {
        console.warn(`No articles found for segment ${segmentId}, looking for any matches`);
        relevantSegmentations = segmentations.filter(seg =>
          seg.segments.some(s => s.segmentId === segmentId && s.relevanceScore >= 0.05)
        );
      }

      // Sort segmentations
      this.sortSegmentations(relevantSegmentations, sortBy);

      // Get corresponding articles and apply final deduplication
      const relevantArticles = relevantSegmentations
        .slice(0, maxArticles)
        .map(seg => articles.find(article => article.id === seg.articleId))
        .filter(article => article !== undefined) as NewsArticle[];

      // Apply final deduplication and category filtering with minimum 9 articles
      const finalArticles = newsDeduplicationService.filterAndDeduplicateForCategory(
        relevantArticles,
        segmentId as any, // Type assertion for compatibility
        maxArticles,
        9 // Minimum 9 articles per category
      );

      // Calculate statistics
      const totalRelevant = finalArticles.length;
      const averageRelevance = totalRelevant > 0
        ? relevantSegmentations.slice(0, totalRelevant).reduce((acc, seg) => acc + seg.relevanceScore, 0) / totalRelevant
        : 0;

      // Log only if no articles found for debugging
      if (totalRelevant === 0) {
        console.warn(`No articles found for segment ${segmentId} with threshold ${minRelevanceScore}`);
      }

      return {
        articles: finalArticles,
        segmentations: relevantSegmentations.slice(0, finalArticles.length),
        totalRelevant,
        averageRelevance
      };
    } catch (error) {
      console.error('Error in news segmentation:', error);
      return {
        articles: [],
        segmentations: [],
        totalRelevant: 0,
        averageRelevance: 0
      };
    }
  }

  /**
   * Get multi-segment analysis for articles
   */
  async getMultiSegmentAnalysis(articles: NewsArticle[]): Promise<{
    segmentDistribution: Record<string, number>;
    topArticlesBySegment: Record<string, NewsArticle[]>;
    crossSegmentArticles: NewsArticle[];
    segmentationQuality: number;
  }> {
    try {
      const segmentations = await segmentationEngine.segmentArticles(articles);
      
      // Calculate segment distribution
      const segmentDistribution: Record<string, number> = {};
      const articlesBySegment: Record<string, NewsArticle[]> = {};
      
      // Initialize segments
      Object.keys(ADVANCED_NEWS_SEGMENTS).forEach(segmentId => {
        segmentDistribution[segmentId] = 0;
        articlesBySegment[segmentId] = [];
      });

      // Process segmentations
      segmentations.forEach(seg => {
        if (seg.relevanceScore >= 0.3) {
          segmentDistribution[seg.primarySegment]++;
          
          const article = articles.find(a => a.id === seg.articleId);
          if (article) {
            articlesBySegment[seg.primarySegment].push(article);
          }
        }
      });

      // Get top articles by segment (top 5 per segment)
      const topArticlesBySegment: Record<string, NewsArticle[]> = {};
      Object.keys(articlesBySegment).forEach(segmentId => {
        const segmentArticles = articlesBySegment[segmentId];
        const segmentSegmentations = segmentations.filter(s => s.primarySegment === segmentId);
        
        // Sort by relevance score
        const sortedArticles = segmentArticles
          .map(article => ({
            article,
            segmentation: segmentSegmentations.find(s => s.articleId === article.id)
          }))
          .filter(item => item.segmentation)
          .sort((a, b) => (b.segmentation?.relevanceScore || 0) - (a.segmentation?.relevanceScore || 0))
          .slice(0, 5)
          .map(item => item.article);
        
        topArticlesBySegment[segmentId] = sortedArticles;
      });

      // Find cross-segment articles (articles that match multiple segments well)
      const crossSegmentArticles = articles.filter(article => {
        const segmentation = segmentations.find(s => s.articleId === article.id);
        if (!segmentation) return false;
        
        const highConfidenceMatches = segmentation.segments.filter(s => s.relevanceScore >= 0.4);
        return highConfidenceMatches.length > 1;
      });

      // Calculate overall segmentation quality
      const avgConfidence = segmentations.reduce((acc, seg) => acc + seg.confidence, 0) / segmentations.length;
      const highQualitySegmentations = segmentations.filter(seg => seg.confidence >= 0.7).length;
      const segmentationQuality = (avgConfidence + (highQualitySegmentations / segmentations.length)) / 2;

      return {
        segmentDistribution,
        topArticlesBySegment,
        crossSegmentArticles,
        segmentationQuality
      };
    } catch (error) {
      console.error('Error in multi-segment analysis:', error);
      return {
        segmentDistribution: {},
        topArticlesBySegment: {},
        crossSegmentArticles: [],
        segmentationQuality: 0
      };
    }
  }

  /**
   * Get segment recommendations for user
   */
  async getSegmentRecommendations(
    articles: NewsArticle[],
    userPreferences?: {
      preferredSegments?: string[];
      minRelevanceScore?: number;
      maxArticlesPerSegment?: number;
    }
  ): Promise<{
    recommendations: SegmentRecommendation[];
    totalArticles: number;
    qualityScore: number;
  }> {
    const {
      preferredSegments = Object.keys(ADVANCED_NEWS_SEGMENTS),
      minRelevanceScore = 0.4,
      maxArticlesPerSegment = 10
    } = userPreferences || {};

    try {
      const recommendations: SegmentRecommendation[] = [];
      let totalArticles = 0;
      let totalQuality = 0;

      for (const segmentId of preferredSegments) {
        const segmentResult = await this.getSegmentedNews(articles, segmentId, {
          minRelevanceScore,
          maxArticles: maxArticlesPerSegment,
          sortBy: 'relevance'
        });

        if (segmentResult.articles.length > 0) {
          const segment = ADVANCED_NEWS_SEGMENTS[segmentId];
          
          recommendations.push({
            segmentId,
            segmentName: segment.name,
            articles: segmentResult.articles,
            relevantCount: segmentResult.totalRelevant,
            averageRelevance: segmentResult.averageRelevance,
            topReasons: this.extractTopReasons(segmentResult.segmentations),
            qualityIndicators: this.calculateQualityIndicators(segmentResult.segmentations)
          });

          totalArticles += segmentResult.articles.length;
          totalQuality += segmentResult.averageRelevance;
        }
      }

      const qualityScore = recommendations.length > 0 
        ? totalQuality / recommendations.length 
        : 0;

      // Sort recommendations by relevance and article count
      recommendations.sort((a, b) => {
        const scoreA = a.averageRelevance * Math.log(a.articles.length + 1);
        const scoreB = b.averageRelevance * Math.log(b.articles.length + 1);
        return scoreB - scoreA;
      });

      return {
        recommendations,
        totalArticles,
        qualityScore
      };
    } catch (error) {
      console.error('Error generating segment recommendations:', error);
      return {
        recommendations: [],
        totalArticles: 0,
        qualityScore: 0
      };
    }
  }

  /**
   * Analyze segmentation performance
   */
  async analyzeSegmentationPerformance(articles: NewsArticle[]): Promise<{
    overallAccuracy: number;
    segmentPerformance: Record<string, SegmentPerformance>;
    improvementSuggestions: string[];
  }> {
    try {
      const segmentations = await segmentationEngine.segmentArticles(articles);
      
      // Calculate overall accuracy
      const highConfidenceSegmentations = segmentations.filter(seg => seg.confidence >= 0.7);
      const overallAccuracy = highConfidenceSegmentations.length / segmentations.length;

      // Calculate per-segment performance
      const segmentPerformance: Record<string, SegmentPerformance> = {};
      
      Object.keys(ADVANCED_NEWS_SEGMENTS).forEach(segmentId => {
        const segmentSegmentations = segmentations.filter(seg => seg.primarySegment === segmentId);
        const avgConfidence = segmentSegmentations.length > 0
          ? segmentSegmentations.reduce((acc, seg) => acc + seg.confidence, 0) / segmentSegmentations.length
          : 0;
        const avgRelevance = segmentSegmentations.length > 0
          ? segmentSegmentations.reduce((acc, seg) => acc + seg.relevanceScore, 0) / segmentSegmentations.length
          : 0;

        segmentPerformance[segmentId] = {
          articleCount: segmentSegmentations.length,
          averageConfidence: avgConfidence,
          averageRelevance: avgRelevance,
          highQualityCount: segmentSegmentations.filter(seg => seg.confidence >= 0.8).length
        };
      });

      // Generate improvement suggestions
      const improvementSuggestions = this.generateImprovementSuggestions(segmentPerformance, overallAccuracy);

      return {
        overallAccuracy,
        segmentPerformance,
        improvementSuggestions
      };
    } catch (error) {
      console.error('Error analyzing segmentation performance:', error);
      return {
        overallAccuracy: 0,
        segmentPerformance: {},
        improvementSuggestions: ['Error in performance analysis']
      };
    }
  }

  /**
   * Helper methods
   */
  private sortSegmentations(segmentations: SegmentationResult[], sortBy: string): void {
    switch (sortBy) {
      case 'relevance':
        segmentations.sort((a, b) => b.relevanceScore - a.relevanceScore);
        break;
      case 'time':
        // Would need article timestamps - placeholder
        break;
      case 'impact':
        segmentations.sort((a, b) => b.confidence - a.confidence);
        break;
    }
  }

  private extractTopReasons(segmentations: SegmentationResult[]): string[] {
    const allReasons = segmentations.flatMap(seg => seg.reasons);
    const reasonCounts = allReasons.reduce((acc, reason) => {
      acc[reason] = (acc[reason] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(reasonCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([reason]) => reason);
  }

  private calculateQualityIndicators(segmentations: SegmentationResult[]): QualityIndicators {
    const avgConfidence = segmentations.reduce((acc, seg) => acc + seg.confidence, 0) / segmentations.length;
    const highConfidenceCount = segmentations.filter(seg => seg.confidence >= 0.8).length;
    const multiMatchCount = segmentations.filter(seg => seg.segments.length > 1).length;

    return {
      averageConfidence: avgConfidence,
      highConfidencePercentage: (highConfidenceCount / segmentations.length) * 100,
      multiMatchPercentage: (multiMatchCount / segmentations.length) * 100
    };
  }

  private generateImprovementSuggestions(
    performance: Record<string, SegmentPerformance>, 
    overallAccuracy: number
  ): string[] {
    const suggestions: string[] = [];

    if (overallAccuracy < 0.7) {
      suggestions.push('Consider refining keyword weights and adding more specific terms');
    }

    Object.entries(performance).forEach(([segmentId, perf]) => {
      if (perf.averageConfidence < 0.6) {
        const segmentName = ADVANCED_NEWS_SEGMENTS[segmentId]?.name || segmentId;
        suggestions.push(`Improve ${segmentName} segment with more specific keywords and entities`);
      }
    });

    if (suggestions.length === 0) {
      suggestions.push('Segmentation performance is good - consider fine-tuning for edge cases');
    }

    return suggestions;
  }
}

// Additional types
interface SegmentRecommendation {
  segmentId: string;
  segmentName: string;
  articles: NewsArticle[];
  relevantCount: number;
  averageRelevance: number;
  topReasons: string[];
  qualityIndicators: QualityIndicators;
}

interface QualityIndicators {
  averageConfidence: number;
  highConfidencePercentage: number;
  multiMatchPercentage: number;
}

interface SegmentPerformance {
  articleCount: number;
  averageConfidence: number;
  averageRelevance: number;
  highQualityCount: number;
}

export const newsSegmentationService = new NewsSegmentationService();
