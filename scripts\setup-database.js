const { Client } = require('pg')
const fs = require('fs')
const path = require('path')

async function setupDatabase() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  })

  try {
    console.log('🔌 Connecting to Neon PostgreSQL...')
    await client.connect()
    console.log('✅ Connected to database successfully!')

    // Read the SQL file
    const sqlPath = path.join(__dirname, 'create-tables.sql')
    const sql = fs.readFileSync(sqlPath, 'utf8')

    console.log('📊 Creating database tables...')
    await client.query(sql)
    console.log('✅ Database tables created successfully!')

    // Test the connection by checking if users table exists
    const result = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = 'users'
    `)

    if (result.rows.length > 0) {
      console.log('✅ Users table verified successfully!')
    } else {
      console.log('❌ Users table not found!')
    }

    console.log('🎉 Database setup completed successfully!')

  } catch (error) {
    console.error('❌ Database setup failed:', error.message)
    process.exit(1)
  } finally {
    await client.end()
  }
}

// Load environment variables
require('dotenv').config({ path: '.env.local' })

setupDatabase()
