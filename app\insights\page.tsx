"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Globe, 
  Brain,
  RefreshCw,
  Clock,
  AlertTriangle,
  Target,
  Newspaper,
  BarChart3,
  Zap,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Minus
} from 'lucide-react';

interface MarketInsights {
  timestamp: string;
  marketSentiment: {
    overall: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
    factors: string[];
  };
  sectorAnalysis: Array<{
    sector: string;
    performance: number;
    topStocks: Array<{
      symbol: string;
      name: string;
      performance: number;
      volume: number;
      signal: 'opportunity' | 'caution' | 'neutral';
    }>;
    outlook: string;
  }>;
  opportunityRadar: Array<{
    type: 'opportunity' | 'risk';
    asset: {
      symbol: string;
      name: string;
      price: number;
      change: number;
    };
    reason: string;
    confidence: number;
    aiExplanation: string;
  }>;
  newsImpacts: Array<{
    headline: string;
    summary: string;
    sentiment: 'positive' | 'negative' | 'neutral';
    impactScore: number;
    affectedAssets: string[];
  }>;
  insightQuestions: Array<{
    id: string;
    question: string;
    category: 'market' | 'sector' | 'stock' | 'crypto';
    complexity: 'beginner' | 'intermediate' | 'advanced';
  }>;
}

export default function MarketInsightsPage() {
  const [insights, setInsights] = useState<MarketInsights | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [activeRegion, setActiveRegion] = useState('india');

  useEffect(() => {
    fetchMarketInsights();
    // Auto-refresh every 5 minutes
    const interval = setInterval(fetchMarketInsights, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const fetchMarketInsights = async (refresh = false) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/market-insights${refresh ? '?refresh=true' : ''}`);
      const data = await response.json();
      
      if (data.success) {
        setInsights(data.data);
        setLastUpdated(new Date());
      } else {
        // Use mock data if API fails
        setInsights(generateMockInsights());
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('Failed to fetch market insights:', error);
      // Use mock data as fallback
      setInsights(generateMockInsights());
      setLastUpdated(new Date());
    } finally {
      setLoading(false);
    }
  };

  const generateMockInsights = (): MarketInsights => ({
    timestamp: new Date().toISOString(),
    marketSentiment: {
      overall: 'bullish',
      confidence: 75,
      factors: ['Strong earnings growth', 'Positive FII flows', 'Sector rotation']
    },
    sectorAnalysis: [
      {
        sector: 'Information Technology',
        performance: 2.45,
        topStocks: [
          { symbol: 'TCS', name: 'Tata Consultancy Services', performance: 3.2, volume: 1800000, signal: 'opportunity' },
          { symbol: 'INFY', name: 'Infosys Ltd', performance: 1.8, volume: 2100000, signal: 'neutral' },
          { symbol: 'WIPRO', name: 'Wipro Ltd', performance: 2.1, volume: 1500000, signal: 'opportunity' }
        ],
        outlook: 'IT sector showing strong momentum driven by digital transformation demand and strong Q3 earnings.'
      },
      {
        sector: 'Banking',
        performance: -1.2,
        topStocks: [
          { symbol: 'HDFCBANK', name: 'HDFC Bank Ltd', performance: -0.8, volume: 3200000, signal: 'caution' },
          { symbol: 'ICICIBANK', name: 'ICICI Bank Ltd', performance: -1.5, volume: 4500000, signal: 'opportunity' },
          { symbol: 'SBIN', name: 'State Bank of India', performance: 2.1, volume: 8900000, signal: 'opportunity' }
        ],
        outlook: 'Banking sector under pressure due to NIM concerns, but PSU banks showing resilience.'
      },
      {
        sector: 'Pharmaceuticals',
        performance: 1.8,
        topStocks: [
          { symbol: 'SUNPHARMA', name: 'Sun Pharmaceutical', performance: 2.5, volume: 1200000, signal: 'opportunity' },
          { symbol: 'DRREDDY', name: 'Dr Reddys Laboratories', performance: 1.2, volume: 890000, signal: 'neutral' }
        ],
        outlook: 'Pharma sector benefiting from export demand and new product launches.'
      }
    ],
    opportunityRadar: [
      {
        type: 'opportunity',
        asset: { symbol: 'ICICIBANK', name: 'ICICI Bank Ltd', price: 945.60, change: -1.5 },
        reason: 'Oversold RSI, Strong fundamentals',
        confidence: 82,
        aiExplanation: 'ICICI Bank has corrected 8% from recent highs despite strong Q3 results. RSI at 28 indicates oversold conditions. Strong deposit growth and improving asset quality make this an attractive entry point for medium-term investors.'
      },
      {
        type: 'opportunity',
        asset: { symbol: 'TCS', name: 'Tata Consultancy Services', price: 3567.25, change: 3.2 },
        reason: 'Earnings beat, Positive guidance',
        confidence: 78,
        aiExplanation: 'TCS reported 12% YoY revenue growth beating estimates. Management raised FY24 guidance citing strong deal pipeline. Stock breaking out of 6-month consolidation with strong volume support.'
      },
      {
        type: 'risk',
        asset: { symbol: 'ADANIPORTS', name: 'Adani Ports', price: 789.45, change: -5.2 },
        reason: 'High debt levels, Regulatory concerns',
        confidence: 85,
        aiExplanation: 'Adani Ports facing headwinds from high leverage ratios and ongoing regulatory scrutiny. Recent credit rating concerns and FII selling pressure suggest further downside risk in near term.'
      }
    ],
    newsImpacts: [
      {
        headline: 'RBI Maintains Repo Rate at 6.5%, Signals Pause in Rate Hike Cycle',
        summary: 'Reserve Bank of India keeps policy rates unchanged, citing balanced approach to inflation and growth.',
        sentiment: 'positive',
        impactScore: 85,
        affectedAssets: ['HDFCBANK', 'ICICIBANK', 'SBIN', 'KOTAKBANK']
      },
      {
        headline: 'TCS Reports Strong Q3 Earnings, Beats Street Estimates',
        summary: 'Tata Consultancy Services posts 12% YoY revenue growth with improved margins and positive outlook.',
        sentiment: 'positive',
        impactScore: 78,
        affectedAssets: ['TCS', 'INFY', 'WIPRO', 'HCLTECH']
      },
      {
        headline: 'FII Outflows Continue for Third Consecutive Week',
        summary: 'Foreign institutional investors pull out ₹8,500 crores from Indian equities amid global uncertainty.',
        sentiment: 'negative',
        impactScore: 72,
        affectedAssets: ['NIFTY', 'SENSEX']
      }
    ],
    insightQuestions: [
      {
        id: '1',
        question: 'Why is the IT sector outperforming despite global slowdown concerns?',
        category: 'sector',
        complexity: 'intermediate'
      },
      {
        id: '2',
        question: 'Is this the right time to buy banking stocks after the recent correction?',
        category: 'sector',
        complexity: 'beginner'
      },
      {
        id: '3',
        question: 'How will the RBI rate pause impact different sectors?',
        category: 'market',
        complexity: 'advanced'
      },
      {
        id: '4',
        question: 'Which stocks look oversold on technical indicators today?',
        category: 'stock',
        complexity: 'intermediate'
      }
    ]
  });

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'bullish': return 'text-green-400';
      case 'bearish': return 'text-red-400';
      default: return 'text-yellow-400';
    }
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'bullish': return <TrendingUp className="h-5 w-5" />;
      case 'bearish': return <TrendingDown className="h-5 w-5" />;
      default: return <Activity className="h-5 w-5" />;
    }
  };

  const getPerformanceIcon = (performance: number) => {
    if (performance > 0) return <ArrowUpRight className="h-4 w-4 text-green-400" />;
    if (performance < 0) return <ArrowDownRight className="h-4 w-4 text-red-400" />;
    return <Minus className="h-4 w-4 text-gray-400" />;
  };

  if (loading && !insights) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-400 mx-auto mb-4" />
          <p className="text-slate-300">Generating AI-powered market insights...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <div className="border-b border-slate-800 bg-slate-900/95 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white flex items-center gap-2">
                <Brain className="h-6 w-6 text-blue-400" />
                AI Market Insights Engine
              </h1>
              <p className="text-slate-400 text-sm">
                Real-time analysis across Indian equities, global markets, and cryptocurrencies
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              {lastUpdated && (
                <div className="text-xs text-slate-400 flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  Updated {lastUpdated.toLocaleTimeString()}
                </div>
              )}
              
              <Button
                onClick={() => fetchMarketInsights(true)}
                disabled={loading}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6 space-y-6">
        {/* Market Sentiment Overview */}
        {insights?.marketSentiment && (
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-blue-400" />
                Overall Market Sentiment
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className={`flex items-center gap-2 ${getSentimentColor(insights.marketSentiment.overall)}`}>
                    {getSentimentIcon(insights.marketSentiment.overall)}
                    <span className="text-xl font-bold capitalize">
                      {insights.marketSentiment.overall}
                    </span>
                  </div>
                  <Badge variant="outline" className="border-slate-600">
                    {insights.marketSentiment.confidence}% Confidence
                  </Badge>
                </div>
                
                <div className="text-right">
                  <p className="text-sm text-slate-400 mb-1">Key Factors:</p>
                  <div className="flex flex-wrap gap-1 justify-end">
                    {insights.marketSentiment.factors.map((factor, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {factor}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content Tabs */}
        <Tabs defaultValue="sectors" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5 bg-slate-800 border-slate-700">
            <TabsTrigger value="sectors" className="data-[state=active]:bg-slate-700">
              Sector Analysis
            </TabsTrigger>
            <TabsTrigger value="opportunities" className="data-[state=active]:bg-slate-700">
              Opportunity Radar
            </TabsTrigger>
            <TabsTrigger value="news" className="data-[state=active]:bg-slate-700">
              News Impact
            </TabsTrigger>
            <TabsTrigger value="ai-insights" className="data-[state=active]:bg-slate-700">
              AI Questions
            </TabsTrigger>
            <TabsTrigger value="regional" className="data-[state=active]:bg-slate-700">
              Regional View
            </TabsTrigger>
          </TabsList>

          {/* Sector Analysis Tab */}
          <TabsContent value="sectors" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {insights?.sectorAnalysis?.map((sector, index) => (
                <Card key={index} className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="text-lg">{sector.sector}</span>
                      <div className="flex items-center gap-2">
                        {getPerformanceIcon(sector.performance)}
                        <Badge 
                          variant={sector.performance > 0 ? "default" : "destructive"}
                          className={sector.performance > 0 ? "bg-green-600" : "bg-red-600"}
                        >
                          {sector.performance > 0 ? '+' : ''}{sector.performance.toFixed(2)}%
                        </Badge>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-slate-400">{sector.outlook}</p>
                    
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-slate-300">Top Performers:</h4>
                      {sector.topStocks?.slice(0, 3).map((stock, stockIndex) => (
                        <div key={stockIndex} className="flex justify-between items-center p-2 bg-slate-700/50 rounded">
                          <div>
                            <span className="text-sm font-medium">{stock.symbol}</span>
                            <p className="text-xs text-slate-400">{stock.name}</p>
                          </div>
                          <div className="text-right">
                            <span className={`text-sm font-medium ${stock.performance > 0 ? 'text-green-400' : 'text-red-400'}`}>
                              {stock.performance > 0 ? '+' : ''}{stock.performance.toFixed(2)}%
                            </span>
                            <p className="text-xs text-slate-400">Vol: {(stock.volume / 1000000).toFixed(1)}M</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Opportunity Radar Tab */}
          <TabsContent value="opportunities" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Opportunities */}
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-400">
                    <Target className="h-5 w-5" />
                    High Opportunity Assets
                  </CardTitle>
                  <CardDescription>
                    AI-identified opportunities based on technical and fundamental analysis
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {insights?.opportunityRadar?.filter(item => item.type === 'opportunity').map((opportunity, index) => (
                    <div key={index} className="border border-slate-700 rounded-lg p-4 space-y-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium text-lg">{opportunity.asset.name}</h4>
                          <p className="text-sm text-slate-400">{opportunity.asset.symbol} • ₹{opportunity.asset.price.toFixed(2)}</p>
                        </div>
                        <Badge className="bg-green-600">
                          {opportunity.confidence}% Confidence
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-green-400">{opportunity.reason}</p>
                        <p className="text-sm text-slate-300">{opportunity.aiExplanation}</p>
                      </div>
                      
                      <Button variant="outline" size="sm" className="w-full border-green-600 text-green-400 hover:bg-green-600 hover:text-white">
                        <Eye className="h-4 w-4 mr-2" />
                        View Analysis
                      </Button>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Risks */}
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-400">
                    <AlertTriangle className="h-5 w-5" />
                    High Risk Assets
                  </CardTitle>
                  <CardDescription>
                    Assets flagged for elevated risk factors requiring caution
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {insights?.opportunityRadar?.filter(item => item.type === 'risk').map((risk, index) => (
                    <div key={index} className="border border-slate-700 rounded-lg p-4 space-y-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium text-lg">{risk.asset.name}</h4>
                          <p className="text-sm text-slate-400">{risk.asset.symbol} • ₹{risk.asset.price.toFixed(2)}</p>
                        </div>
                        <Badge variant="destructive">
                          {risk.confidence}% Risk
                        </Badge>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="text-sm font-medium text-red-400">{risk.reason}</p>
                        <p className="text-sm text-slate-300">{risk.aiExplanation}</p>
                      </div>
                      
                      <Button variant="outline" size="sm" className="w-full border-red-600 text-red-400 hover:bg-red-600 hover:text-white">
                        <AlertTriangle className="h-4 w-4 mr-2" />
                        Risk Details
                      </Button>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* News Impact Tab */}
          <TabsContent value="news" className="space-y-6">
            <div className="space-y-4">
              {insights?.newsImpacts?.map((news, index) => (
                <Card key={index} className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Newspaper className="h-5 w-5 text-blue-400" />
                      {news.headline}
                    </CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <Badge variant={news.sentiment === 'positive' ? 'default' : news.sentiment === 'negative' ? 'destructive' : 'secondary'}>
                        {news.sentiment.toUpperCase()}
                      </Badge>
                      <Badge variant="outline">Impact Score: {news.impactScore}/100</Badge>
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-slate-300 mb-4">{news.summary}</p>
                    
                    {news.affectedAssets?.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium mb-2 text-slate-400">Affected Assets:</h4>
                        <div className="flex flex-wrap gap-1">
                          {news.affectedAssets.map((asset, assetIndex) => (
                            <Badge key={assetIndex} variant="outline" className="text-xs border-slate-600">
                              {asset}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* AI Insights Tab */}
          <TabsContent value="ai-insights" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {insights?.insightQuestions?.map((question, index) => (
                <Card key={index} className="bg-slate-800 border-slate-700 cursor-pointer hover:bg-slate-750 transition-colors">
                  <CardHeader>
                    <CardTitle className="text-lg">{question.question}</CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <Badge variant="outline" className="border-slate-600">
                        {question.category.toUpperCase()}
                      </Badge>
                      <Badge variant="secondary">
                        {question.complexity.toUpperCase()}
                      </Badge>
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button variant="outline" size="sm" className="w-full border-slate-600 text-slate-300 hover:bg-slate-700">
                      <Brain className="h-4 w-4 mr-2" />
                      Get AI Analysis
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Regional View Tab */}
          <TabsContent value="regional" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5 text-blue-400" />
                    India Markets
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Nifty 50</span>
                      <span className="text-green-400 text-sm">+0.85%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Sensex</span>
                      <span className="text-green-400 text-sm">+0.92%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Bank Nifty</span>
                      <span className="text-red-400 text-sm">-0.45%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5 text-purple-400" />
                    Global Markets
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">S&P 500</span>
                      <span className="text-green-400 text-sm">+0.65%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Nasdaq</span>
                      <span className="text-green-400 text-sm">+1.12%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Nikkei 225</span>
                      <span className="text-red-400 text-sm">-0.28%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-slate-800 border-slate-700">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-yellow-400" />
                    Crypto Markets
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Bitcoin</span>
                      <span className="text-green-400 text-sm">+2.45%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Ethereum</span>
                      <span className="text-red-400 text-sm">-1.23%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Solana</span>
                      <span className="text-green-400 text-sm">+4.67%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
