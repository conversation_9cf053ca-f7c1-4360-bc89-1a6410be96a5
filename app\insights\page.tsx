"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Globe, 
  Brain,
  RefreshCw,
  Clock,
  AlertTriangle,
  Target,
  Newspaper,
  BarChart3,
  Zap,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Minus
} from 'lucide-react';

interface MarketInsights {
  timestamp: string;
  region: 'india' | 'global' | 'crypto';
  marketSentiment: {
    overall: 'bullish' | 'bearish' | 'neutral';
    confidence: number;
    factors: string[];
  };
  sectorAnalysis: Array<{
    sector: string;
    performance: number;
    topStocks: Array<{
      symbol: string;
      name: string;
      performance: number;
      volume: number;
      signal: 'opportunity' | 'caution' | 'neutral';
    }>;
    outlook: string;
  }>;
  opportunityRadar: Array<{
    type: 'opportunity' | 'risk';
    asset: {
      symbol: string;
      name: string;
      price: number;
      change: number;
    };
    reason: string;
    confidence: number;
    aiExplanation: string;
  }>;
  newsImpacts: Array<{
    headline: string;
    summary: string;
    sentiment: 'positive' | 'negative' | 'neutral';
    impactScore: number;
    affectedAssets: string[];
  }>;
  insightQuestions: Array<{
    id: string;
    question: string;
    category: 'market' | 'sector' | 'stock' | 'crypto' | 'technical' | 'fundamental' | 'news' | 'strategy';
    complexity: 'beginner' | 'intermediate' | 'advanced';
  }>;
  topMovers: Array<{
    symbol: string;
    name: string;
    price: number;
    changePercent: number;
    volume: number;
  }>;
  regionalSummaries: Array<{
    region: string;
    summary: string;
    indices: Array<{
      name: string;
      changePercent: number;
    }>;
    topMovers: Array<{
      symbol: string;
      name: string;
      changePercent: number;
    }>;
  }>;
}

export default function MarketInsightsPage() {
  const [insights, setInsights] = useState<MarketInsights | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [activeRegion, setActiveRegion] = useState<'india' | 'global' | 'crypto'>('india');

  useEffect(() => {
    fetchMarketInsights();
    // Auto-refresh every 5 minutes
    const interval = setInterval(fetchMarketInsights, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const fetchMarketInsights = async (refresh = false, region = activeRegion) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/market-insights?region=${region}${refresh ? '&refresh=true' : ''}`);
      const data = await response.json();

      if (data.success) {
        setInsights(data.data);
        setLastUpdated(new Date());
      } else {
        // Use mock data if API fails
        setInsights(generateMockInsights(region));
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('Failed to fetch market insights:', error);
      // Use mock data as fallback
      setInsights(generateMockInsights(region));
      setLastUpdated(new Date());
    } finally {
      setLoading(false);
    }
  };

  const getRegionSpecificData = (region: 'india' | 'global' | 'crypto') => {
    switch (region) {
      case 'india':
        return {
          sectors: [
            {
              sector: 'Information Technology',
              performance: 2.45,
              topStocks: [
                { symbol: 'TCS', name: 'Tata Consultancy Services', performance: 3.2, volume: 1800000, signal: 'opportunity' as const },
                { symbol: 'INFY', name: 'Infosys Ltd', performance: 1.8, volume: 2100000, signal: 'neutral' as const },
                { symbol: 'WIPRO', name: 'Wipro Ltd', performance: 2.1, volume: 1500000, signal: 'opportunity' as const }
              ],
              outlook: 'IT sector showing strong momentum driven by digital transformation demand and strong Q3 earnings.'
            },
            {
              sector: 'Banking',
              performance: -1.2,
              topStocks: [
                { symbol: 'HDFCBANK', name: 'HDFC Bank Ltd', performance: -0.8, volume: 3200000, signal: 'caution' as const },
                { symbol: 'ICICIBANK', name: 'ICICI Bank Ltd', performance: -1.5, volume: 4500000, signal: 'opportunity' as const },
                { symbol: 'SBIN', name: 'State Bank of India', performance: 2.1, volume: 8900000, signal: 'opportunity' as const }
              ],
              outlook: 'Banking sector under pressure due to NIM concerns, but PSU banks showing resilience.'
            },
            {
              sector: 'Pharmaceuticals',
              performance: 1.8,
              topStocks: [
                { symbol: 'SUNPHARMA', name: 'Sun Pharmaceutical', performance: 2.5, volume: 1200000, signal: 'opportunity' as const },
                { symbol: 'DRREDDY', name: 'Dr Reddys Laboratories', performance: 1.2, volume: 890000, signal: 'neutral' as const }
              ],
              outlook: 'Pharma sector benefiting from export demand and new product launches.'
            }
          ],
          opportunities: [
            {
              type: 'opportunity' as const,
              asset: { symbol: 'ICICIBANK', name: 'ICICI Bank Ltd', price: 945.60, change: -1.5 },
              reason: 'Oversold RSI, Strong fundamentals',
              confidence: 82,
              aiExplanation: 'ICICI Bank has corrected 8% from recent highs despite strong Q3 results. RSI at 28 indicates oversold conditions. Strong deposit growth and improving asset quality make this an attractive entry point for medium-term investors.'
            },
            {
              type: 'opportunity' as const,
              asset: { symbol: 'TCS', name: 'Tata Consultancy Services', price: 3567.25, change: 3.2 },
              reason: 'Earnings beat, Positive guidance',
              confidence: 78,
              aiExplanation: 'TCS reported 12% YoY revenue growth beating estimates. Management raised FY24 guidance citing strong deal pipeline. Stock breaking out of 6-month consolidation with strong volume support.'
            },
            {
              type: 'opportunity' as const,
              asset: { symbol: 'SUNPHARMA', name: 'Sun Pharmaceutical', price: 1089.45, change: 2.5 },
              reason: 'FDA approval, Export growth',
              confidence: 75,
              aiExplanation: 'Sun Pharma received FDA approval for key generic drug. Strong export growth to US markets and improving margins make this attractive at current levels.'
            }
          ],
          risks: [
            {
              type: 'risk' as const,
              asset: { symbol: 'ADANIPORTS', name: 'Adani Ports', price: 789.45, change: -5.2 },
              reason: 'High debt levels, Regulatory concerns',
              confidence: 85,
              aiExplanation: 'Adani Ports facing headwinds from high leverage ratios and ongoing regulatory scrutiny. Recent credit rating concerns and FII selling pressure suggest further downside risk in near term.'
            },
            {
              type: 'risk' as const,
              asset: { symbol: 'PAYTM', name: 'One 97 Communications', price: 456.30, change: -3.8 },
              reason: 'Regulatory uncertainty, Valuation concerns',
              confidence: 80,
              aiExplanation: 'Paytm faces regulatory headwinds and high valuation multiples. Recent RBI guidelines on payment aggregators create uncertainty around business model sustainability.'
            }
          ]
        };
      case 'global':
        return {
          sectors: [
            {
              sector: 'Technology',
              performance: 1.85,
              topStocks: [
                { symbol: 'AAPL', name: 'Apple Inc', performance: 2.1, volume: 45000000, signal: 'neutral' as const },
                { symbol: 'MSFT', name: 'Microsoft Corp', performance: 1.6, volume: 32000000, signal: 'opportunity' as const },
                { symbol: 'GOOGL', name: 'Alphabet Inc', performance: 2.8, volume: 28000000, signal: 'opportunity' as const }
              ],
              outlook: 'Tech sector recovering from 2023 lows, driven by AI optimism and cloud growth.'
            },
            {
              sector: 'Healthcare',
              performance: 0.95,
              topStocks: [
                { symbol: 'JNJ', name: 'Johnson & Johnson', performance: 1.2, volume: 15000000, signal: 'neutral' as const },
                { symbol: 'PFE', name: 'Pfizer Inc', performance: -0.8, volume: ********, signal: 'caution' as const }
              ],
              outlook: 'Healthcare showing mixed performance with pharma facing patent cliff concerns.'
            },
            {
              sector: 'Financial Services',
              performance: -0.65,
              topStocks: [
                { symbol: 'JPM', name: 'JPMorgan Chase', performance: -0.4, volume: ********, signal: 'opportunity' as const },
                { symbol: 'BAC', name: 'Bank of America', performance: -1.1, volume: ********, signal: 'caution' as const }
              ],
              outlook: 'Banks under pressure from rate cut expectations and credit concerns.'
            }
          ],
          opportunities: [
            {
              type: 'opportunity' as const,
              asset: { symbol: 'MSFT', name: 'Microsoft Corp', price: 378.85, change: 1.6 },
              reason: 'AI leadership, Cloud growth',
              confidence: 88,
              aiExplanation: 'Microsoft leading AI revolution with Copilot integration. Azure cloud growth accelerating and strong enterprise demand. Trading at reasonable valuation despite AI premium.'
            },
            {
              type: 'opportunity' as const,
              asset: { symbol: 'GOOGL', name: 'Alphabet Inc', price: 142.56, change: 2.8 },
              reason: 'Search dominance, AI integration',
              confidence: 84,
              aiExplanation: 'Google maintaining search market share while successfully integrating AI. Bard improvements and cloud growth provide multiple expansion avenues.'
            },
            {
              type: 'opportunity' as const,
              asset: { symbol: 'JPM', name: 'JPMorgan Chase', price: 168.45, change: -0.4 },
              reason: 'Strong balance sheet, Rate environment',
              confidence: 79,
              aiExplanation: 'JPM well-positioned for higher-for-longer rate environment. Strong capital ratios and diversified revenue streams provide defensive characteristics.'
            }
          ],
          risks: [
            {
              type: 'risk' as const,
              asset: { symbol: 'TSLA', name: 'Tesla Inc', price: 201.29, change: -4.2 },
              reason: 'Valuation concerns, Competition',
              confidence: 87,
              aiExplanation: 'Tesla facing increased EV competition and margin pressure. High valuation multiples vulnerable to delivery disappointments and macro headwinds.'
            },
            {
              type: 'risk' as const,
              asset: { symbol: 'NFLX', name: 'Netflix Inc', price: 445.73, change: -2.8 },
              reason: 'Subscriber growth slowdown, Content costs',
              confidence: 82,
              aiExplanation: 'Netflix subscriber growth plateauing in mature markets. Rising content costs and streaming competition pressuring margins and growth outlook.'
            }
          ]
        };
      case 'crypto':
        return {
          sectors: [
            {
              sector: 'Layer 1 Blockchains',
              performance: 3.45,
              topStocks: [
                { symbol: 'BTC', name: 'Bitcoin', performance: 2.8, volume: 28000000000, signal: 'opportunity' as const },
                { symbol: 'ETH', name: 'Ethereum', performance: 4.2, volume: 15000000000, signal: 'opportunity' as const },
                { symbol: 'SOL', name: 'Solana', performance: 8.5, volume: 2500000000, signal: 'opportunity' as const }
              ],
              outlook: 'Layer 1s benefiting from institutional adoption and ETF optimism.'
            },
            {
              sector: 'DeFi Tokens',
              performance: 5.2,
              topStocks: [
                { symbol: 'UNI', name: 'Uniswap', performance: 6.8, volume: 450000000, signal: 'opportunity' as const },
                { symbol: 'AAVE', name: 'Aave', performance: 4.1, volume: 280000000, signal: 'neutral' as const }
              ],
              outlook: 'DeFi tokens rallying on increased TVL and protocol improvements.'
            },
            {
              sector: 'AI Tokens',
              performance: 12.8,
              topStocks: [
                { symbol: 'FET', name: 'Fetch.ai', performance: 15.2, volume: ********0, signal: 'caution' as const },
                { symbol: 'OCEAN', name: 'Ocean Protocol', performance: 11.4, volume: 95000000, signal: 'caution' as const }
              ],
              outlook: 'AI tokens experiencing speculative rally but high volatility expected.'
            }
          ],
          opportunities: [
            {
              type: 'opportunity' as const,
              asset: { symbol: 'ETH', name: 'Ethereum', price: 2650.00, change: 4.2 },
              reason: 'ETF approval momentum, Staking yields',
              confidence: 89,
              aiExplanation: 'Ethereum benefiting from spot ETF approval expectations. Strong staking yields and upcoming protocol upgrades provide fundamental support. Technical breakout above $2600 resistance.'
            },
            {
              type: 'opportunity' as const,
              asset: { symbol: 'SOL', name: 'Solana', price: 98.75, change: 8.5 },
              reason: 'Ecosystem growth, High throughput',
              confidence: 85,
              aiExplanation: 'Solana ecosystem expanding rapidly with new DeFi and NFT projects. High transaction throughput and low fees attracting developers. Strong momentum above $90 support.'
            },
            {
              type: 'opportunity' as const,
              asset: { symbol: 'MATIC', name: 'Polygon', price: 0.89, change: 6.2 },
              reason: 'Ethereum scaling, Enterprise adoption',
              confidence: 81,
              aiExplanation: 'Polygon leading Ethereum scaling solutions with major enterprise partnerships. zkEVM launch driving developer adoption and transaction volume growth.'
            }
          ],
          risks: [
            {
              type: 'risk' as const,
              asset: { symbol: 'DOGE', name: 'Dogecoin', price: 0.082, change: -8.5 },
              reason: 'Meme coin volatility, No utility',
              confidence: 92,
              aiExplanation: 'Dogecoin highly susceptible to social media sentiment swings. Lack of fundamental utility and high correlation with speculative trading makes it extremely risky.'
            },
            {
              type: 'risk' as const,
              asset: { symbol: 'SHIB', name: 'Shiba Inu', price: 0.0000089, change: -12.3 },
              reason: 'Speculative bubble, Regulatory risk',
              confidence: 90,
              aiExplanation: 'Shiba Inu showing signs of speculative exhaustion. Regulatory crackdown on meme coins and lack of real-world adoption create significant downside risk.'
            }
          ]
        };
      default:
        return { sectors: [], opportunities: [], risks: [] };
    }
  };

  const generateRegionSpecificQuestions = (region: 'india' | 'global' | 'crypto') => {
    const questionSets = {
      india: [
        {
          id: '1',
          question: 'Why is the IT sector outperforming despite global slowdown concerns?',
          category: 'sector' as const,
          complexity: 'intermediate' as const
        },
        {
          id: '2',
          question: 'Is this the right time to buy banking stocks after the recent correction?',
          category: 'sector' as const,
          complexity: 'beginner' as const
        },
        {
          id: '3',
          question: 'How will the RBI rate pause impact different sectors?',
          category: 'market' as const,
          complexity: 'advanced' as const
        },
        {
          id: '4',
          question: 'Which Indian stocks look oversold on technical indicators today?',
          category: 'stock' as const,
          complexity: 'intermediate' as const
        },
        {
          id: '5',
          question: 'How do FII flows affect Indian market sentiment?',
          category: 'market' as const,
          complexity: 'intermediate' as const
        },
        {
          id: '6',
          question: 'What is the impact of monsoon on agricultural and FMCG stocks?',
          category: 'sector' as const,
          complexity: 'beginner' as const
        },
        {
          id: '7',
          question: 'How does rupee depreciation benefit IT exporters?',
          category: 'sector' as const,
          complexity: 'intermediate' as const
        },
        {
          id: '8',
          question: 'Why are PSU banks outperforming private banks recently?',
          category: 'sector' as const,
          complexity: 'intermediate' as const
        },
        {
          id: '9',
          question: 'What role does India VIX play in market timing strategies?',
          category: 'technical' as const,
          complexity: 'advanced' as const
        },
        {
          id: '10',
          question: 'How do Budget announcements create sector rotation opportunities?',
          category: 'news' as const,
          complexity: 'advanced' as const
        }
      ],
      global: [
        {
          id: '1',
          question: 'How will Fed rate cuts impact different US sectors?',
          category: 'market',
          complexity: 'intermediate'
        },
        {
          id: '2',
          question: 'Why are tech stocks leading the current market rally?',
          category: 'sector',
          complexity: 'beginner'
        },
        {
          id: '3',
          question: 'What is the significance of the yield curve inversion?',
          category: 'market',
          complexity: 'advanced'
        },
        {
          id: '4',
          question: 'How do earnings revisions predict stock performance?',
          category: 'fundamental',
          complexity: 'intermediate'
        },
        {
          id: '5',
          question: 'What drives the performance of growth vs value stocks?',
          category: 'strategy',
          complexity: 'intermediate'
        },
        {
          id: '6',
          question: 'How do geopolitical tensions affect global markets?',
          category: 'news',
          complexity: 'beginner'
        },
        {
          id: '7',
          question: 'What is the impact of AI adoption on tech valuations?',
          category: 'sector',
          complexity: 'advanced'
        },
        {
          id: '8',
          question: 'How do oil prices affect energy sector investments?',
          category: 'sector',
          complexity: 'intermediate'
        },
        {
          id: '9',
          question: 'What role does the US Dollar play in global market movements?',
          category: 'market',
          complexity: 'intermediate'
        },
        {
          id: '10',
          question: 'How do you analyze healthcare stocks during regulatory changes?',
          category: 'sector',
          complexity: 'advanced'
        }
      ],
      crypto: [
        {
          id: '1',
          question: 'How will Bitcoin ETF approval impact crypto markets?',
          category: 'crypto',
          complexity: 'intermediate'
        },
        {
          id: '2',
          question: 'What drives Ethereum price movements beyond Bitcoin correlation?',
          category: 'crypto',
          complexity: 'intermediate'
        },
        {
          id: '3',
          question: 'How do you evaluate DeFi protocol fundamentals?',
          category: 'fundamental',
          complexity: 'advanced'
        },
        {
          id: '4',
          question: 'What is the significance of crypto market dominance ratios?',
          category: 'technical',
          complexity: 'intermediate'
        },
        {
          id: '5',
          question: 'How do regulatory announcements affect different crypto sectors?',
          category: 'news',
          complexity: 'beginner'
        },
        {
          id: '6',
          question: 'What role do stablecoins play in crypto market stability?',
          category: 'crypto',
          complexity: 'beginner'
        },
        {
          id: '7',
          question: 'How do you analyze Layer 1 blockchain competition?',
          category: 'fundamental',
          complexity: 'advanced'
        },
        {
          id: '8',
          question: 'What drives meme coin price volatility and sustainability?',
          category: 'crypto',
          complexity: 'intermediate'
        },
        {
          id: '9',
          question: 'How do institutional crypto adoptions impact market cycles?',
          category: 'market',
          complexity: 'advanced'
        },
        {
          id: '10',
          question: 'What are the key metrics for evaluating NFT and gaming tokens?',
          category: 'fundamental',
          complexity: 'advanced'
        }
      ]
    };

    return (questionSets[region] || questionSets.india) as Array<{
      id: string;
      question: string;
      category: 'market' | 'sector' | 'stock' | 'crypto' | 'technical' | 'fundamental' | 'news' | 'strategy';
      complexity: 'beginner' | 'intermediate' | 'advanced';
    }>;
  };

  const getNewsImpactsByRegion = (region: 'india' | 'global' | 'crypto') => {
    switch (region) {
      case 'india':
        return [
          {
            headline: 'RBI Maintains Repo Rate at 6.5%, Signals Pause in Rate Hike Cycle',
            summary: 'Reserve Bank of India keeps policy rates unchanged, citing balanced approach to inflation and growth.',
            sentiment: 'positive' as const,
            impactScore: 85,
            affectedAssets: ['HDFCBANK', 'ICICIBANK', 'SBIN', 'KOTAKBANK']
          },
          {
            headline: 'TCS Reports Strong Q3 Earnings, Beats Street Estimates',
            summary: 'Tata Consultancy Services posts 12% YoY revenue growth with improved margins and positive outlook.',
            sentiment: 'positive' as const,
            impactScore: 78,
            affectedAssets: ['TCS', 'INFY', 'WIPRO', 'HCLTECH']
          },
          {
            headline: 'FII Outflows Continue for Third Consecutive Week',
            summary: 'Foreign institutional investors pull out ₹8,500 crores from Indian equities amid global uncertainty.',
            sentiment: 'negative' as const,
            impactScore: 72,
            affectedAssets: ['NIFTY', 'SENSEX']
          }
        ];
      case 'global':
        return [
          {
            headline: 'Fed Signals Potential Rate Cuts in 2024, Markets Rally',
            summary: 'Federal Reserve hints at possible rate cuts if inflation continues to moderate, boosting market sentiment.',
            sentiment: 'positive' as const,
            impactScore: 88,
            affectedAssets: ['SPY', 'QQQ', 'JPM', 'AAPL']
          },
          {
            headline: 'Microsoft Reports Strong AI Revenue Growth',
            summary: 'Microsoft Azure AI services drive 25% revenue growth, beating analyst expectations significantly.',
            sentiment: 'positive' as const,
            impactScore: 82,
            affectedAssets: ['MSFT', 'GOOGL', 'NVDA']
          },
          {
            headline: 'China Economic Data Shows Continued Weakness',
            summary: 'Chinese manufacturing PMI falls below 50, raising concerns about global growth outlook.',
            sentiment: 'negative' as const,
            impactScore: 75,
            affectedAssets: ['AAPL', 'TSLA', 'NKE']
          }
        ];
      case 'crypto':
        return [
          {
            headline: 'Bitcoin ETF Approval Odds Rise to 90%, Analysts Say',
            summary: 'Multiple sources suggest SEC approval for spot Bitcoin ETF is imminent, driving institutional interest.',
            sentiment: 'positive' as const,
            impactScore: 92,
            affectedAssets: ['BTC', 'ETH', 'COIN', 'MSTR']
          },
          {
            headline: 'Ethereum Staking Yields Hit 5.2% as Network Upgrades',
            summary: 'Ethereum staking rewards increase following successful network upgrades, attracting more validators.',
            sentiment: 'positive' as const,
            impactScore: 79,
            affectedAssets: ['ETH', 'LDO', 'RPL']
          },
          {
            headline: 'Regulatory Clarity Emerges for DeFi Protocols',
            summary: 'New regulatory framework provides clearer guidelines for DeFi operations, reducing uncertainty.',
            sentiment: 'positive' as const,
            impactScore: 84,
            affectedAssets: ['UNI', 'AAVE', 'COMP']
          }
        ];
      default:
        return [];
    }
  };

  const generateMockInsights = (region: 'india' | 'global' | 'crypto' = activeRegion): MarketInsights => {
    const regionData = getRegionSpecificData(region);

    const getSentimentByRegion = (region: string) => {
      switch (region) {
        case 'india': return { overall: 'bullish' as const, confidence: 75, factors: ['Strong earnings growth', 'Positive FII flows', 'Sector rotation'] };
        case 'global': return { overall: 'neutral' as const, confidence: 68, factors: ['Fed policy uncertainty', 'Mixed earnings', 'Geopolitical tensions'] };
        case 'crypto': return { overall: 'bullish' as const, confidence: 82, factors: ['ETF optimism', 'Institutional adoption', 'Technical breakouts'] };
        default: return { overall: 'neutral' as const, confidence: 50, factors: ['Mixed signals'] };
      }
    };

    return {
    timestamp: new Date().toISOString(),
    region: region,
    marketSentiment: getSentimentByRegion(region),
    sectorAnalysis: regionData.sectors,
    opportunityRadar: [...regionData.opportunities, ...regionData.risks],
    newsImpacts: getNewsImpactsByRegion(region),
    insightQuestions: generateRegionSpecificQuestions(region),
    topMovers: [
      {
        symbol: region === 'india' ? 'TCS' : region === 'global' ? 'AAPL' : 'BTC',
        name: region === 'india' ? 'Tata Consultancy Services' : region === 'global' ? 'Apple Inc' : 'Bitcoin',
        price: region === 'india' ? 3567.25 : region === 'global' ? 178.85 : 43250.00,
        changePercent: 3.2,
        volume: region === 'crypto' ? 28000000000 : 1800000
      },
      {
        symbol: region === 'india' ? 'INFY' : region === 'global' ? 'MSFT' : 'ETH',
        name: region === 'india' ? 'Infosys Ltd' : region === 'global' ? 'Microsoft Corp' : 'Ethereum',
        price: region === 'india' ? 1456.80 : region === 'global' ? 378.85 : 2650.00,
        changePercent: 1.8,
        volume: region === 'crypto' ? 15000000000 : 2100000
      },
      {
        symbol: region === 'india' ? 'WIPRO' : region === 'global' ? 'GOOGL' : 'SOL',
        name: region === 'india' ? 'Wipro Ltd' : region === 'global' ? 'Alphabet Inc' : 'Solana',
        price: region === 'india' ? 445.60 : region === 'global' ? 142.56 : 98.75,
        changePercent: 2.1,
        volume: region === 'crypto' ? 2500000000 : 1500000
      }
    ],
    regionalSummaries: [
      {
        region: region,
        summary: `${region.charAt(0).toUpperCase() + region.slice(1)} markets showing positive momentum with strong sector performance and increased trading activity.`,
        indices: [
          {
            name: region === 'india' ? 'Nifty 50' : region === 'global' ? 'S&P 500' : 'Total Market Cap',
            changePercent: 0.85
          },
          {
            name: region === 'india' ? 'Sensex' : region === 'global' ? 'Nasdaq' : 'DeFi TVL',
            changePercent: 0.92
          },
          {
            name: region === 'india' ? 'Bank Nifty' : region === 'global' ? 'Dow Jones' : 'Bitcoin Dominance',
            changePercent: -0.45
          }
        ],
        topMovers: [
          {
            symbol: region === 'india' ? 'TCS' : region === 'global' ? 'AAPL' : 'BTC',
            name: region === 'india' ? 'Tata Consultancy Services' : region === 'global' ? 'Apple Inc' : 'Bitcoin',
            changePercent: 3.2
          },
          {
            symbol: region === 'india' ? 'INFY' : region === 'global' ? 'MSFT' : 'ETH',
            name: region === 'india' ? 'Infosys Ltd' : region === 'global' ? 'Microsoft Corp' : 'Ethereum',
            changePercent: 1.8
          },
          {
            symbol: region === 'india' ? 'WIPRO' : region === 'global' ? 'GOOGL' : 'SOL',
            name: region === 'india' ? 'Wipro Ltd' : region === 'global' ? 'Alphabet Inc' : 'Solana',
            changePercent: 2.1
          }
        ]
      }
    ]
  };
};

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'bullish': return 'text-green-400';
      case 'bearish': return 'text-red-400';
      default: return 'text-yellow-400';
    }
  };

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'bullish': return <TrendingUp className="h-5 w-5" />;
      case 'bearish': return <TrendingDown className="h-5 w-5" />;
      default: return <Activity className="h-5 w-5" />;
    }
  };

  const getPerformanceIcon = (performance: number) => {
    if (performance > 0) return <ArrowUpRight className="h-4 w-4 text-green-400" />;
    if (performance < 0) return <ArrowDownRight className="h-4 w-4 text-red-400" />;
    return <Minus className="h-4 w-4 text-gray-400" />;
  };

  const handleAIAnalysis = (question: any) => {
    // Create URL with question parameters
    const params = new URLSearchParams({
      question: question.question,
      category: question.category,
      complexity: question.complexity,
      region: activeRegion
    });

    // Open in new tab
    const url = `/ai-chat?${params.toString()}`;
    window.open(url, '_blank');
  };

  if (loading && !insights) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-400 mx-auto mb-4" />
          <p className="text-slate-300">Generating AI-powered market insights...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900 text-white">
      {/* Header */}
      <div className="border-b border-slate-800 bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white flex items-center gap-2">
                <Brain className="h-6 w-6 text-blue-400" />
                AI Market Insights Engine
              </h1>
              <p className="text-slate-400 text-sm">
                Real-time analysis across Indian equities, global markets, and cryptocurrencies
              </p>
            </div>

            <div className="flex items-center gap-4">
              {/* Region Toggle */}
              <div className="flex items-center gap-1 bg-slate-800 rounded-lg p-1">
                <Button
                  onClick={() => {
                    setActiveRegion('india');
                    setInsights(generateMockInsights('india'));
                  }}
                  variant={activeRegion === 'india' ? 'default' : 'ghost'}
                  size="sm"
                  className={`text-xs ${activeRegion === 'india'
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'text-slate-300 hover:bg-slate-700'
                  }`}
                >
                  🇮🇳 India
                </Button>
                <Button
                  onClick={() => {
                    setActiveRegion('global');
                    setInsights(generateMockInsights('global'));
                  }}
                  variant={activeRegion === 'global' ? 'default' : 'ghost'}
                  size="sm"
                  className={`text-xs ${activeRegion === 'global'
                    ? 'bg-purple-600 text-white hover:bg-purple-700'
                    : 'text-slate-300 hover:bg-slate-700'
                  }`}
                >
                  🌍 Global
                </Button>
                <Button
                  onClick={() => {
                    setActiveRegion('crypto');
                    setInsights(generateMockInsights('crypto'));
                  }}
                  variant={activeRegion === 'crypto' ? 'default' : 'ghost'}
                  size="sm"
                  className={`text-xs ${activeRegion === 'crypto'
                    ? 'bg-orange-600 text-white hover:bg-orange-700'
                    : 'text-slate-300 hover:bg-slate-700'
                  }`}
                >
                  ₿ Crypto
                </Button>
              </div>

              {lastUpdated && (
                <div className="text-xs text-slate-400 flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  Updated {lastUpdated.toLocaleTimeString()}
                </div>
              )}

              <Button
                onClick={() => fetchMarketInsights(true)}
                disabled={loading}
                variant="outline"
                size="sm"
                className="border-slate-600 text-slate-300 hover:bg-slate-700 hover:border-slate-500"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-6 space-y-6">
        {/* Market Sentiment Overview */}
        {insights?.marketSentiment && (
          <Card className={`border-2 ${
            activeRegion === 'india' ? 'bg-gradient-to-r from-blue-900/20 to-slate-800 border-blue-700/50' :
            activeRegion === 'global' ? 'bg-gradient-to-r from-purple-900/20 to-slate-800 border-purple-700/50' :
            'bg-gradient-to-r from-orange-900/20 to-slate-800 border-orange-700/50'
          }`}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className={`h-5 w-5 ${
                  activeRegion === 'india' ? 'text-blue-400' :
                  activeRegion === 'global' ? 'text-purple-400' :
                  'text-orange-400'
                }`} />
                <span className="capitalize">{activeRegion}</span> Market Sentiment
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className={`flex items-center gap-2 ${getSentimentColor(insights.marketSentiment.overall)}`}>
                    {getSentimentIcon(insights.marketSentiment.overall)}
                    <span className="text-2xl font-bold capitalize">
                      {insights.marketSentiment.overall}
                    </span>
                  </div>
                  <Badge
                    variant="outline"
                    className={`border-2 font-semibold ${
                      insights.marketSentiment.confidence >= 80 ? 'border-green-500 text-green-400' :
                      insights.marketSentiment.confidence >= 60 ? 'border-yellow-500 text-yellow-400' :
                      'border-red-500 text-red-400'
                    }`}
                  >
                    {insights.marketSentiment.confidence}% Confidence
                  </Badge>
                </div>

                <div className="text-right">
                  <p className="text-sm text-slate-400 mb-2 font-medium">Key Factors:</p>
                  <div className="flex flex-wrap gap-1 justify-end">
                    {insights.marketSentiment.factors.map((factor, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className={`text-xs font-medium ${
                          activeRegion === 'india' ? 'bg-blue-900/30 text-blue-300 border-blue-700' :
                          activeRegion === 'global' ? 'bg-purple-900/30 text-purple-300 border-purple-700' :
                          'bg-orange-900/30 text-orange-300 border-orange-700'
                        }`}
                      >
                        {factor}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content Tabs */}
        <Tabs defaultValue="sectors" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5 bg-slate-800 border-slate-700">
            <TabsTrigger value="sectors" className="data-[state=active]:bg-slate-700">
              Sector Analysis
            </TabsTrigger>
            <TabsTrigger value="opportunities" className="data-[state=active]:bg-slate-700">
              Opportunity Radar
            </TabsTrigger>
            <TabsTrigger value="news" className="data-[state=active]:bg-slate-700">
              News Impact
            </TabsTrigger>
            <TabsTrigger value="ai-insights" className="data-[state=active]:bg-slate-700">
              AI Questions
            </TabsTrigger>
            <TabsTrigger value="regional" className="data-[state=active]:bg-slate-700">
              Regional View
            </TabsTrigger>
          </TabsList>

          {/* Sector Analysis Tab */}
          <TabsContent value="sectors" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {insights?.sectorAnalysis?.map((sector, index) => (
                <Card key={index} className={`border-2 transition-all duration-300 hover:shadow-lg ${
                  sector.performance > 2 ? 'bg-gradient-to-br from-green-900/20 to-slate-800 border-green-700/50 hover:border-green-600' :
                  sector.performance > 0 ? 'bg-gradient-to-br from-blue-900/20 to-slate-800 border-blue-700/50 hover:border-blue-600' :
                  sector.performance > -2 ? 'bg-gradient-to-br from-yellow-900/20 to-slate-800 border-yellow-700/50 hover:border-yellow-600' :
                  'bg-gradient-to-br from-red-900/20 to-slate-800 border-red-700/50 hover:border-red-600'
                }`}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="text-lg font-bold">{sector.sector}</span>
                      <div className="flex items-center gap-2">
                        {getPerformanceIcon(sector.performance)}
                        <Badge
                          variant={sector.performance > 0 ? "default" : "destructive"}
                          className={`font-bold text-sm ${
                            sector.performance > 2 ? "bg-green-600 hover:bg-green-700" :
                            sector.performance > 0 ? "bg-blue-600 hover:bg-blue-700" :
                            sector.performance > -2 ? "bg-yellow-600 hover:bg-yellow-700" :
                            "bg-red-600 hover:bg-red-700"
                          }`}
                        >
                          {sector.performance > 0 ? '+' : ''}{sector.performance.toFixed(2)}%
                        </Badge>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-slate-300 font-medium leading-relaxed">{sector.outlook}</p>

                    <div className="space-y-3">
                      <h4 className="text-sm font-bold text-slate-200 border-b border-slate-600 pb-1">Top Performers:</h4>
                      {sector.topStocks?.slice(0, 3).map((stock, stockIndex) => (
                        <div key={stockIndex} className={`flex justify-between items-center p-3 rounded-lg border transition-all duration-200 hover:scale-[1.02] ${
                          stock.signal === 'opportunity' ? 'bg-green-900/20 border-green-700/50 hover:border-green-600' :
                          stock.signal === 'caution' ? 'bg-red-900/20 border-red-700/50 hover:border-red-600' :
                          'bg-slate-700/30 border-slate-600/50 hover:border-slate-500'
                        }`}>
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${
                              stock.signal === 'opportunity' ? 'bg-green-400' :
                              stock.signal === 'caution' ? 'bg-red-400' :
                              'bg-gray-400'
                            }`}></div>
                            <div>
                              <span className="text-sm font-bold text-white">{stock.symbol}</span>
                              <p className="text-xs text-slate-400">{stock.name}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <span className={`text-sm font-bold ${stock.performance > 0 ? 'text-green-400' : 'text-red-400'}`}>
                              {stock.performance > 0 ? '+' : ''}{stock.performance.toFixed(2)}%
                            </span>
                            <p className="text-xs text-slate-400">
                              Vol: {activeRegion === 'crypto' ?
                                `$${(stock.volume / 1000000000).toFixed(1)}B` :
                                `${(stock.volume / 1000000).toFixed(1)}M`
                              }
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Opportunity Radar Tab */}
          <TabsContent value="opportunities" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Opportunities */}
              <Card className="bg-gradient-to-br from-green-900/20 to-slate-800 border-2 border-green-700/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-green-400">
                    <Target className="h-6 w-6" />
                    High Opportunity Assets
                  </CardTitle>
                  <CardDescription className="text-slate-300 font-medium">
                    AI-identified opportunities based on technical and fundamental analysis
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {insights?.opportunityRadar?.filter(item => item.type === 'opportunity').length === 0 ? (
                    <div className="text-center py-8 text-slate-400">
                      <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No high-opportunity assets identified today</p>
                      <p className="text-xs mt-2">Check back later for new opportunities</p>
                    </div>
                  ) : (
                    insights?.opportunityRadar?.filter(item => item.type === 'opportunity').map((opportunity, index) => (
                      <div key={index} className="border-2 border-green-700/30 bg-green-900/10 rounded-lg p-4 space-y-3 hover:border-green-600/50 transition-all duration-300">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-bold text-lg text-white">{opportunity.asset.name}</h4>
                            <p className="text-sm text-slate-300 font-medium">
                              {opportunity.asset.symbol} • {activeRegion === 'crypto' ? '$' : '₹'}{opportunity.asset.price?.toFixed(2) || 'N/A'}
                              <span className={`ml-2 ${(opportunity.asset.change || 0) > 0 ? 'text-green-400' : 'text-red-400'}`}>
                                ({(opportunity.asset.change || 0) > 0 ? '+' : ''}{opportunity.asset.change?.toFixed(2) || '0.00'}%)
                              </span>
                            </p>
                          </div>
                          <Badge className="bg-green-600 hover:bg-green-700 font-bold text-sm px-3 py-1">
                            {opportunity.confidence}% Confidence
                          </Badge>
                        </div>

                        <div className="space-y-3">
                          <div className="bg-green-900/20 border border-green-700/30 rounded p-3">
                            <p className="text-sm font-bold text-green-300 mb-1">Signal:</p>
                            <p className="text-sm text-green-400 font-medium">{opportunity.reason}</p>
                          </div>
                          <div className="bg-slate-700/30 border border-slate-600/30 rounded p-3">
                            <p className="text-sm font-bold text-slate-300 mb-1">AI Analysis:</p>
                            <p className="text-sm text-slate-300 leading-relaxed">{opportunity.aiExplanation}</p>
                          </div>
                        </div>

                        <Button variant="outline" size="sm" className="w-full border-2 border-green-600 text-green-400 hover:bg-green-600 hover:text-white font-medium transition-all duration-200">
                          <Eye className="h-4 w-4 mr-2" />
                          View Detailed Analysis
                        </Button>
                      </div>
                    ))
                  )}
                </CardContent>
              </Card>

              {/* Risks */}
              <Card className="bg-gradient-to-br from-red-900/20 to-slate-800 border-2 border-red-700/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-400">
                    <AlertTriangle className="h-6 w-6" />
                    High Risk Assets
                  </CardTitle>
                  <CardDescription className="text-slate-300 font-medium">
                    Assets flagged for elevated risk factors requiring caution
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {insights?.opportunityRadar?.filter(item => item.type === 'risk').length === 0 ? (
                    <div className="text-center py-8 text-slate-400">
                      <AlertTriangle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No high-risk assets flagged today</p>
                      <p className="text-xs mt-2">Market conditions appear stable</p>
                    </div>
                  ) : (
                    insights?.opportunityRadar?.filter(item => item.type === 'risk').map((risk, index) => (
                      <div key={index} className="border-2 border-red-700/30 bg-red-900/10 rounded-lg p-4 space-y-3 hover:border-red-600/50 transition-all duration-300">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-bold text-lg text-white">{risk.asset.name}</h4>
                            <p className="text-sm text-slate-300 font-medium">
                              {risk.asset.symbol} • {activeRegion === 'crypto' ? '$' : '₹'}{risk.asset.price?.toFixed(2) || 'N/A'}
                              <span className={`ml-2 ${(risk.asset.change || 0) > 0 ? 'text-green-400' : 'text-red-400'}`}>
                                ({(risk.asset.change || 0) > 0 ? '+' : ''}{risk.asset.change?.toFixed(2) || '0.00'}%)
                              </span>
                            </p>
                          </div>
                          <Badge variant="destructive" className="bg-red-600 hover:bg-red-700 font-bold text-sm px-3 py-1">
                            {risk.confidence}% Risk
                          </Badge>
                        </div>

                        <div className="space-y-3">
                          <div className="bg-red-900/20 border border-red-700/30 rounded p-3">
                            <p className="text-sm font-bold text-red-300 mb-1">Risk Factors:</p>
                            <p className="text-sm text-red-400 font-medium">{risk.reason}</p>
                          </div>
                          <div className="bg-slate-700/30 border border-slate-600/30 rounded p-3">
                            <p className="text-sm font-bold text-slate-300 mb-1">AI Analysis:</p>
                            <p className="text-sm text-slate-300 leading-relaxed">{risk.aiExplanation}</p>
                          </div>
                        </div>

                        <Button variant="outline" size="sm" className="w-full border-2 border-red-600 text-red-400 hover:bg-red-600 hover:text-white font-medium transition-all duration-200">
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          View Risk Assessment
                        </Button>
                      </div>
                    ))
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* News Impact Tab */}
          <TabsContent value="news" className="space-y-6">
            <div className="space-y-4">
              {insights?.newsImpacts?.map((news, index) => (
                <Card key={index} className="bg-slate-800 border-slate-700">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Newspaper className="h-5 w-5 text-blue-400" />
                      {news.headline}
                    </CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <Badge variant={news.sentiment === 'positive' ? 'default' : news.sentiment === 'negative' ? 'destructive' : 'secondary'}>
                        {news.sentiment.toUpperCase()}
                      </Badge>
                      <Badge variant="outline">Impact Score: {news.impactScore}/100</Badge>
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-slate-300 mb-4">{news.summary}</p>
                    
                    {news.affectedAssets?.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium mb-2 text-slate-400">Affected Assets:</h4>
                        <div className="flex flex-wrap gap-1">
                          {news.affectedAssets.map((asset, assetIndex) => (
                            <Badge key={assetIndex} variant="outline" className="text-xs border-slate-600">
                              {asset}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* AI Insights Tab */}
          <TabsContent value="ai-insights" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {insights?.insightQuestions?.map((question, index) => (
                <Card key={index} className="bg-slate-800 border-slate-700 cursor-pointer hover:bg-slate-750 transition-colors">
                  <CardHeader>
                    <CardTitle className="text-lg">{question.question}</CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <Badge variant="outline" className="border-slate-600">
                        {question.category.toUpperCase()}
                      </Badge>
                      <Badge variant="secondary">
                        {question.complexity.toUpperCase()}
                      </Badge>
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                      onClick={() => handleAIAnalysis(question)}
                    >
                      <Brain className="h-4 w-4 mr-2" />
                      Get AI Analysis
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Regional View Tab */}
          <TabsContent value="regional" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Market Indices */}
              <Card className={`border-2 ${
                activeRegion === 'india' ? 'bg-gradient-to-br from-blue-900/20 to-slate-800 border-blue-700/50' :
                activeRegion === 'global' ? 'bg-gradient-to-br from-purple-900/20 to-slate-800 border-purple-700/50' :
                'bg-gradient-to-br from-orange-900/20 to-slate-800 border-orange-700/50'
              }`}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className={`h-5 w-5 ${
                      activeRegion === 'india' ? 'text-blue-400' :
                      activeRegion === 'global' ? 'text-purple-400' :
                      'text-orange-400'
                    }`} />
                    {activeRegion === 'india' ? '🇮🇳 Indian Indices' :
                     activeRegion === 'global' ? '🌍 Global Indices' :
                     '₿ Crypto Market Metrics'}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {insights?.regionalSummaries?.[0]?.indices?.map((index, idx) => (
                      <div key={idx} className={`flex justify-between items-center p-3 rounded-lg border ${
                        index.changePercent > 0 ? 'bg-green-900/20 border-green-700/30' :
                        index.changePercent < 0 ? 'bg-red-900/20 border-red-700/30' :
                        'bg-slate-700/30 border-slate-600/30'
                      }`}>
                        <div>
                          <span className="text-sm font-bold text-white">{index.name}</span>
                          <p className="text-xs text-slate-400">
                            {activeRegion === 'crypto' ? 'Market Metric' : 'Index'}
                          </p>
                        </div>
                        <span className={`text-sm font-bold ${
                          index.changePercent > 0 ? 'text-green-400' :
                          index.changePercent < 0 ? 'text-red-400' :
                          'text-slate-400'
                        }`}>
                          {index.changePercent > 0 ? '+' : ''}{index.changePercent.toFixed(2)}%
                        </span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Top Movers */}
              <Card className={`border-2 ${
                activeRegion === 'india' ? 'bg-gradient-to-br from-blue-900/20 to-slate-800 border-blue-700/50' :
                activeRegion === 'global' ? 'bg-gradient-to-br from-purple-900/20 to-slate-800 border-purple-700/50' :
                'bg-gradient-to-br from-orange-900/20 to-slate-800 border-orange-700/50'
              }`}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className={`h-5 w-5 ${
                      activeRegion === 'india' ? 'text-blue-400' :
                      activeRegion === 'global' ? 'text-purple-400' :
                      'text-orange-400'
                    }`} />
                    Top 3 Momentum {activeRegion === 'crypto' ? 'Assets' : 'Stocks'}
                  </CardTitle>
                  <CardDescription className="text-slate-300">
                    Highest volume and price movement today
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {insights?.topMovers?.slice(0, 3).map((mover, idx) => (
                      <div key={idx} className={`flex justify-between items-center p-3 rounded-lg border transition-all duration-200 hover:scale-[1.02] ${
                        mover.changePercent > 0 ? 'bg-green-900/20 border-green-700/30 hover:border-green-600' :
                        'bg-red-900/20 border-red-700/30 hover:border-red-600'
                      }`}>
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${
                            mover.changePercent > 0 ? 'bg-green-400' : 'bg-red-400'
                          }`}></div>
                          <div>
                            <span className="text-sm font-bold text-white">
                              {mover.symbol.replace('.NS', '').replace('-USD', '')}
                            </span>
                            <p className="text-xs text-slate-400 max-w-[150px] truncate">
                              {mover.name}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <span className={`text-sm font-bold ${
                            (mover.changePercent || 0) > 0 ? 'text-green-400' : 'text-red-400'
                          }`}>
                            {(mover.changePercent || 0) > 0 ? '+' : ''}{mover.changePercent?.toFixed(2) || '0.00'}%
                          </span>
                          <p className="text-xs text-slate-400">
                            {activeRegion === 'crypto' ?
                              `$${mover.price?.toFixed(2) || 'N/A'}` :
                              activeRegion === 'india' ?
                                `₹${mover.price?.toFixed(2) || 'N/A'}` :
                                `$${mover.price?.toFixed(2) || 'N/A'}`
                            }
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Market Summary */}
            <Card className={`border-2 ${
              activeRegion === 'india' ? 'bg-gradient-to-r from-blue-900/20 to-slate-800 border-blue-700/50' :
              activeRegion === 'global' ? 'bg-gradient-to-r from-purple-900/20 to-slate-800 border-purple-700/50' :
              'bg-gradient-to-r from-orange-900/20 to-slate-800 border-orange-700/50'
            }`}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className={`h-5 w-5 ${
                    activeRegion === 'india' ? 'text-blue-400' :
                    activeRegion === 'global' ? 'text-purple-400' :
                    'text-orange-400'
                  }`} />
                  {activeRegion.charAt(0).toUpperCase() + activeRegion.slice(1)} Market Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-300 leading-relaxed">
                  {insights?.regionalSummaries?.[0]?.summary ||
                   `${activeRegion.charAt(0).toUpperCase() + activeRegion.slice(1)} markets showing mixed signals with ongoing analysis of sector performance and individual stock movements.`
                  }
                </p>

                <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-slate-700/30 rounded-lg">
                    <p className="text-xs text-slate-400 mb-1">Sectors Analyzed</p>
                    <p className="text-lg font-bold text-white">{insights?.sectorAnalysis?.length || 0}</p>
                  </div>
                  <div className="text-center p-3 bg-slate-700/30 rounded-lg">
                    <p className="text-xs text-slate-400 mb-1">News Articles</p>
                    <p className="text-lg font-bold text-white">{insights?.newsImpacts?.length || 0}</p>
                  </div>
                  <div className="text-center p-3 bg-slate-700/30 rounded-lg">
                    <p className="text-xs text-slate-400 mb-1">AI Questions</p>
                    <p className="text-lg font-bold text-white">{insights?.insightQuestions?.length || 0}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
