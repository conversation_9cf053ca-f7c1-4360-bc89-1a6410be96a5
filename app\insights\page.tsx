"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  TrendingUp,
  TrendingDown,
  Activity,
  Globe,
  DollarSign,
  Brain,
  RefreshCw,
  Filter,
  Clock,
  AlertTriangle,
  Target,
  Newspaper,
  BarChart3,
  PieChart,
  LineChart,
  Lightbulb
} from 'lucide-react';
import { LineChart as RechartsLineChart, Line, ResponsiveContainer, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';

interface MarketData {
  symbol: string;
  price: number;
  change: number;
  changePercent: number;
}

interface Insight {
  id: string;
  type: 'market' | 'personal' | 'educational' | 'opportunity';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  timestamp: Date;
  actionable?: string[];
}

export default function InsightsPage() {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [insights, setInsights] = useState<Insight[]>([]);
  const [marketData, setMarketData] = useState<MarketData[]>([]);
  const [selectedInsightType, setSelectedInsightType] = useState('all');

  useEffect(() => {
    // Update time every minute
    const timer = setInterval(() => setCurrentTime(new Date()), 60000);
    
    // Generate sample insights
    generateInsights();
    generateMarketData();
    
    return () => clearInterval(timer);
  }, []);

  const generateInsights = () => {
    const sampleInsights: Insight[] = [
      {
        id: '1',
        type: 'market',
        title: 'Federal Reserve Interest Rate Decision',
        description: 'The Fed is expected to maintain current rates, which could benefit bond portfolios and stable dividend stocks.',
        impact: 'high',
        timestamp: new Date(),
        actionable: ['Review fixed-income allocation', 'Consider dividend aristocrats', 'Monitor bond duration']
      },
      {
        id: '2',
        type: 'personal',
        title: 'Portfolio Rebalancing Opportunity',
        description: 'Your equity allocation has drifted 8% above target due to recent market gains. Consider rebalancing.',
        impact: 'medium',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        actionable: ['Sell 5% equity positions', 'Increase bond allocation', 'Review target allocation']
      },
      {
        id: '3',
        type: 'educational',
        title: 'Tax-Loss Harvesting Season',
        description: 'Year-end approaches - review underperforming positions for potential tax-loss harvesting opportunities.',
        impact: 'medium',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        actionable: ['Identify unrealized losses', 'Plan wash sale avoidance', 'Consult tax advisor']
      },
      {
        id: '4',
        type: 'opportunity',
        title: 'High-Yield Savings Rate Alert',
        description: 'Several banks have increased savings rates to 5.2%. Your emergency fund could earn more.',
        impact: 'low',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
        actionable: ['Compare current rate', 'Research bank options', 'Transfer emergency fund']
      }
    ];
    
    setInsights(sampleInsights);
  };

  const generateMarketData = () => {
    const markets: MarketData[] = [
      { symbol: 'S&P 500', price: 4732.85, change: 23.45, changePercent: 0.50 },
      { symbol: 'NASDAQ', price: 14845.50, change: -15.23, changePercent: -0.10 },
      { symbol: 'DOW', price: 37123.25, change: 145.67, changePercent: 0.39 },
      { symbol: 'Gold', price: 2034.50, change: 12.30, changePercent: 0.61 },
      { symbol: 'Bitcoin', price: 42850.00, change: -523.45, changePercent: -1.20 },
      { symbol: '10Y Treasury', price: 4.42, change: 0.05, changePercent: 1.14 }
    ];
    
    setMarketData(markets);
  };

  const chartData = [
    { time: '9:30', sp500: 4710, nasdaq: 14820, dow: 37080 },
    { time: '10:00', sp500: 4715, nasdaq: 14835, dow: 37095 },
    { time: '10:30', sp500: 4720, nasdaq: 14840, dow: 37105 },
    { time: '11:00', sp500: 4725, nasdaq: 14845, dow: 37115 },
    { time: '11:30', sp500: 4730, nasdaq: 14850, dow: 37120 },
    { time: '12:00', sp500: 4733, nasdaq: 14845, dow: 37123 }
  ];

  const filteredInsights = insights.filter(insight => 
    selectedInsightType === 'all' || insight.type === selectedInsightType
  );

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'market': return <Globe className="h-4 w-4" />;
      case 'personal': return <Target className="h-4 w-4" />;
      case 'educational': return <Lightbulb className="h-4 w-4" />;
      case 'opportunity': return <TrendingUp className="h-4 w-4" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'border-red-500/30 text-red-300 bg-red-500/10';
      case 'medium': return 'border-yellow-500/30 text-yellow-300 bg-yellow-500/10';
      case 'low': return 'border-green-500/30 text-green-300 bg-green-500/10';
      default: return 'border-slate-500/30 text-slate-300 bg-slate-500/10';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">
          
          {/* Header */}
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold text-white">Market Insights</h1>
              <p className="text-slate-300">Real-time market data and personalized financial insights</p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-green-500/10 text-green-300 border-green-500/30">
                <Clock className="h-3 w-3 mr-1" />
                Live: {currentTime.toLocaleTimeString()}
              </Badge>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => {
                  generateInsights();
                  generateMarketData();
                }}
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Market Overview */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {marketData.map((market, index) => (
              <Card key={index} className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-slate-300">{market.symbol}</p>
                    <p className="text-lg font-bold text-white">
                      {market.symbol.includes('Treasury') ? `${market.price}%` : `$${market.price.toLocaleString()}`}
                    </p>
                    <div className={`flex items-center gap-1 text-sm ${
                      market.change >= 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {market.change >= 0 ? (
                        <TrendingUp className="h-3 w-3" />
                      ) : (
                        <TrendingDown className="h-3 w-3" />
                      )}
                      <span>{market.change >= 0 ? '+' : ''}{market.change}</span>
                      <span>({market.changePercent >= 0 ? '+' : ''}{market.changePercent}%)</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Market Chart */}
          <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-400" />
                Intraday Performance
              </CardTitle>
              <CardDescription className="text-slate-400">
                Major indices performance throughout the trading day
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RechartsLineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                  <XAxis dataKey="time" stroke="#9CA3AF" />
                  <YAxis stroke="#9CA3AF" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: '#1F2937', 
                      border: '1px solid #374151',
                      borderRadius: '8px'
                    }} 
                  />
                  <Line type="monotone" dataKey="sp500" stroke="#3B82F6" strokeWidth={2} name="S&P 500" />
                  <Line type="monotone" dataKey="nasdaq" stroke="#10B981" strokeWidth={2} name="NASDAQ" />
                  <Line type="monotone" dataKey="dow" stroke="#F59E0B" strokeWidth={2} name="DOW" />
                </RechartsLineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Insights Section */}
          <Tabs defaultValue="all" className="space-y-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
              <TabsList className="bg-slate-800 border-slate-700">
                <TabsTrigger 
                  value="all"
                  onClick={() => setSelectedInsightType('all')}
                  className="data-[state=active]:bg-slate-700"
                >
                  All Insights
                </TabsTrigger>
                <TabsTrigger 
                  value="market"
                  onClick={() => setSelectedInsightType('market')}
                  className="data-[state=active]:bg-slate-700"
                >
                  Market
                </TabsTrigger>
                <TabsTrigger 
                  value="personal"
                  onClick={() => setSelectedInsightType('personal')}
                  className="data-[state=active]:bg-slate-700"
                >
                  Personal
                </TabsTrigger>
                <TabsTrigger 
                  value="educational"
                  onClick={() => setSelectedInsightType('educational')}
                  className="data-[state=active]:bg-slate-700"
                >
                  Educational
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="all" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {filteredInsights.map((insight) => (
                  <Card key={insight.id} className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getInsightIcon(insight.type)}
                          <span className="text-sm font-medium text-slate-400 capitalize">
                            {insight.type}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className={getImpactColor(insight.impact)}>
                            {insight.impact} impact
                          </Badge>
                          <span className="text-xs text-slate-500">
                            {insight.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                      </div>
                      <CardTitle className="text-white text-lg">{insight.title}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-slate-300">{insight.description}</p>
                      
                      {insight.actionable && insight.actionable.length > 0 && (
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-slate-400">Recommended Actions:</p>
                          <ul className="space-y-1">
                            {insight.actionable.map((action, index) => (
                              <li key={index} className="text-sm text-slate-300 flex items-center gap-2">
                                <div className="w-1 h-1 bg-blue-400 rounded-full" />
                                {action}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}