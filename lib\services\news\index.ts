// Main news service exports

export * from './types';
export * from './fetch';
export * from './summarize';
export * from './search';

// Re-export main services for convenience
export { newsFetchService } from './fetch';
export { newsSummarizationService } from './summarize';
export { newsSearchService } from './search';

// Combined news service class for easier usage
import { newsFetchService } from './fetch';
import { newsSummarizationService } from './summarize';
import { newsSearchService } from './search';
import { NewsArticle, NewsCategory, NewsResponse, SearchFilters } from './types';

class NewsService {
  /**
   * Get news by category with summaries
   */
  async getNewsByCategory(category: NewsCategory, includeSummaries = false): Promise<NewsResponse> {
    const response = await newsFetchService.fetchNewsByCategory(category);

    if (response.status === 'ok' && includeSummaries && response.articles.length > 0) {
      try {
        // Limit to first 5 articles for summarization to avoid rate limits
        const articlesToSummarize = response.articles.slice(0, 5);
        const summaries = await newsSummarizationService.summarizeArticles(articlesToSummarize);

        // Merge summaries with articles
        response.articles = response.articles.map((article, index) => {
          if (index < 5 && summaries[index]) {
            return {
              ...article,
              summary: summaries[index].summary,
              sentiment: summaries[index].sentiment,
              tags: summaries[index].tags
            };
          }
          return article;
        });
      } catch (error) {
        console.warn('AI summarization unavailable, continuing with basic news:', error);
        // Continue without summaries - this is not a critical failure
      }
    }

    return response;
  }

  /**
   * Search news with enhanced features
   */
  async searchNews(
    query: string,
    filters?: SearchFilters,
    includeSummaries = false
  ): Promise<NewsResponse> {
    const response = await newsSearchService.searchNews(query, filters);

    if (response.status === 'ok' && includeSummaries && response.articles.length > 0) {
      try {
        // Summarize top 3 results only to save API calls and avoid rate limits
        const topArticles = response.articles.slice(0, 3);
        const summaries = await newsSummarizationService.summarizeArticles(topArticles);

        // Merge summaries with top articles
        response.articles = response.articles.map((article, index) => {
          if (index < 3 && summaries[index]) {
            return {
              ...article,
              summary: summaries[index].summary,
              sentiment: summaries[index].sentiment,
              tags: summaries[index].tags
            };
          }
          return article;
        });
      } catch (error) {
        console.warn('AI summarization unavailable for search results, continuing with basic news:', error);
        // Continue without summaries - this is not a critical failure
      }
    }

    return response;
  }

  /**
   * Get trending news with market insights
   */
  async getTrendingNews(): Promise<{
    articles: NewsArticle[];
    insights: string;
    themes: string[];
  }> {
    try {
      const response = await newsFetchService.getTrendingNews();
      
      if (response.status === 'error' || response.articles.length === 0) {
        return {
          articles: [],
          insights: 'Unable to fetch trending news at this time.',
          themes: []
        };
      }

      // Get market insights and themes in parallel
      const [insights, themes] = await Promise.all([
        newsSummarizationService.generateMarketInsights(response.articles),
        newsSummarizationService.extractKeyThemes(response.articles)
      ]);

      return {
        articles: response.articles,
        insights,
        themes
      };
    } catch (error) {
      console.error('Error getting trending news:', error);
      return {
        articles: [],
        insights: 'Unable to generate market insights at this time.',
        themes: []
      };
    }
  }

  /**
   * Get search suggestions
   */
  async getSearchSuggestions(query: string): Promise<string[]> {
    return newsSearchService.getSearchSuggestions(query);
  }

  /**
   * Get trending topics
   */
  async getTrendingTopics(): Promise<string[]> {
    return newsSearchService.getTrendingTopics();
  }

  /**
   * Get all categories with their configurations
   */
  getCategories() {
    return Object.entries(require('./types').NEWS_CATEGORIES).map(([key, config]) => ({
      id: key,
      ...config
    }));
  }
}

// Export singleton instance
export const newsService = new NewsService();
