// Main news service exports

export * from './types';
export * from './fetch';
export * from './summarize';
export * from './search';

// Re-export main services for convenience
export { newsFetchService } from './fetch';
export { newsSummarizationService } from './summarize';
export { newsSearchService } from './search';

// Combined news service class for easier usage
import { newsFetchService } from './fetch';
import { newsSummarizationService } from './summarize';
import { newsSearchService } from './search';
import { marketIntelligenceService } from '../market-intelligence';
import { newsDeduplicationService } from './deduplication';
import { NewsArticle, NewsCategory, NewsResponse, SearchFilters } from './types';

class NewsService {
  /**
   * Get news by category with summaries and deduplication
   */
  async getNewsByCategory(category: NewsCategory, includeSummaries = false): Promise<NewsResponse> {
    const response = await newsFetchService.fetchNewsByCategory(category);

    if (response.status === 'ok' && response.articles.length > 0) {
      // Simple deduplication - just remove exact duplicates by URL
      const uniqueArticles = this.simpleDeduplication(response.articles);

      if (includeSummaries && uniqueArticles.length > 0) {
        try {
          // Limit to first 5 articles for summarization to avoid rate limits
          const articlesToSummarize = uniqueArticles.slice(0, 5);
          const summaries = await newsSummarizationService.summarizeArticles(articlesToSummarize);

          // Merge summaries with articles
          const articlesWithSummaries = uniqueArticles.map((article, index) => {
            if (index < 5 && summaries[index]) {
              return {
                ...article,
                summary: summaries[index].summary,
                sentiment: summaries[index].sentiment,
                tags: summaries[index].tags
              };
            }
            return article;
          });

          return {
            status: 'ok',
            totalResults: articlesWithSummaries.length,
            articles: articlesWithSummaries
          };
        } catch (error) {
          console.warn('AI summarization unavailable, continuing with news:', error);
          // Continue with articles without summaries
        }
      }

      return {
        status: 'ok',
        totalResults: uniqueArticles.length,
        articles: uniqueArticles
      };
    }

    return response;
  }

  /**
   * Search news with enhanced features and deduplication
   */
  async searchNews(
    query: string,
    filters?: SearchFilters,
    includeSummaries = false
  ): Promise<NewsResponse> {
    const response = await newsSearchService.searchNews(query, filters);

    if (response.status === 'ok' && response.articles.length > 0) {
      // Simple deduplication for search results
      const uniqueArticles = this.simpleDeduplication(response.articles);

      if (includeSummaries && uniqueArticles.length > 0) {
        try {
          // Summarize top 3 results only to save API calls and avoid rate limits
          const topArticles = uniqueArticles.slice(0, 3);
          const summaries = await newsSummarizationService.summarizeArticles(topArticles);

          // Merge summaries with top articles
          const articlesWithSummaries = uniqueArticles.map((article, index) => {
            if (index < 3 && summaries[index]) {
              return {
                ...article,
                summary: summaries[index].summary,
                sentiment: summaries[index].sentiment,
                tags: summaries[index].tags
              };
            }
            return article;
          });

          return {
            status: 'ok',
            totalResults: articlesWithSummaries.length,
            articles: articlesWithSummaries
          };
        } catch (error) {
          console.warn('AI summarization unavailable for search results, continuing with news:', error);
          // Continue with articles without summaries
        }
      }

      return {
        status: 'ok',
        totalResults: uniqueArticles.length,
        articles: uniqueArticles
      };
    }

    return response;
  }

  /**
   * Simple deduplication - removes exact URL duplicates
   */
  private simpleDeduplication(articles: NewsArticle[]): NewsArticle[] {
    const seenUrls = new Set<string>();
    return articles.filter(article => {
      if (seenUrls.has(article.url)) {
        return false;
      }
      seenUrls.add(article.url);
      return true;
    });
  }

  /**
   * Get trending news with advanced market intelligence
   */
  async getTrendingNews(): Promise<{
    articles: NewsArticle[];
    insights: string;
    themes: string[];
    marketIntelligence?: any;
    sentimentAnalysis?: any[];
  }> {
    try {
      const response = await newsFetchService.getTrendingNews();

      if (response.status === 'error' || response.articles.length === 0) {
        return {
          articles: [],
          insights: `## Market Overview

**News services are temporarily unavailable.** We're working to restore full functionality.

- **Status**: Unable to fetch trending news at this time
- **Recommendation**: Please try refreshing the page in a few minutes
- **Alternative**: Check individual news categories below for the latest updates

*We apologize for the inconvenience and appreciate your patience.*`,
          themes: ['Service Maintenance', 'System Updates']
        };
      }

      console.log(`📰 Processing ${response.articles.length} articles for comprehensive market analysis...`);

      // Apply additional filtering for hot and trending topics
      const hotTrendingArticles = this.selectHotTrendingArticles(response.articles);
      console.log(`🔥 Selected ${hotTrendingArticles.length} hot trending articles from ${response.articles.length} total`);

      // Get enhanced market intelligence from larger article set
      try {
        const marketData = await marketIntelligenceService.getMarketIntelligence(hotTrendingArticles);

        return {
          articles: hotTrendingArticles,
          insights: marketData.insights,
          themes: this.extractThemesFromIntelligence(marketData.intelligence),
          marketIntelligence: marketData.intelligence,
          sentimentAnalysis: marketData.sentimentAnalysis
        };
      } catch (aiError) {
        // If AI fails but we have articles, provide enhanced manual summary
        console.warn('Advanced intelligence failed, providing enhanced fallback:', aiError);

        const enhancedInsights = this.generateEnhancedManualInsights(hotTrendingArticles);
        const enhancedThemes = this.extractEnhancedManualThemes(hotTrendingArticles);

        return {
          articles: hotTrendingArticles,
          insights: enhancedInsights,
          themes: enhancedThemes
        };
      }
    } catch (error) {
      console.error('Error getting trending news:', error);
      return {
        articles: [],
        insights: `## Market Analysis Unavailable

**We're experiencing technical difficulties** with our market analysis system.

- **Issue**: ${error instanceof Error ? error.message : 'Unknown error occurred'}
- **Impact**: Market insights and trending analysis temporarily unavailable
- **Workaround**: Individual news categories are still functional
- **Status**: Our team is working to resolve this issue

*Please check back shortly for full market insights and analysis.*`,
        themes: ['Technical Issues', 'Service Recovery']
      };
    }
  }

  /**
   * Get multi-market sentiment analysis
   */
  async getMarketSentiment(articles: NewsArticle[]) {
    try {
      return await marketIntelligenceService.getMultiMarketSentiment(articles);
    } catch (error) {
      console.error('Error getting market sentiment:', error);
      return [];
    }
  }

  /**
   * Get market summary for dashboard
   */
  async getMarketSummary(articles: NewsArticle[]) {
    try {
      return await marketIntelligenceService.getMarketSummary(articles);
    } catch (error) {
      console.error('Error getting market summary:', error);
      return null;
    }
  }

  /**
   * Get search suggestions
   */
  async getSearchSuggestions(query: string): Promise<string[]> {
    return newsSearchService.getSearchSuggestions(query);
  }

  /**
   * Get trending topics
   */
  async getTrendingTopics(): Promise<string[]> {
    return newsSearchService.getTrendingTopics();
  }

  /**
   * Get all categories with their configurations
   */
  getCategories() {
    return Object.entries(require('./types').NEWS_CATEGORIES).map(([key, config]) => ({
      id: key,
      ...config
    }));
  }

  /**
   * Generate manual insights when AI is unavailable
   */
  private generateManualInsights(articles: NewsArticle[]): string {
    const totalArticles = articles.length;
    const recentArticles = articles.filter(a => {
      const hoursAgo = (Date.now() - new Date(a.publishedAt).getTime()) / (1000 * 60 * 60);
      return hoursAgo < 24;
    }).length;

    const categories = articles.reduce((acc, article) => {
      acc[article.category] = (acc[article.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topCategory = Object.entries(categories).sort(([,a], [,b]) => b - a)[0];

    return `## Market Overview

**Current Market Activity** (${totalArticles} articles analyzed)

### Key Highlights:
- **Recent Activity**: ${recentArticles} articles published in the last 24 hours
- **Primary Focus**: ${topCategory ? `${topCategory[0].replace('-', ' ')} sector (${topCategory[1]} articles)` : 'Mixed market coverage'}
- **Market Sentiment**: Active news flow indicates ongoing market developments

### Top Headlines:
${articles.slice(0, 3).map((article, index) =>
  `${index + 1}. **${article.title}** - ${article.source.name}`
).join('\n')}

### Analysis:
The current news flow suggests **active market conditions** with developments across multiple sectors. Key areas of focus include policy changes, corporate earnings, and market movements.

*Note: This is a manual analysis. AI-powered insights will return shortly for more detailed market analysis.*`;
  }

  /**
   * Extract manual themes when AI is unavailable
   */
  private extractManualThemes(articles: NewsArticle[]): string[] {
    const themes = new Set<string>();

    articles.forEach(article => {
      const title = article.title.toLowerCase();
      const description = article.description.toLowerCase();
      const content = `${title} ${description}`;

      // Common financial themes
      if (content.includes('rbi') || content.includes('reserve bank')) themes.add('RBI Policy');
      if (content.includes('fed') || content.includes('federal reserve')) themes.add('Federal Reserve');
      if (content.includes('inflation')) themes.add('Inflation');
      if (content.includes('interest rate')) themes.add('Interest Rates');
      if (content.includes('earnings') || content.includes('results')) themes.add('Corporate Earnings');
      if (content.includes('bitcoin') || content.includes('crypto')) themes.add('Cryptocurrency');
      if (content.includes('sensex') || content.includes('nifty')) themes.add('Indian Markets');
      if (content.includes('stock market') || content.includes('equity')) themes.add('Stock Markets');
      if (content.includes('banking') || content.includes('bank')) themes.add('Banking Sector');
      if (content.includes('ipo') || content.includes('listing')) themes.add('IPO Activity');
    });

    return Array.from(themes).slice(0, 8);
  }

  /**
   * Extract themes from market intelligence data
   */
  private extractThemesFromIntelligence(intelligence: any): string[] {
    const themes: string[] = [];

    // Extract from sentiment analysis
    if (intelligence.overallSentiment) {
      intelligence.overallSentiment.forEach((sentiment: any) => {
        if (sentiment.sentiment === 'bullish') themes.push(`${sentiment.market} Bullish`);
        if (sentiment.sentiment === 'bearish') themes.push(`${sentiment.market} Bearish`);
        if (sentiment.volatility === 'high') themes.push(`${sentiment.market} Volatility`);
      });
    }

    // Extract from trends
    if (intelligence.trends) {
      intelligence.trends.forEach((trend: any) => {
        if (trend.direction === 'upward') themes.push(`${trend.market} Uptrend`);
        if (trend.direction === 'downward') themes.push(`${trend.market} Downtrend`);
      });
    }

    // Extract from risk assessment
    if (intelligence.riskAssessment?.factors) {
      themes.push(...intelligence.riskAssessment.factors.slice(0, 3));
    }

    // Add correlation themes
    if (intelligence.marketCorrelations) {
      const corr = intelligence.marketCorrelations;
      if (corr.indianForeign > 0.7) themes.push('High Indian-Global Correlation');
      if (corr.indianCrypto > 0.7) themes.push('High Indian-Crypto Correlation');
      if (corr.foreignCrypto > 0.7) themes.push('High Global-Crypto Correlation');
    }

    return themes.slice(0, 10);
  }

  /**
   * Enhanced article selection for 50-article processing
   */
  private selectHotTrendingArticles(articles: NewsArticle[]): NewsArticle[] {
    console.log(`🔍 Selecting hot trending articles from ${articles.length} candidates...`);

    const scoredArticles = articles.map(article => ({
      article,
      score: this.calculateTrendingScore(article)
    })).sort((a, b) => b.score - a.score);

    const selectedArticles = this.ensureDiverseCoverage(scoredArticles.slice(0, 50));
    console.log(`✅ Selected ${selectedArticles.length} hot trending articles`);
    return selectedArticles;
  }

  private calculateTrendingScore(article: NewsArticle): number {
    const content = `${article.title} ${article.description}`.toLowerCase();
    let score = 0;

    // Hot topics (40%)
    const hotTopics = ['breaking', 'urgent', 'surge', 'crash', 'rally', 'record'];
    hotTopics.forEach(topic => {
      if (content.includes(topic)) score += 0.4 / hotTopics.length;
    });

    // Market impact (35%)
    const marketTerms = ['fed', 'rbi', 'earnings', 'ipo', 'regulation'];
    marketTerms.forEach(term => {
      if (content.includes(term)) score += 0.35 / marketTerms.length;
    });

    // Time relevance (25%)
    const hoursAgo = (Date.now() - new Date(article.publishedAt).getTime()) / (1000 * 60 * 60);
    if (hoursAgo < 2) score += 0.25;
    else if (hoursAgo < 6) score += 0.15;
    else if (hoursAgo < 12) score += 0.1;

    return Math.min(1.0, score);
  }

  private ensureDiverseCoverage(scoredArticles: { article: NewsArticle; score: number }[]): NewsArticle[] {
    const categories = { indian: [], global: [], crypto: [], sectors: [], policy: [] } as Record<string, NewsArticle[]>;

    scoredArticles.forEach(({ article }) => {
      const content = `${article.title} ${article.description}`.toLowerCase();

      if (['india', 'rbi', 'sensex', 'nifty'].some(term => content.includes(term))) {
        categories.indian.push(article);
      } else if (['bitcoin', 'crypto', 'blockchain'].some(term => content.includes(term))) {
        categories.crypto.push(article);
      } else if (['policy', 'regulation', 'fed'].some(term => content.includes(term))) {
        categories.policy.push(article);
      } else if (['banking', 'technology', 'sector'].some(term => content.includes(term))) {
        categories.sectors.push(article);
      } else {
        categories.global.push(article);
      }
    });

    const selected: NewsArticle[] = [];
    const targets = { indian: 15, global: 12, crypto: 8, sectors: 8, policy: 7 };

    Object.entries(categories).forEach(([category, articles]) => {
      const target = targets[category as keyof typeof targets];
      selected.push(...articles.slice(0, target));
    });

    return selected;
  }

  private generateEnhancedManualInsights(articles: NewsArticle[]): string {
    const segments = this.analyzeMarketSegments(articles);
    const topics = this.identifyTrendingTopics(articles);

    return `## 📊 Enhanced Market Intelligence (${articles.length} Articles)

### 🔥 Hot Topics:
${topics.slice(0, 5).map(t => `- **${t.topic}**: ${t.count} articles`).join('\n')}

### 📈 Market Coverage:
- **Indian Markets**: ${segments.indian} articles
- **Global Markets**: ${segments.global} articles
- **Crypto Markets**: ${segments.crypto} articles

### 💡 Key Insights:
- Processing ${articles.length} carefully selected trending articles
- Enhanced coverage across all major market segments
- Real-time analysis of hot market topics

*Comprehensive analysis from ${articles.length} high-relevance trending articles*`;
  }

  private analyzeMarketSegments(articles: NewsArticle[]) {
    return {
      indian: articles.filter(a => ['india', 'rbi', 'sensex'].some(term =>
        `${a.title} ${a.description}`.toLowerCase().includes(term))).length,
      global: articles.filter(a => !['india', 'bitcoin', 'crypto'].some(term =>
        `${a.title} ${a.description}`.toLowerCase().includes(term))).length,
      crypto: articles.filter(a => ['bitcoin', 'crypto'].some(term =>
        `${a.title} ${a.description}`.toLowerCase().includes(term))).length
    };
  }

  private identifyTrendingTopics(articles: NewsArticle[]) {
    const topics = new Map<string, number>();
    const keywords = ['earnings', 'inflation', 'bitcoin', 'regulation', 'merger', 'ipo'];

    articles.forEach(article => {
      const content = `${article.title} ${article.description}`.toLowerCase();
      keywords.forEach(keyword => {
        if (content.includes(keyword)) {
          topics.set(keyword, (topics.get(keyword) || 0) + 1);
        }
      });
    });

    return Array.from(topics.entries())
      .map(([topic, count]) => ({ topic, count }))
      .sort((a, b) => b.count - a.count);
  }

  private extractEnhancedManualThemes(articles: NewsArticle[]): string[] {
    const themes = new Set<string>();
    const keywords = [
      'Market Volatility', 'Interest Rates', 'Inflation', 'Corporate Earnings',
      'Cryptocurrency', 'Banking Sector', 'Global Markets', 'Indian Markets'
    ];

    articles.forEach(article => {
      const content = `${article.title} ${article.description}`.toLowerCase();
      keywords.forEach(keyword => {
        if (content.includes(keyword.toLowerCase())) {
          themes.add(keyword);
        }
      });
    });

    return Array.from(themes).slice(0, 8);
  }
}

// Export singleton instance
export const newsService = new NewsService();
