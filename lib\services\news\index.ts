// Main news service exports

export * from './types';
export * from './fetch';
export * from './summarize';
export * from './search';

// Re-export main services for convenience
export { newsFetchService } from './fetch';
export { newsSummarizationService } from './summarize';
export { newsSearchService } from './search';

// Combined news service class for easier usage
import { newsFetchService } from './fetch';
import { newsSummarizationService } from './summarize';
import { newsSearchService } from './search';
import { NewsArticle, NewsCategory, NewsResponse, SearchFilters } from './types';

class NewsService {
  /**
   * Get news by category with summaries
   */
  async getNewsByCategory(category: NewsCategory, includeSummaries = false): Promise<NewsResponse> {
    const response = await newsFetchService.fetchNewsByCategory(category);
    
    if (response.status === 'ok' && includeSummaries && response.articles.length > 0) {
      try {
        const summaries = await newsSummarizationService.summarizeArticles(response.articles);
        
        // Merge summaries with articles
        response.articles = response.articles.map((article, index) => ({
          ...article,
          summary: summaries[index]?.summary,
          sentiment: summaries[index]?.sentiment,
          tags: summaries[index]?.tags
        }));
      } catch (error) {
        console.error('Error adding summaries:', error);
        // Continue without summaries
      }
    }
    
    return response;
  }

  /**
   * Search news with enhanced features
   */
  async searchNews(
    query: string, 
    filters?: SearchFilters,
    includeSummaries = false
  ): Promise<NewsResponse> {
    const response = await newsSearchService.searchNews(query, filters);
    
    if (response.status === 'ok' && includeSummaries && response.articles.length > 0) {
      try {
        // Summarize top 10 results only to save API calls
        const topArticles = response.articles.slice(0, 10);
        const summaries = await newsSummarizationService.summarizeArticles(topArticles);
        
        // Merge summaries with top articles
        response.articles = response.articles.map((article, index) => {
          if (index < 10 && summaries[index]) {
            return {
              ...article,
              summary: summaries[index].summary,
              sentiment: summaries[index].sentiment,
              tags: summaries[index].tags
            };
          }
          return article;
        });
      } catch (error) {
        console.error('Error adding summaries to search results:', error);
      }
    }
    
    return response;
  }

  /**
   * Get trending news with market insights
   */
  async getTrendingNews(): Promise<{
    articles: NewsArticle[];
    insights: string;
    themes: string[];
  }> {
    try {
      const response = await newsFetchService.getTrendingNews();
      
      if (response.status === 'error' || response.articles.length === 0) {
        return {
          articles: [],
          insights: 'Unable to fetch trending news at this time.',
          themes: []
        };
      }

      // Get market insights and themes in parallel
      const [insights, themes] = await Promise.all([
        newsSummarizationService.generateMarketInsights(response.articles),
        newsSummarizationService.extractKeyThemes(response.articles)
      ]);

      return {
        articles: response.articles,
        insights,
        themes
      };
    } catch (error) {
      console.error('Error getting trending news:', error);
      return {
        articles: [],
        insights: 'Unable to generate market insights at this time.',
        themes: []
      };
    }
  }

  /**
   * Get search suggestions
   */
  async getSearchSuggestions(query: string): Promise<string[]> {
    return newsSearchService.getSearchSuggestions(query);
  }

  /**
   * Get trending topics
   */
  async getTrendingTopics(): Promise<string[]> {
    return newsSearchService.getTrendingTopics();
  }

  /**
   * Get all categories with their configurations
   */
  getCategories() {
    return Object.entries(require('./types').NEWS_CATEGORIES).map(([key, config]) => ({
      id: key,
      ...config
    }));
  }
}

// Export singleton instance
export const newsService = new NewsService();
