"use client";

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Mi<PERSON>, MicOff, Send, Brain, Lightbulb, TrendingUp, PieChart, Bot, User, Loader2, AlertTriangle, Volume2, VolumeX, AudioWaveform as Waveform, Play, Pause } from 'lucide-react';
import { geminiClient, type ChatMessage as GeminiChatMessage } from '@/lib/gemini';
import { MarkdownRenderer } from '@/components/ui/markdown-renderer';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

export default function ChatPage() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: `# 🎯 Welcome to Your AI Financial Strategist!

I'm your expert financial advisor, powered by advanced AI to provide comprehensive financial guidance. I specialize in delivering **structured, actionable financial advice** with real-world examples and market insights.

## 💼 **My Expertise Areas:**

- **💰 Personal Budgeting & Expense Management**
- **📈 Investment Strategies & Portfolio Analysis**
- **🏦 Retirement Planning & Tax Optimization**
- **💳 Debt Management & Credit Improvement**
- **🎯 Financial Goal Setting & Achievement**
- **📊 Market Analysis & Economic Insights**

## 🔍 **What Makes My Responses Special:**

✅ **Structured Format**: Every response follows a clear framework with definitions, examples, and action steps
✅ **Real Numbers**: Specific calculations, dollar amounts, and realistic scenarios
✅ **Market Context**: How financial decisions impact your position and the broader market
✅ **Actionable Steps**: Clear, implementable recommendations you can act on immediately

**Ready to transform your financial future?** Ask me any financial question and I'll provide a comprehensive, structured response with examples and actionable insights!`,
      timestamp: new Date(),
      suggestions: [
        "Create a budget for $75,000 annual income",
        "Explain compound interest with $500/month example",
        "Compare Roth IRA vs Traditional IRA with calculations",
        "Design an emergency fund strategy"
      ]
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [audioLevel, setAudioLevel] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [autoReadResponses, setAutoReadResponses] = useState(false);
  const [currentSpeakingMessageId, setCurrentSpeakingMessageId] = useState<string | null>(null);
  const [currentUtterance, setCurrentUtterance] = useState<SpeechSynthesisUtterance | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  // @ts-ignore: SpeechRecognition is a browser API, not in TypeScript DOM lib by default
  const recognition = useRef<(typeof window extends { SpeechRecognition: any } ? InstanceType<typeof window.SpeechRecognition> : InstanceType<typeof window.webkitSpeechRecognition>) | null>(null);
  const synthesis = useRef<SpeechSynthesis | null>(null);
  const audioContext = useRef<AudioContext | null>(null);
  const analyser = useRef<AnalyserNode | null>(null);
  const microphone = useRef<MediaStreamAudioSourceNode | null>(null);
  const latestTranscriptRef = useRef<string>('');
  const silenceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const promptSuggestions = [
    {
      category: "Investment & Portfolio",
      icon: <TrendingUp className="h-4 w-4" />,
      prompts: [
        "Explain dollar-cost averaging with a $500/month example",
        "Compare ETFs vs mutual funds with real examples",
        "How to build a diversified portfolio with $10,000?",
        "Calculate potential returns: $1000 monthly for 20 years",
        "What's the difference between growth and value stocks?",
        "Should I invest in international markets?"
      ]
    },
    {
      category: "Budgeting & Savings",
      icon: <PieChart className="h-4 w-4" />,
      prompts: [
        "Create a budget for $60,000 annual income",
        "How to save $10,000 in one year?",
        "Track expenses effectively: best methods",
        "Emergency fund: how much and where to keep it?",
        "Reduce monthly expenses by 20%: practical tips",
        "High-yield savings vs money market accounts"
      ]
    },
    {
      category: "Debt & Credit",
      icon: <Brain className="h-4 w-4" />,
      prompts: [
        "Debt snowball vs avalanche: which is better?",
        "Improve credit score from 650 to 750",
        "Should I consolidate my credit card debt?",
        "Student loan repayment strategies",
        "Mortgage vs rent: financial comparison",
        "How to negotiate with creditors effectively"
      ]
    },
    {
      category: "Retirement Planning",
      icon: <Lightbulb className="h-4 w-4" />,
      prompts: [
        "Calculate retirement needs for $80,000 lifestyle",
        "401(k) vs Roth IRA: which should I prioritize?",
        "Maximize employer 401(k) matching strategies",
        "Retirement withdrawal strategies to minimize taxes",
        "Social Security optimization techniques",
        "Catch-up contributions for people over 50"
      ]
    }
  ];

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  useEffect(() => {
    // Initialize Web Speech API with enhanced functionality
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognition.current = new SpeechRecognition();
      recognition.current.continuous = true;
      recognition.current.interimResults = true;
      recognition.current.lang = 'en-US';

      recognition.current.onresult = (event: any) => {
        let transcript = '';
        let isFinal = false;

        for (let i = event.resultIndex; i < event.results.length; i++) {
          transcript += event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            isFinal = true;
          }
        }

        setCurrentTranscript(transcript);
        latestTranscriptRef.current = transcript;

        // Clear any existing timeout
        if (silenceTimeoutRef.current) {
          clearTimeout(silenceTimeoutRef.current);
        }

        // If this is a final result, process immediately
        if (isFinal && transcript.trim()) {
          recognition.current?.stop();
          setInputValue(transcript.trim());
          setCurrentTranscript('');
          setIsListening(false);
        } else {
          // Set a timeout to process after silence
          silenceTimeoutRef.current = setTimeout(() => {
            if (transcript.trim()) {
              recognition.current?.stop();
              setInputValue(transcript.trim());
              setCurrentTranscript('');
              setIsListening(false);
            }
          }, 2000); // 2 seconds of silence
        }
      };

      recognition.current.onend = () => {
        setIsListening(false);
        setAudioLevel(0);

        // Clear any pending timeout
        if (silenceTimeoutRef.current) {
          clearTimeout(silenceTimeoutRef.current);
          silenceTimeoutRef.current = null;
        }

        const finalTranscript = latestTranscriptRef.current.trim();
        if (finalTranscript && !isProcessing) {
          setInputValue(finalTranscript);
          setCurrentTranscript('');
        }
      };

      recognition.current.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
        setAudioLevel(0);
        setCurrentTranscript('');
      };

      recognition.current.onstart = () => {
        setIsListening(true);
      };
    }

    // Initialize Speech Synthesis
    if ('speechSynthesis' in window) {
      synthesis.current = window.speechSynthesis;
    }

    // Initialize Audio Context for visualization
    initializeAudioVisualization();

    // Add keyboard shortcuts for voice controls
    const handleKeyPress = (event: KeyboardEvent) => {
      // Space bar to pause/play (when not typing in input)
      if (event.code === 'Space' && event.target === document.body && isSpeaking) {
        event.preventDefault();
        if (isPaused) {
          resumeSpeaking();
        } else {
          pauseSpeaking();
        }
      }
      // Escape to stop speaking
      if (event.code === 'Escape' && isSpeaking) {
        event.preventDefault();
        stopSpeaking();
      }
    };

    document.addEventListener('keydown', handleKeyPress);

    return () => {
      if (audioContext.current) {
        audioContext.current.close();
      }
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current);
      }
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [isSpeaking, isPaused]);

  const initializeAudioVisualization = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      audioContext.current = new AudioContext();
      analyser.current = audioContext.current.createAnalyser();
      microphone.current = audioContext.current.createMediaStreamSource(stream);

      analyser.current.fftSize = 256;
      microphone.current.connect(analyser.current);

      visualizeAudio();
    } catch (error) {
      console.error('Error accessing microphone:', error);
    }
  };

  const visualizeAudio = () => {
    if (!analyser.current) return;

    const bufferLength = analyser.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const updateLevel = () => {
      analyser.current!.getByteFrequencyData(dataArray);
      const average = dataArray.reduce((a, b) => a + b) / bufferLength;
      setAudioLevel(Math.min(average / 128 * 100, 100));

      if (isListening) {
        requestAnimationFrame(updateLevel);
      }
    };

    if (isListening) {
      updateLevel();
    }
  };

  // Convert markdown response to speech-friendly text
  const convertMarkdownToSpeech = (markdown: string): string => {
    let speechText = markdown;

    // Remove markdown headers and replace with spoken equivalents
    speechText = speechText.replace(/^#{1,6}\s*(.+)$/gm, '$1. ');

    // Remove bold and italic formatting
    speechText = speechText.replace(/\*\*(.*?)\*\*/g, '$1');
    speechText = speechText.replace(/\*(.*?)\*/g, '$1');

    // Remove emojis and special characters
    speechText = speechText.replace(/[📋🎯💡🌍📈✅🔍📊💰💼🏦💳🎪⚡🔥]/g, '');

    // Replace bullet points with spoken format
    speechText = speechText.replace(/^[-•]\s*/gm, 'Point: ');
    speechText = speechText.replace(/^\d+\.\s*/gm, 'Step $&');

    // Clean up extra whitespace and line breaks
    speechText = speechText.replace(/\n\s*\n/g, '. ');
    speechText = speechText.replace(/\n/g, ' ');
    speechText = speechText.replace(/\s+/g, ' ');

    // Limit length for speech (keep first 800 characters for better voice experience)
    if (speechText.length > 800) {
      speechText = speechText.substring(0, 800) + '... For more details, please check the full response on screen.';
    }

    return speechText.trim();
  };

  const speakResponse = (text: string, messageId?: string) => {
    if (!synthesis.current) return;

    // Stop any current speech
    if (isSpeaking) {
      synthesis.current.cancel();
    }

    setIsSpeaking(true);
    setIsPaused(false);
    setCurrentSpeakingMessageId(messageId || null);

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = 0.9;
    utterance.pitch = 1;
    utterance.volume = 0.8;

    utterance.onend = () => {
      setIsSpeaking(false);
      setIsPaused(false);
      setCurrentSpeakingMessageId(null);
      setCurrentUtterance(null);
    };

    utterance.onerror = () => {
      setIsSpeaking(false);
      setIsPaused(false);
      setCurrentSpeakingMessageId(null);
      setCurrentUtterance(null);
    };

    utterance.onpause = () => {
      setIsPaused(true);
    };

    utterance.onresume = () => {
      setIsPaused(false);
    };

    setCurrentUtterance(utterance);
    synthesis.current.speak(utterance);
  };

  const pauseSpeaking = () => {
    if (synthesis.current && isSpeaking && !isPaused) {
      synthesis.current.pause();
      setIsPaused(true);
    }
  };

  const resumeSpeaking = () => {
    if (synthesis.current && isSpeaking && isPaused) {
      synthesis.current.resume();
      setIsPaused(false);
    }
  };

  const stopSpeaking = () => {
    if (synthesis.current) {
      synthesis.current.cancel();
      setIsSpeaking(false);
      setIsPaused(false);
      setCurrentSpeakingMessageId(null);
      setCurrentUtterance(null);
    }
  };

  const handleSendMessage = async (content: string, shouldSpeak: boolean = false) => {
    if (!content.trim()) return;

    // Check if the query is finance-related
    if (!isFinanceRelated(content.trim())) {
      const warningMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: "I'm your AI Financial Strategist, and I'm specifically designed to help with financial questions and planning. Please ask me about topics like budgeting, investing, retirement planning, debt management, or other financial matters. How can I assist you with your financial goals today?",
        timestamp: new Date(),
        suggestions: [
          "Help me create a budget plan",
          "Explain investment strategies",
          "How to build an emergency fund",
          "Retirement planning advice"
        ]
      };
      setMessages(prev => [...prev, warningMessage]);

      // Speak the warning if voice was used
      if (shouldSpeak) {
        speakResponse(warningMessage.content);
      }
      return;
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: content.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Convert messages to Gemini format
      const chatHistory: GeminiChatMessage[] = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      // Add the new user message
      chatHistory.push({
        role: 'user',
        content: content.trim()
      });

      // Call Gemini API with financial context
      const response = await geminiClient.generateContent(chatHistory, {
        userBehavior: { focusArea: 'financial_planning' },
        financialProfile: { experienceLevel: 'mixed' }
      });

      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.content,
        timestamp: new Date(),
        suggestions: response.suggestions || generateFinancialSuggestions(content)
      };

      setMessages(prev => [...prev, aiResponse]);

      // Speak the response if voice was used OR auto-read is enabled
      if (shouldSpeak || autoReadResponses) {
        const speechText = convertMarkdownToSpeech(response.content);
        speakResponse(speechText, aiResponse.id);
      }
    } catch (error) {
      console.error('Error calling Gemini API:', error);
      const errorContent = "I apologize, but I'm experiencing technical difficulties. Please try again in a moment. In the meantime, I can help you with budgeting, investment strategies, retirement planning, or any other financial questions you might have.";

      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: errorContent,
        timestamp: new Date(),
        suggestions: [
          "Help me understand compound interest",
          "Explain different investment types",
          "How to improve my credit score",
          "Create a debt payoff strategy"
        ]
      };
      setMessages(prev => [...prev, errorMessage]);

      // Speak the error message if voice was used OR auto-read is enabled
      if (shouldSpeak || autoReadResponses) {
        speakResponse(errorContent, errorMessage.id);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Check if the user's query is finance-related
  const isFinanceRelated = (input: string): boolean => {
    const financeKeywords = [
      // Core financial terms
      'budget', 'money', 'finance', 'financial', 'investment', 'invest', 'portfolio', 'stock', 'bond',
      'retirement', 'pension', 'savings', 'save', 'debt', 'loan', 'credit', 'mortgage', 'insurance',
      'tax', 'taxes', 'income', 'salary', 'expense', 'spending', 'cost', 'price', 'value', 'worth',

      // Investment terms
      'etf', 'mutual fund', 'index fund', 'dividend', 'yield', 'return', 'roi', 'risk', 'diversification',
      'asset', 'allocation', 'rebalance', 'compound', 'interest', 'inflation', 'market', 'trading',

      // Banking and accounts
      'bank', 'account', 'checking', 'savings', 'cd', 'certificate', 'deposit', 'withdrawal', 'transfer',
      'ira', '401k', 'roth', 'traditional', 'hsa', 'emergency fund',

      // Financial planning
      'goal', 'planning', 'strategy', 'advice', 'guidance', 'recommendation', 'analysis', 'calculation',
      'net worth', 'cash flow', 'liquidity', 'solvency', 'bankruptcy', 'foreclosure',

      // Economic terms
      'economy', 'economic', 'recession', 'bull market', 'bear market', 'volatility', 'gdp',
      'federal reserve', 'interest rate', 'monetary policy'
    ];

    const lowerInput = input.toLowerCase();
    return financeKeywords.some(keyword => lowerInput.includes(keyword)) ||
           // Also check for financial question patterns
           /how much|how to|what is|explain|calculate|compare|should i|can i|help me/i.test(input);
  };

  // Generate contextual financial suggestions based on user input
  const generateFinancialSuggestions = (input: string): string[] => {
    const lowerInput = input.toLowerCase();

    if (lowerInput.includes('budget') || lowerInput.includes('spending')) {
      return [
        "Show me the 50/30/20 budgeting rule",
        "How to track expenses effectively?",
        "What budgeting apps do you recommend?",
        "Help me reduce monthly expenses"
      ];
    }

    if (lowerInput.includes('invest') || lowerInput.includes('portfolio') || lowerInput.includes('stock')) {
      return [
        "Explain dollar-cost averaging strategy",
        "What's the difference between ETFs and mutual funds?",
        "How to diversify my investment portfolio?",
        "Calculate potential investment returns"
      ];
    }

    if (lowerInput.includes('retirement') || lowerInput.includes('401k') || lowerInput.includes('ira')) {
      return [
        "Calculate my retirement savings needs",
        "Explain Roth vs Traditional IRA",
        "How to maximize employer 401(k) matching?",
        "Retirement withdrawal strategies"
      ];
    }

    if (lowerInput.includes('debt') || lowerInput.includes('loan') || lowerInput.includes('credit')) {
      return [
        "Debt snowball vs avalanche method",
        "How to improve my credit score?",
        "Should I consolidate my debts?",
        "Strategies for paying off student loans"
      ];
    }

    if (lowerInput.includes('emergency') || lowerInput.includes('savings')) {
      return [
        "How much should I save for emergencies?",
        "Best high-yield savings accounts",
        "Building an emergency fund step by step",
        "Where to keep emergency savings?"
      ];
    }

    // Default financial suggestions
    return [
      "Help me create a comprehensive financial plan",
      "Explain compound interest with examples",
      "How to set and achieve financial goals?",
      "Basic principles of personal finance"
    ];
  };

  const toggleListening = () => {
    if (!recognition.current) return;

    if (isListening) {
      recognition.current.stop();
      setIsListening(false);
      setAudioLevel(0);
      setCurrentTranscript('');

      // Clear any pending timeout
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current);
        silenceTimeoutRef.current = null;
      }
    } else {
      setCurrentTranscript('');
      setInputValue('');
      latestTranscriptRef.current = '';
      recognition.current.start();
      setIsListening(true);
      visualizeAudio();
    }
  };

  const handleVoiceMessage = () => {
    if (inputValue.trim()) {
      handleSendMessage(inputValue, true); // true indicates voice input
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 overflow-x-hidden">
      <div className="container mx-auto px-4 py-8 max-w-full overflow-x-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[calc(100vh-8rem)] max-w-full overflow-hidden">

          {/* Sidebar - Prompt Suggestions */}
          <div className="lg:col-span-1 min-w-0">
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm h-full overflow-hidden">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-400" />
                  Smart Prompts
                </CardTitle>
              </CardHeader>
              <CardContent className="overflow-hidden">
                <ScrollArea className="h-[calc(100vh-16rem)]">
                  <div className="space-y-6">
                    {promptSuggestions.map((category, index) => (
                      <div key={index} className="space-y-3">
                        <div className="flex items-center gap-2">
                          {category.icon}
                          <h3 className="text-sm font-medium text-slate-300">{category.category}</h3>
                        </div>
                        <div className="space-y-2">
                          {category.prompts.map((prompt, promptIndex) => (
                            <Button
                              key={promptIndex}
                              variant="ghost"
                              size="sm"
                              className="text-xs text-slate-400 hover:text-slate-200 h-auto p-3 justify-start whitespace-normal text-left leading-relaxed min-h-[2.5rem]"
                              onClick={() => handleSendMessage(prompt)}
                            >
                              {prompt}
                            </Button>
                          ))}
                        </div>
                        {index < promptSuggestions.length - 1 && (
                          <Separator className="bg-slate-700" />
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* Main Chat Area */}
          <div className="lg:col-span-3 min-w-0 overflow-hidden">
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm h-full flex flex-col overflow-hidden">
              <CardHeader className="flex-shrink-0">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-white flex items-center gap-2">
                    <Bot className="h-5 w-5 text-blue-400" />
                    AI Financial Strategist
                  </CardTitle>
                  <Badge variant="outline" className="bg-green-500/10 text-green-300 border-green-500/30">
                    Online
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="flex-1 flex flex-col overflow-hidden min-h-0">
                {/* Auto-Read Notification */}
                {autoReadResponses && (
                  <div className="mb-4 p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Volume2 className="h-4 w-4 text-green-400" />
                      <span className="text-sm text-green-300 font-medium">🔊 Auto-Read Enabled</span>
                    </div>
                    <p className="text-xs text-green-200 mt-1">
                      AI responses will be automatically read aloud. Use pause/play controls to manage playback, or disable auto-read below.
                    </p>
                    <div className="flex items-center gap-4 mt-2 text-xs text-green-300">
                      <div className="flex items-center gap-1">
                        <Play className="h-3 w-3" />
                        <span>Pause/Play controls available</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <kbd className="px-1 py-0.5 bg-green-500/20 rounded text-xs">Space</kbd>
                        <span>Pause/Play</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <kbd className="px-1 py-0.5 bg-green-500/20 rounded text-xs">Esc</kbd>
                        <span>Stop</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Messages */}
                <ScrollArea className="flex-1 pr-2">
                  <div className="space-y-6 pb-4 pr-2">
                    {messages.map((message) => (
                      <div key={message.id} className={`flex gap-3 w-full ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                        <div className={`flex gap-3 ${message.role === 'user' ? 'max-w-[85%] flex-row-reverse' : 'max-w-full flex-row'} w-full`}>
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                            message.role === 'user' ? 'bg-blue-600' : 'bg-slate-700'
                          }`}>
                            {message.role === 'user' ? (
                              <User className="h-4 w-4 text-white" />
                            ) : (
                              <Bot className="h-4 w-4 text-blue-400" />
                            )}
                          </div>
                          <div className="space-y-2 flex-1 min-w-0 overflow-hidden">
                            <div className={`p-4 rounded-lg ${
                              message.role === 'user'
                                ? 'bg-blue-600 text-white max-h-96 overflow-y-auto'
                                : 'bg-slate-700 text-slate-200 max-h-[32rem] overflow-y-auto'
                            }`}>
                              {message.role === 'user' ? (
                                <p className="text-sm leading-relaxed break-words overflow-wrap-anywhere chat-message-content">{message.content}</p>
                              ) : (
                                <div className="space-y-3">
                                  <div className="overflow-hidden w-full chat-message-content">
                                    <MarkdownRenderer
                                      content={message.content}
                                      className="text-sm w-full chat-container"
                                    />
                                  </div>
                                  {/* Enhanced Voice Controls for AI Messages */}
                                  <div className="flex items-center justify-between pt-3 border-t border-slate-600">
                                    <div className="flex items-center gap-2">
                                      {/* Main Read Response Button */}
                                      <Button
                                        size="sm"
                                        variant={currentSpeakingMessageId === message.id ? "default" : "outline"}
                                        onClick={() => speakResponse(convertMarkdownToSpeech(message.content), message.id)}
                                        disabled={isSpeaking && currentSpeakingMessageId !== message.id}
                                        className={`h-8 px-3 ${
                                          currentSpeakingMessageId === message.id
                                            ? 'bg-blue-600 text-white border-blue-500'
                                            : 'border-slate-500 text-slate-300 hover:text-white hover:bg-slate-600 hover:border-slate-400'
                                        }`}
                                        title={currentSpeakingMessageId === message.id ? "Currently reading response" : "Read this response aloud"}
                                      >
                                        {currentSpeakingMessageId === message.id ? (
                                          <>
                                            <Waveform className={`h-3 w-3 mr-1 ${isPaused ? '' : 'animate-pulse'}`} />
                                            <span className="text-xs font-medium">
                                              {isPaused ? 'Paused' : 'Reading...'}
                                            </span>
                                          </>
                                        ) : (
                                          <>
                                            <Volume2 className="h-3 w-3 mr-1" />
                                            <span className="text-xs font-medium">🔊 Read Response</span>
                                          </>
                                        )}
                                      </Button>

                                      {/* Pause/Play Button (only when this message is speaking) */}
                                      {currentSpeakingMessageId === message.id && (
                                        <Button
                                          size="sm"
                                          variant="outline"
                                          onClick={isPaused ? resumeSpeaking : pauseSpeaking}
                                          className="text-blue-400 border-blue-500/50 hover:text-white hover:bg-blue-600/20 hover:border-blue-400 h-8 px-2"
                                          title={isPaused ? "Resume reading" : "Pause reading"}
                                        >
                                          {isPaused ? (
                                            <>
                                              <Play className="h-3 w-3 mr-1" />
                                              <span className="text-xs">Play</span>
                                            </>
                                          ) : (
                                            <>
                                              <Pause className="h-3 w-3 mr-1" />
                                              <span className="text-xs">Pause</span>
                                            </>
                                          )}
                                        </Button>
                                      )}

                                      {/* Stop Button (only when this message is speaking) */}
                                      {currentSpeakingMessageId === message.id && (
                                        <Button
                                          size="sm"
                                          variant="outline"
                                          onClick={stopSpeaking}
                                          className="text-red-400 border-red-500/50 hover:text-white hover:bg-red-600/20 hover:border-red-400 h-8 px-2"
                                          title="Stop reading"
                                        >
                                          <VolumeX className="h-3 w-3 mr-1" />
                                          <span className="text-xs">Stop</span>
                                        </Button>
                                      )}
                                    </div>

                                    {/* Speaking/Paused Indicator */}
                                    {currentSpeakingMessageId === message.id && (
                                      <div className={`flex items-center gap-1 px-2 py-1 rounded-full ${
                                        isPaused
                                          ? 'bg-yellow-500/10 border border-yellow-500/30'
                                          : 'bg-blue-500/10 border border-blue-500/30'
                                      }`}>
                                        <div className={`w-2 h-2 rounded-full ${
                                          isPaused
                                            ? 'bg-yellow-400'
                                            : 'bg-blue-400 animate-pulse'
                                        }`}></div>
                                        <span className={`text-xs ${
                                          isPaused ? 'text-yellow-300' : 'text-blue-300'
                                        }`}>
                                          {isPaused ? 'Paused' : 'Speaking'}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                            {message.suggestions && message.suggestions.length > 0 && (
                              <div className="flex flex-wrap gap-2 mt-3">
                                {message.suggestions.map((suggestion, index) => (
                                  <Button
                                    key={index}
                                    variant="outline"
                                    size="sm"
                                    className="text-xs border-slate-600 text-slate-300 hover:bg-slate-700 whitespace-normal text-left h-auto py-2 px-3"
                                    onClick={() => handleSendMessage(suggestion)}
                                  >
                                    {suggestion}
                                  </Button>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                    {isLoading && (
                      <div className="flex gap-3">
                        <div className="w-8 h-8 rounded-full bg-slate-700 flex items-center justify-center">
                          <Bot className="h-4 w-4 text-blue-400" />
                        </div>
                        <div className="bg-slate-700 p-3 rounded-lg">
                          <div className="flex items-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin text-blue-400" />
                            <span className="text-sm text-slate-300">Analyzing your request...</span>
                          </div>
                        </div>
                      </div>
                    )}
                    <div ref={messagesEndRef} />
                  </div>
                </ScrollArea>

                {/* Voice Transcript Display */}
                {currentTranscript && (
                  <div className="mt-4 p-3 bg-slate-700/50 border border-slate-600 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Waveform className="h-4 w-4 text-purple-400" />
                      <span className="text-sm text-slate-300">Listening...</span>
                    </div>
                    <p className="text-white text-sm">{currentTranscript}</p>
                  </div>
                )}

                {/* Audio Level Visualization */}
                {isListening && (
                  <div className="mt-4 space-y-2">
                    <div className="flex items-center justify-center gap-2">
                      <Waveform className="h-4 w-4 text-purple-400" />
                      <span className="text-sm text-slate-300">Audio Level</span>
                    </div>
                    <div className="w-full bg-slate-700 rounded-full h-2">
                      <div
                        className="bg-purple-400 h-2 rounded-full transition-all duration-100"
                        style={{ width: `${audioLevel}%` }}
                      />
                    </div>
                  </div>
                )}

                {/* Input Area */}
                <div className="mt-4 space-y-4">
                  <Separator className="bg-slate-700" />
                  <div className="flex gap-2">
                    <div className="flex-1">
                      <Input
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        placeholder={isListening ? "Listening... Speak now" : "Ask me anything about your finances..."}
                        className="bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage(inputValue);
                          }
                        }}
                        disabled={isListening}
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      className={`border-slate-600 ${
                        isListening
                          ? 'bg-red-600 text-white animate-pulse'
                          : 'text-slate-300 hover:bg-slate-700'
                      }`}
                      onClick={toggleListening}
                      disabled={isLoading || isSpeaking}
                      title={isListening ? "Stop listening" : "Start voice input"}
                    >
                      {isListening ? <MicOff className="h-4 w-4" /> : <Mic className="h-4 w-4" />}
                    </Button>
                    {isSpeaking && (
                      <Button
                        variant="outline"
                        size="icon"
                        className="border-slate-600 text-slate-300 hover:bg-slate-700"
                        onClick={stopSpeaking}
                        title="Stop speaking"
                      >
                        <VolumeX className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      onClick={() => handleSendMessage(inputValue)}
                      disabled={!inputValue.trim() || isLoading || isListening}
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                      title="Send message"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Voice Status Indicators and Controls */}
                  <div className="flex justify-center gap-2 flex-wrap">
                    <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
                      isListening
                        ? 'bg-red-500/10 text-red-300 border border-red-500/30'
                        : 'bg-slate-500/10 text-slate-400 border border-slate-500/30'
                    }`}>
                      <Mic className="h-3 w-3" />
                      {isListening ? 'Recording' : 'Ready'}
                    </div>

                    <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
                      isSpeaking
                        ? isPaused
                          ? 'bg-yellow-500/10 text-yellow-300 border border-yellow-500/30'
                          : 'bg-blue-500/10 text-blue-300 border border-blue-500/30'
                        : 'bg-slate-500/10 text-slate-400 border border-slate-500/30'
                    }`}>
                      {isSpeaking ? (
                        isPaused ? <Pause className="h-3 w-3" /> : <Volume2 className="h-3 w-3" />
                      ) : (
                        <Volume2 className="h-3 w-3" />
                      )}
                      {isSpeaking ? (isPaused ? 'Paused' : 'Speaking') : 'Silent'}
                    </div>

                    {/* Global Pause/Play Controls (when speaking) */}
                    {isSpeaking && (
                      <button
                        onClick={isPaused ? resumeSpeaking : pauseSpeaking}
                        className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs transition-all ${
                          isPaused
                            ? 'bg-green-500/10 text-green-300 border border-green-500/30 hover:bg-green-500/20'
                            : 'bg-blue-500/10 text-blue-300 border border-blue-500/30 hover:bg-blue-500/20'
                        }`}
                        title={isPaused ? "Resume reading" : "Pause reading"}
                      >
                        {isPaused ? (
                          <>
                            <Play className="h-3 w-3" />
                            <span className="text-xs">Resume</span>
                          </>
                        ) : (
                          <>
                            <Pause className="h-3 w-3" />
                            <span className="text-xs">Pause</span>
                          </>
                        )}
                      </button>
                    )}

                    {/* Auto-Read Toggle */}
                    <button
                      onClick={() => setAutoReadResponses(!autoReadResponses)}
                      className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs transition-all ${
                        autoReadResponses
                          ? 'bg-green-500/10 text-green-300 border border-green-500/30'
                          : 'bg-slate-500/10 text-slate-400 border border-slate-500/30 hover:bg-slate-400/10'
                      }`}
                      title={autoReadResponses ? 'Auto-read enabled: AI responses will be spoken automatically' : 'Auto-read disabled: Click to enable automatic reading of AI responses'}
                    >
                      <Volume2 className="h-3 w-3" />
                      <span className="text-xs">Auto-Read {autoReadResponses ? 'ON' : 'OFF'}</span>
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}