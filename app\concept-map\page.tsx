"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import {
  Brain,
  Network,
  TrendingUp,
  Target,
  Lightbulb,
  BookOpen,
  Zap,
  Award,
  Play,
  Users,
  Clock,
  CheckCircle,
  Lock,
  Star,
  ArrowRight,
  Search,
  Filter,
  BarChart3,
  Compass,
  Map,
  Route,
  GraduationCap,
  Trophy,
  Flame,
  Calendar,
  Timer
} from 'lucide-react';

export default function ConceptMapPage() {
  const router = useRouter();
  const [selectedConcept, setSelectedConcept] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('map');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedPath, setSelectedPath] = useState<string | null>(null);
  const [userProgress, setUserProgress] = useState({
    totalConcepts: 15,
    masteredConcepts: 4,
    inProgressConcepts: 3,
    overallProgress: 47,
    currentStreak: 7,
    weeklyGoal: 5,
    weeklyProgress: 3
  });

  const conceptDetails = {
    'diversification': {
      title: 'Diversification',
      description: 'The practice of spreading investments across various assets to reduce risk.',
      category: 'Risk Management',
      difficulty: 2,
      estimatedTime: '30 minutes',
      prerequisites: [],
      mastery: 90,
      keyPoints: [
        'Reduces portfolio volatility by spreading risk',
        'Protects against specific asset or sector risks',
        'Improves risk-adjusted returns over time',
        'Core principle: "Don\'t put all eggs in one basket"'
      ],
      realWorldExample: 'Instead of investing $10,000 in one tech stock, spread it: $4,000 in tech stocks, $3,000 in bonds, $2,000 in real estate, $1,000 in commodities.',
      practicalSteps: [
        'Start with broad market index funds',
        'Add international exposure (20-30%)',
        'Include bonds for stability',
        'Consider REITs for real estate exposure'
      ],
      commonMistakes: [
        'Over-diversification (diminishing returns)',
        'Thinking you\'re diversified with multiple similar stocks',
        'Ignoring correlation between assets'
      ],
      nextSteps: ['Learn about correlation', 'Study asset allocation', 'Practice portfolio construction'],
      relatedConcepts: ['asset_allocation', 'portfolio_theory', 'risk_return'],
      resources: [
        { type: 'video', title: 'Diversification Explained', duration: '15 min' },
        { type: 'article', title: 'Building Your First Diversified Portfolio', duration: '10 min' },
        { type: 'quiz', title: 'Diversification Quiz', duration: '5 min' }
      ]
    },
    'asset_allocation': {
      title: 'Asset Allocation',
      description: 'Strategic distribution of investments across different asset classes.',
      category: 'Portfolio Management',
      difficulty: 3,
      estimatedTime: '45 minutes',
      prerequisites: ['diversification'],
      mastery: 70,
      keyPoints: [
        'Determines 90% of portfolio performance',
        'Should match investor goals and timeline',
        'Requires periodic rebalancing',
        'Age-based rules provide starting points'
      ],
      realWorldExample: 'A 30-year-old with $50,000 might allocate: 70% stocks ($35,000), 20% bonds ($10,000), 10% alternatives ($5,000).',
      practicalSteps: [
        'Determine your risk tolerance',
        'Set investment timeline and goals',
        'Choose target allocation percentages',
        'Implement with low-cost funds',
        'Rebalance quarterly or annually'
      ],
      commonMistakes: [
        'Changing allocation based on market emotions',
        'Ignoring rebalancing for years',
        'Not adjusting as you age'
      ],
      nextSteps: ['Understand rebalancing', 'Learn about strategic vs tactical allocation', 'Study lifecycle investing'],
      relatedConcepts: ['diversification', 'rebalancing', 'portfolio_theory'],
      resources: [
        { type: 'calculator', title: 'Asset Allocation Calculator', duration: '10 min' },
        { type: 'video', title: 'Age-Based Allocation Strategies', duration: '20 min' },
        { type: 'worksheet', title: 'Personal Allocation Planner', duration: '15 min' }
      ]
    },
    'sharpe_ratio': {
      title: 'Sharpe Ratio',
      description: 'Measures risk-adjusted return by comparing excess return to volatility.',
      category: 'Performance Analysis',
      difficulty: 6,
      estimatedTime: '60 minutes',
      prerequisites: ['portfolio_theory', 'risk_return'],
      mastery: 40,
      keyPoints: [
        'Formula: (Return - Risk-free rate) / Standard deviation',
        'Higher ratio indicates better risk-adjusted performance',
        'Useful for comparing different investments',
        'Considers both return and risk in one metric'
      ],
      realWorldExample: 'Fund A: 12% return, 15% volatility, Sharpe = 0.67. Fund B: 10% return, 8% volatility, Sharpe = 1.0. Fund B is better risk-adjusted.',
      practicalSteps: [
        'Calculate annual returns for your investments',
        'Find standard deviation of returns',
        'Subtract risk-free rate (Treasury bill rate)',
        'Divide by standard deviation',
        'Compare ratios across investments'
      ],
      commonMistakes: [
        'Using Sharpe ratio alone for decisions',
        'Not considering time periods',
        'Ignoring other risk metrics'
      ],
      nextSteps: ['Learn about other risk metrics', 'Study portfolio optimization', 'Practice calculation methods'],
      relatedConcepts: ['portfolio_theory', 'risk_return', 'var'],
      resources: [
        { type: 'calculator', title: 'Sharpe Ratio Calculator', duration: '5 min' },
        { type: 'tutorial', title: 'Step-by-Step Calculation', duration: '25 min' },
        { type: 'practice', title: 'Real Portfolio Analysis', duration: '30 min' }
      ]
    }
  };

  const learningPaths = [
    {
      id: 'beginner',
      title: 'Foundation Builder',
      description: 'Master the fundamentals of investing and financial planning',
      concepts: ['diversification', 'asset_allocation', 'risk_return', 'compound_interest', 'emergency_fund'],
      duration: '2-3 weeks',
      difficulty: 'Beginner',
      estimatedHours: 15,
      completionRate: 85,
      enrolled: 1247,
      rating: 4.8,
      outcomes: [
        'Understand basic investment principles',
        'Create your first diversified portfolio',
        'Set up automatic investing',
        'Build an emergency fund strategy'
      ],
      modules: [
        { name: 'Investment Basics', duration: '3 hours', status: 'completed' },
        { name: 'Diversification Deep Dive', duration: '4 hours', status: 'completed' },
        { name: 'Asset Allocation Strategy', duration: '4 hours', status: 'in-progress' },
        { name: 'Risk & Return', duration: '2 hours', status: 'locked' },
        { name: 'Building Your Portfolio', duration: '2 hours', status: 'locked' }
      ]
    },
    {
      id: 'intermediate',
      title: 'Portfolio Strategist',
      description: 'Advanced portfolio management and optimization techniques',
      concepts: ['portfolio_theory', 'sharpe_ratio', 'rebalancing', 'tax_optimization', 'factor_investing'],
      duration: '4-6 weeks',
      difficulty: 'Intermediate',
      estimatedHours: 25,
      completionRate: 72,
      enrolled: 892,
      rating: 4.7,
      outcomes: [
        'Optimize portfolio performance',
        'Implement tax-efficient strategies',
        'Master rebalancing techniques',
        'Understand factor-based investing'
      ],
      modules: [
        { name: 'Modern Portfolio Theory', duration: '5 hours', status: 'locked' },
        { name: 'Risk Metrics & Analysis', duration: '6 hours', status: 'locked' },
        { name: 'Rebalancing Strategies', duration: '4 hours', status: 'locked' },
        { name: 'Tax Optimization', duration: '5 hours', status: 'locked' },
        { name: 'Factor Investing', duration: '5 hours', status: 'locked' }
      ]
    },
    {
      id: 'advanced',
      title: 'Risk Master',
      description: 'Sophisticated risk analysis and institutional-level strategies',
      concepts: ['var', 'convexity', 'stress_testing', 'derivatives', 'alternative_investments'],
      duration: '6-8 weeks',
      difficulty: 'Advanced',
      estimatedHours: 40,
      completionRate: 58,
      enrolled: 324,
      rating: 4.9,
      outcomes: [
        'Master advanced risk metrics',
        'Implement institutional strategies',
        'Use derivatives for hedging',
        'Analyze alternative investments'
      ],
      modules: [
        { name: 'Value at Risk (VaR)', duration: '8 hours', status: 'locked' },
        { name: 'Bond Convexity & Duration', duration: '6 hours', status: 'locked' },
        { name: 'Stress Testing', duration: '8 hours', status: 'locked' },
        { name: 'Derivatives Strategies', duration: '10 hours', status: 'locked' },
        { name: 'Alternative Investments', duration: '8 hours', status: 'locked' }
      ]
    }
  ];

  const achievements = [
    {
      id: 'first_concept',
      title: 'First Steps',
      description: 'Mastered your first financial concept',
      icon: <BookOpen className="h-5 w-5" />,
      unlocked: true
    },
    {
      id: 'diversification_master',
      title: 'Diversification Expert',
      description: 'Achieved 90%+ mastery in diversification',
      icon: <Target className="h-5 w-5" />,
      unlocked: true
    },
    {
      id: 'connection_finder',
      title: 'Connection Finder',
      description: 'Discovered 5 concept relationships',
      icon: <Network className="h-5 w-5" />,
      unlocked: false
    },
    {
      id: 'advanced_learner',
      title: 'Advanced Learner',
      description: 'Unlocked advanced concepts',
      icon: <Brain className="h-5 w-5" />,
      unlocked: false
    }
  ];



  const selectedConceptData = selectedConcept ? conceptDetails[selectedConcept as keyof typeof conceptDetails] : null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-8">

          {/* Enhanced Header with Progress */}
          <div className="text-center space-y-6">
            <div className="space-y-2">
              <h1 className="text-4xl font-bold text-white flex items-center justify-center gap-3">
                <Map className="h-8 w-8 text-purple-400" />
                Financial Knowledge Map
              </h1>
              <p className="text-slate-300 max-w-3xl mx-auto text-lg">
                Your personalized learning journey through finance. Track progress, discover connections,
                and master concepts step by step.
              </p>
            </div>

            {/* Progress Overview */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-400">{userProgress.masteredConcepts}</div>
                  <div className="text-sm text-slate-400">Concepts Mastered</div>
                  <div className="text-xs text-slate-500">of {userProgress.totalConcepts}</div>
                </CardContent>
              </Card>
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-400">{userProgress.overallProgress}%</div>
                  <div className="text-sm text-slate-400">Overall Progress</div>
                  <Progress value={userProgress.overallProgress} className="h-1 mt-2" />
                </CardContent>
              </Card>
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-orange-400 flex items-center justify-center gap-1">
                    <Flame className="h-5 w-5" />
                    {userProgress.currentStreak}
                  </div>
                  <div className="text-sm text-slate-400">Day Streak</div>
                  <div className="text-xs text-slate-500">Keep it up!</div>
                </CardContent>
              </Card>
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-400">{userProgress.weeklyProgress}/{userProgress.weeklyGoal}</div>
                  <div className="text-sm text-slate-400">Weekly Goal</div>
                  <Progress value={(userProgress.weeklyProgress / userProgress.weeklyGoal) * 100} className="h-1 mt-2" />
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Main Tabbed Interface */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <div className="flex justify-center">
              <TabsList className="bg-slate-800/50 border-slate-700 p-1">
                <TabsTrigger value="map" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Knowledge Dashboard
                </TabsTrigger>
                <TabsTrigger value="paths" className="flex items-center gap-2">
                  <Route className="h-4 w-4" />
                  Learning Paths
                </TabsTrigger>
                <TabsTrigger value="progress" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Progress Tracker
                </TabsTrigger>
                <TabsTrigger value="explore" className="flex items-center gap-2">
                  <Compass className="h-4 w-4" />
                  Explore Concepts
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Knowledge Dashboard Tab */}
            <TabsContent value="map" className="space-y-6">
              <div className="text-center space-y-2 mb-6">
                <h2 className="text-2xl font-bold text-white">Knowledge Dashboard</h2>
                <p className="text-slate-300 max-w-2xl mx-auto">
                  Organize your learning with smart tools, track your progress, and plan your financial education journey.
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Learning Planner */}
                <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Calendar className="h-5 w-5 text-blue-400" />
                      Learning Planner
                    </CardTitle>
                    <CardDescription className="text-slate-400">
                      Plan and schedule your learning sessions
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* This Week's Plan */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-slate-300">This Week's Plan</h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 bg-slate-700/30 rounded-lg">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-400" />
                            <span className="text-sm text-slate-300">Diversification</span>
                          </div>
                          <span className="text-xs text-slate-500">Mon</span>
                        </div>
                        <div className="flex items-center justify-between p-2 bg-slate-700/30 rounded-lg">
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-400" />
                            <span className="text-sm text-slate-300">Asset Allocation</span>
                          </div>
                          <span className="text-xs text-slate-500">Wed</span>
                        </div>
                        <div className="flex items-center justify-between p-2 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-blue-400" />
                            <span className="text-sm text-blue-300">Portfolio Theory</span>
                          </div>
                          <span className="text-xs text-blue-400">Today</span>
                        </div>
                        <div className="flex items-center justify-between p-2 bg-slate-700/30 rounded-lg">
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-slate-500" />
                            <span className="text-sm text-slate-400">Risk Metrics</span>
                          </div>
                          <span className="text-xs text-slate-500">Fri</span>
                        </div>
                      </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="space-y-2">
                      <Button
                        className="w-full bg-blue-600 hover:bg-blue-700"
                        size="sm"
                        onClick={() => router.push('/chat?query=' + encodeURIComponent('Help me create a personalized weekly learning schedule'))}
                      >
                        <Calendar className="h-4 w-4 mr-2" />
                        Plan This Week
                      </Button>
                      <Button
                        variant="outline"
                        className="w-full border-slate-600"
                        size="sm"
                        onClick={() => router.push('/chat?query=' + encodeURIComponent('What should I study next based on my progress?'))}
                      >
                        <Lightbulb className="h-4 w-4 mr-2" />
                        Get Suggestions
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Knowledge Organizer */}
                <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <BookOpen className="h-5 w-5 text-purple-400" />
                      Knowledge Organizer
                    </CardTitle>
                    <CardDescription className="text-slate-400">
                      Organize concepts by categories and priorities
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Categories */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-slate-300">By Category</h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-2 bg-slate-700/30 rounded-lg">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                            <span className="text-sm text-slate-300">Investing</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-xs text-slate-400">4/6</span>
                            <Progress value={67} className="w-12 h-1" />
                          </div>
                        </div>
                        <div className="flex items-center justify-between p-2 bg-slate-700/30 rounded-lg">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                            <span className="text-sm text-slate-300">Risk Management</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-xs text-slate-400">2/5</span>
                            <Progress value={40} className="w-12 h-1" />
                          </div>
                        </div>
                        <div className="flex items-center justify-between p-2 bg-slate-700/30 rounded-lg">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 rounded-full bg-green-500"></div>
                            <span className="text-sm text-slate-300">Analysis</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <span className="text-xs text-slate-400">1/4</span>
                            <Progress value={25} className="w-12 h-1" />
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Priority Queue */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-slate-300">Priority Queue</h4>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 p-2 bg-red-500/10 border border-red-500/30 rounded-lg">
                          <Flame className="h-4 w-4 text-red-400" />
                          <span className="text-sm text-red-300">High: Portfolio Theory</span>
                        </div>
                        <div className="flex items-center gap-2 p-2 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                          <Clock className="h-4 w-4 text-yellow-400" />
                          <span className="text-sm text-yellow-300">Medium: Sharpe Ratio</span>
                        </div>
                        <div className="flex items-center gap-2 p-2 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                          <Target className="h-4 w-4 text-blue-400" />
                          <span className="text-sm text-blue-300">Low: Advanced Metrics</span>
                        </div>
                      </div>
                    </div>

                    <Button
                      variant="outline"
                      className="w-full border-slate-600"
                      size="sm"
                      onClick={() => setActiveTab('explore')}
                    >
                      <Filter className="h-4 w-4 mr-2" />
                      Organize Concepts
                    </Button>
                  </CardContent>
                </Card>

                {/* Study Tools */}
                <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Zap className="h-5 w-5 text-yellow-400" />
                      Study Tools
                    </CardTitle>
                    <CardDescription className="text-slate-400">
                      Smart tools to enhance your learning
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Study Session */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-slate-300">Quick Study Session</h4>
                      <div className="p-3 bg-slate-700/30 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm text-slate-300">Portfolio Theory</span>
                          <Badge variant="outline" className="text-xs border-blue-500/30 text-blue-300">
                            30 min
                          </Badge>
                        </div>
                        <Progress value={65} className="h-2 mb-2" />
                        <div className="text-xs text-slate-400">65% complete • 10 min remaining</div>
                      </div>
                    </div>

                    {/* Study Tools Grid */}
                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-slate-600 text-slate-300 h-auto p-3 flex flex-col items-center gap-1"
                        onClick={() => router.push('/chat?query=' + encodeURIComponent('Create a quiz on my current learning topics'))}
                      >
                        <Target className="h-4 w-4" />
                        <span className="text-xs">Quiz Me</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-slate-600 text-slate-300 h-auto p-3 flex flex-col items-center gap-1"
                        onClick={() => router.push('/chat?query=' + encodeURIComponent('Create flashcards for financial concepts'))}
                      >
                        <BookOpen className="h-4 w-4" />
                        <span className="text-xs">Flashcards</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-slate-600 text-slate-300 h-auto p-3 flex flex-col items-center gap-1"
                        onClick={() => router.push('/chat?query=' + encodeURIComponent('Explain concepts with real-world examples'))}
                      >
                        <Lightbulb className="h-4 w-4" />
                        <span className="text-xs">Examples</span>
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="border-slate-600 text-slate-300 h-auto p-3 flex flex-col items-center gap-1"
                        onClick={() => router.push('/chat?query=' + encodeURIComponent('Help me practice financial calculations'))}
                      >
                        <BarChart3 className="h-4 w-4" />
                        <span className="text-xs">Practice</span>
                      </Button>
                    </div>

                    {/* Study Stats */}
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-slate-300">Today's Stats</h4>
                      <div className="grid grid-cols-2 gap-2 text-center">
                        <div className="p-2 bg-slate-700/30 rounded-lg">
                          <div className="text-lg font-bold text-blue-400">45</div>
                          <div className="text-xs text-slate-400">Minutes</div>
                        </div>
                        <div className="p-2 bg-slate-700/30 rounded-lg">
                          <div className="text-lg font-bold text-green-400">3</div>
                          <div className="text-xs text-slate-400">Concepts</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Knowledge Connections */}
              <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Network className="h-5 w-5 text-purple-400" />
                    Knowledge Connections
                  </CardTitle>
                  <CardDescription className="text-slate-400">
                    See how concepts connect and build upon each other
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {/* Foundation Level */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-green-400 flex items-center gap-2">
                        <GraduationCap className="h-4 w-4" />
                        Foundation
                      </h4>
                      <div className="space-y-2">
                        <div className="p-2 bg-green-500/10 border border-green-500/30 rounded-lg">
                          <div className="text-sm text-green-300">Diversification</div>
                          <div className="text-xs text-green-400">✓ Mastered</div>
                        </div>
                        <div className="p-2 bg-green-500/10 border border-green-500/30 rounded-lg">
                          <div className="text-sm text-green-300">Risk & Return</div>
                          <div className="text-xs text-green-400">✓ Mastered</div>
                        </div>
                      </div>
                    </div>

                    {/* Intermediate Level */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-yellow-400 flex items-center gap-2">
                        <Target className="h-4 w-4" />
                        Intermediate
                      </h4>
                      <div className="space-y-2">
                        <div className="p-2 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                          <div className="text-sm text-blue-300">Asset Allocation</div>
                          <div className="text-xs text-blue-400">📚 In Progress</div>
                        </div>
                        <div className="p-2 bg-slate-700/30 border border-slate-600 rounded-lg">
                          <div className="text-sm text-slate-400">Portfolio Theory</div>
                          <div className="text-xs text-slate-500">🔒 Locked</div>
                        </div>
                      </div>
                    </div>

                    {/* Advanced Level */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-red-400 flex items-center gap-2">
                        <Trophy className="h-4 w-4" />
                        Advanced
                      </h4>
                      <div className="space-y-2">
                        <div className="p-2 bg-slate-700/30 border border-slate-600 rounded-lg">
                          <div className="text-sm text-slate-400">Sharpe Ratio</div>
                          <div className="text-xs text-slate-500">🔒 Locked</div>
                        </div>
                        <div className="p-2 bg-slate-700/30 border border-slate-600 rounded-lg">
                          <div className="text-sm text-slate-400">Value at Risk</div>
                          <div className="text-xs text-slate-500">🔒 Locked</div>
                        </div>
                      </div>
                    </div>

                    {/* Expert Level */}
                    <div className="space-y-3">
                      <h4 className="text-sm font-medium text-purple-400 flex items-center gap-2">
                        <Star className="h-4 w-4" />
                        Expert
                      </h4>
                      <div className="space-y-2">
                        <div className="p-2 bg-slate-700/30 border border-slate-600 rounded-lg">
                          <div className="text-sm text-slate-400">Derivatives</div>
                          <div className="text-xs text-slate-500">🔒 Locked</div>
                        </div>
                        <div className="p-2 bg-slate-700/30 border border-slate-600 rounded-lg">
                          <div className="text-sm text-slate-400">Alt Investments</div>
                          <div className="text-xs text-slate-500">🔒 Locked</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 text-center">
                    <Button
                      className="bg-purple-600 hover:bg-purple-700"
                      onClick={() => setActiveTab('paths')}
                    >
                      <Route className="h-4 w-4 mr-2" />
                      View Full Learning Paths
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Learning Paths Tab */}
            <TabsContent value="paths" className="space-y-6">
              <div className="text-center space-y-2">
                <h2 className="text-2xl font-bold text-white">Structured Learning Paths</h2>
                <p className="text-slate-300 max-w-2xl mx-auto">
                  Follow curated learning journeys designed by financial experts. Each path builds knowledge progressively.
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {learningPaths.map((path) => (
                  <Card key={path.id} className={`bg-slate-800/50 border-slate-700 backdrop-blur-sm transition-all hover:bg-slate-800/70 ${
                    selectedPath === path.id ? 'ring-2 ring-blue-500' : ''
                  }`}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className={`p-2 rounded-lg ${
                            path.difficulty === 'Beginner' ? 'bg-green-500/20 text-green-400' :
                            path.difficulty === 'Intermediate' ? 'bg-yellow-500/20 text-yellow-400' :
                            'bg-red-500/20 text-red-400'
                          }`}>
                            {path.difficulty === 'Beginner' ? <GraduationCap className="h-5 w-5" /> :
                             path.difficulty === 'Intermediate' ? <Target className="h-5 w-5" /> :
                             <Trophy className="h-5 w-5" />}
                          </div>
                          <Badge variant="outline" className={`${
                            path.difficulty === 'Beginner' ? 'border-green-500/30 text-green-300' :
                            path.difficulty === 'Intermediate' ? 'border-yellow-500/30 text-yellow-300' :
                            'border-red-500/30 text-red-300'
                          }`}>
                            {path.difficulty}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-1 text-yellow-400">
                          <Star className="h-4 w-4 fill-current" />
                          <span className="text-sm">{path.rating}</span>
                        </div>
                      </div>
                      <CardTitle className="text-white">{path.title}</CardTitle>
                      <CardDescription className="text-slate-400">
                        {path.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Path Stats */}
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                          <div className="text-lg font-bold text-white">{path.estimatedHours}h</div>
                          <div className="text-xs text-slate-400">Duration</div>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-white">{path.enrolled}</div>
                          <div className="text-xs text-slate-400">Enrolled</div>
                        </div>
                        <div>
                          <div className="text-lg font-bold text-white">{path.completionRate}%</div>
                          <div className="text-xs text-slate-400">Complete</div>
                        </div>
                      </div>

                      {/* Learning Outcomes */}
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-slate-300">What you'll learn:</h4>
                        <ul className="space-y-1">
                          {path.outcomes.slice(0, 3).map((outcome, index) => (
                            <li key={index} className="text-xs text-slate-400 flex items-start gap-2">
                              <CheckCircle className="h-3 w-3 text-green-400 mt-0.5 flex-shrink-0" />
                              {outcome}
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Module Progress */}
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-slate-300">Modules:</h4>
                        <div className="space-y-1">
                          {path.modules.slice(0, 3).map((module, index) => (
                            <div key={index} className="flex items-center justify-between text-xs">
                              <div className="flex items-center gap-2">
                                {module.status === 'completed' ? (
                                  <CheckCircle className="h-3 w-3 text-green-400" />
                                ) : module.status === 'in-progress' ? (
                                  <Clock className="h-3 w-3 text-yellow-400" />
                                ) : (
                                  <Lock className="h-3 w-3 text-slate-500" />
                                )}
                                <span className={`${
                                  module.status === 'completed' ? 'text-slate-300' :
                                  module.status === 'in-progress' ? 'text-yellow-300' :
                                  'text-slate-500'
                                }`}>
                                  {module.name}
                                </span>
                              </div>
                              <span className="text-slate-500">{module.duration}</span>
                            </div>
                          ))}
                          {path.modules.length > 3 && (
                            <div className="text-xs text-slate-500 text-center pt-1">
                              +{path.modules.length - 3} more modules
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="space-y-2 pt-2">
                        <Button
                          className="w-full bg-blue-600 hover:bg-blue-700"
                          onClick={() => {
                            setSelectedPath(path.id);
                            router.push('/learning?path=' + path.id);
                          }}
                        >
                          {path.id === 'beginner' && userProgress.masteredConcepts > 0 ? 'Continue Path' : 'Start Path'}
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full border-slate-600 text-slate-300"
                          onClick={() => setSelectedPath(selectedPath === path.id ? null : path.id)}
                        >
                          {selectedPath === path.id ? 'Hide Details' : 'View Details'}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Progress Tracker Tab */}
            <TabsContent value="progress" className="space-y-6">
              <div className="text-center space-y-2">
                <h2 className="text-2xl font-bold text-white">Your Learning Progress</h2>
                <p className="text-slate-300 max-w-2xl mx-auto">
                  Track your mastery across all financial concepts and see your learning journey.
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Progress Overview */}
                <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <BarChart3 className="h-5 w-5 text-blue-400" />
                      Overall Progress
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="text-center">
                      <div className="text-4xl font-bold text-blue-400 mb-2">{userProgress.overallProgress}%</div>
                      <div className="text-slate-300">Knowledge Mastery</div>
                      <Progress value={userProgress.overallProgress} className="h-3 mt-3" />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-slate-700/30 rounded-lg">
                        <div className="text-xl font-bold text-green-400">{userProgress.masteredConcepts}</div>
                        <div className="text-sm text-slate-400">Mastered</div>
                      </div>
                      <div className="text-center p-3 bg-slate-700/30 rounded-lg">
                        <div className="text-xl font-bold text-yellow-400">{userProgress.inProgressConcepts}</div>
                        <div className="text-sm text-slate-400">In Progress</div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-slate-300">Current Streak</span>
                        <div className="flex items-center gap-1 text-orange-400">
                          <Flame className="h-4 w-4" />
                          <span className="font-bold">{userProgress.currentStreak} days</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-slate-300">Weekly Goal</span>
                        <span className="text-white">{userProgress.weeklyProgress}/{userProgress.weeklyGoal} concepts</span>
                      </div>
                      <Progress value={(userProgress.weeklyProgress / userProgress.weeklyGoal) * 100} className="h-2" />
                    </div>
                  </CardContent>
                </Card>

                {/* Achievements */}
                <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Trophy className="h-5 w-5 text-yellow-400" />
                      Achievements
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {achievements.map((achievement) => (
                      <div key={achievement.id} className={`flex items-center gap-3 p-3 rounded-lg transition-all ${
                        achievement.unlocked
                          ? 'bg-yellow-500/10 border border-yellow-500/30'
                          : 'bg-slate-700/30 border border-slate-600/30'
                      }`}>
                        <div className={`p-2 rounded-lg ${
                          achievement.unlocked ? 'bg-yellow-500/20 text-yellow-400' : 'bg-slate-600/50 text-slate-500'
                        }`}>
                          {achievement.icon}
                        </div>
                        <div className="flex-1">
                          <div className={`text-sm font-medium ${
                            achievement.unlocked ? 'text-white' : 'text-slate-400'
                          }`}>
                            {achievement.title}
                          </div>
                          <div className="text-xs text-slate-500">
                            {achievement.description}
                          </div>
                        </div>
                        {achievement.unlocked && (
                          <CheckCircle className="h-5 w-5 text-green-400" />
                        )}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            {/* Explore Concepts Tab */}
            <TabsContent value="explore" className="space-y-6">
              <div className="text-center space-y-2">
                <h2 className="text-2xl font-bold text-white">Explore Financial Concepts</h2>
                <p className="text-slate-300 max-w-2xl mx-auto">
                  Browse and search through all available financial concepts. Find what interests you most.
                </p>
              </div>

              {/* Search and Filter */}
              <div className="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                  <Input
                    placeholder="Search concepts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-slate-800/50 border-slate-600 text-white placeholder-slate-400"
                  />
                </div>
                <Button variant="outline" className="border-slate-600 text-slate-300">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </div>

              {/* Concept Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {Object.entries(conceptDetails)
                  .filter(([_, concept]) =>
                    !searchTerm ||
                    concept.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    concept.category.toLowerCase().includes(searchTerm.toLowerCase())
                  )
                  .map(([key, concept]) => (
                  <Card key={key} className="bg-slate-800/50 border-slate-700 backdrop-blur-sm hover:bg-slate-800/70 transition-all cursor-pointer"
                        onClick={() => setSelectedConcept(key)}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <Badge variant="outline" className="text-xs border-slate-500 text-slate-300">
                          {concept.category}
                        </Badge>
                        <div className="flex items-center gap-1">
                          <div className={`w-3 h-3 rounded-full ${
                            concept.mastery >= 80 ? 'bg-green-500' :
                            concept.mastery >= 40 ? 'bg-yellow-500' :
                            concept.mastery > 0 ? 'bg-blue-500' : 'bg-gray-500'
                          }`}></div>
                          <span className="text-xs text-slate-400">{concept.mastery}%</span>
                        </div>
                      </div>
                      <CardTitle className="text-white text-lg">{concept.title}</CardTitle>
                      <CardDescription className="text-slate-400 text-sm">
                        {concept.description}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-1 text-slate-400">
                          <Timer className="h-3 w-3" />
                          {concept.estimatedTime}
                        </div>
                        <div className="flex items-center gap-1 text-slate-400">
                          <Target className="h-3 w-3" />
                          Level {concept.difficulty}
                        </div>
                      </div>

                      <Progress value={concept.mastery} className="h-2" />

                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          className="flex-1 bg-blue-600 hover:bg-blue-700"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push('/learning?topic=' + key);
                          }}
                        >
                          <BookOpen className="h-3 w-3 mr-1" />
                          Study
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          className="border-slate-600"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push('/chat?query=' + encodeURIComponent(`Explain ${concept.title} with examples`));
                          }}
                        >
                          <Brain className="h-3 w-3 mr-1" />
                          Ask AI
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>

          {/* Enhanced Detailed Concept Information */}
          {selectedConceptData && (
            <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-white flex items-center gap-2">
                      <Brain className="h-6 w-6 text-purple-400" />
                      {selectedConceptData.title}
                    </CardTitle>
                    <CardDescription className="text-slate-400 mt-2">
                      {selectedConceptData.description}
                    </CardDescription>
                  </div>
                  <div className="flex flex-col items-end gap-2">
                    <Badge variant="outline" className="border-slate-500 text-slate-300">
                      {selectedConceptData.category}
                    </Badge>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-blue-400">{selectedConceptData.mastery}%</div>
                      <div className="text-xs text-slate-400">Mastery</div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="overview" className="space-y-4">
                  <TabsList className="bg-slate-700 border-slate-600">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="example">Real Example</TabsTrigger>
                    <TabsTrigger value="practice">Practice</TabsTrigger>
                    <TabsTrigger value="resources">Resources</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div>
                          <h4 className="text-lg font-semibold text-white mb-3">Key Points</h4>
                          <ul className="space-y-2">
                            {selectedConceptData.keyPoints.map((point, index) => (
                              <li key={index} className="text-slate-300 flex items-start gap-2">
                                <CheckCircle className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" />
                                {point}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h4 className="text-lg font-semibold text-white mb-3">Prerequisites</h4>
                          {selectedConceptData.prerequisites.length > 0 ? (
                            <div className="space-y-2">
                              {selectedConceptData.prerequisites.map((prereq, index) => (
                                <div key={index} className="flex items-center gap-2 text-sm text-slate-400">
                                  <ArrowRight className="h-3 w-3" />
                                  {prereq}
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-slate-400 text-sm">No prerequisites required</p>
                          )}
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <h4 className="text-lg font-semibold text-white mb-3">Learning Info</h4>
                          <div className="space-y-3">
                            <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                              <div className="flex items-center gap-2">
                                <Timer className="h-4 w-4 text-blue-400" />
                                <span className="text-slate-300">Estimated Time</span>
                              </div>
                              <span className="text-white">{selectedConceptData.estimatedTime}</span>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-slate-700/30 rounded-lg">
                              <div className="flex items-center gap-2">
                                <Target className="h-4 w-4 text-yellow-400" />
                                <span className="text-slate-300">Difficulty</span>
                              </div>
                              <span className="text-white">Level {selectedConceptData.difficulty}</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="text-lg font-semibold text-white mb-3">Common Mistakes</h4>
                          <ul className="space-y-2">
                            {selectedConceptData.commonMistakes.map((mistake, index) => (
                              <li key={index} className="text-slate-300 flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                                {mistake}
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="example" className="space-y-4">
                    <div className="bg-slate-700/50 rounded-lg p-6">
                      <h4 className="text-lg font-semibold text-white mb-4">Real-World Example</h4>
                      <p className="text-slate-300 mb-4">{selectedConceptData.realWorldExample}</p>

                      <h5 className="text-md font-semibold text-white mb-3">Practical Steps</h5>
                      <div className="space-y-2">
                        {selectedConceptData.practicalSteps.map((step, index) => (
                          <div key={index} className="flex items-start gap-3 p-3 bg-slate-800/50 rounded-lg">
                            <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold flex-shrink-0">
                              {index + 1}
                            </div>
                            <span className="text-slate-300">{step}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="practice" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Card className="bg-slate-700/30 border-slate-600">
                        <CardHeader>
                          <CardTitle className="text-white text-lg">Next Steps</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          {selectedConceptData.nextSteps.map((step, index) => (
                            <div key={index} className="flex items-center gap-3 p-3 bg-slate-800/50 rounded-lg">
                              <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                                {index + 1}
                              </div>
                              <span className="text-slate-300">{step}</span>
                            </div>
                          ))}
                        </CardContent>
                      </Card>

                      <Card className="bg-slate-700/30 border-slate-600">
                        <CardHeader>
                          <CardTitle className="text-white text-lg">Quick Actions</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <Button
                            className="w-full bg-blue-600 hover:bg-blue-700"
                            onClick={() => router.push('/learning?topic=' + selectedConcept)}
                          >
                            <BookOpen className="h-4 w-4 mr-2" />
                            Start Learning
                          </Button>
                          <Button
                            variant="outline"
                            className="w-full border-slate-600"
                            onClick={() => router.push('/chat?query=' + encodeURIComponent(`Give me a quiz on ${selectedConceptData.title}`))}
                          >
                            <Target className="h-4 w-4 mr-2" />
                            Take Quiz
                          </Button>
                          <Button
                            variant="outline"
                            className="w-full border-slate-600"
                            onClick={() => router.push('/chat?query=' + encodeURIComponent(`Explain ${selectedConceptData.title} with more examples`))}
                          >
                            <Brain className="h-4 w-4 mr-2" />
                            Ask AI
                          </Button>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>

                  <TabsContent value="resources" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {selectedConceptData.resources.map((resource, index) => (
                        <Card key={index} className="bg-slate-700/30 border-slate-600 hover:bg-slate-700/50 transition-all cursor-pointer">
                          <CardContent className="p-4">
                            <div className="flex items-center gap-3 mb-2">
                              <div className={`p-2 rounded-lg ${
                                resource.type === 'video' ? 'bg-red-500/20 text-red-400' :
                                resource.type === 'article' ? 'bg-blue-500/20 text-blue-400' :
                                resource.type === 'quiz' ? 'bg-green-500/20 text-green-400' :
                                resource.type === 'calculator' ? 'bg-purple-500/20 text-purple-400' :
                                'bg-yellow-500/20 text-yellow-400'
                              }`}>
                                {resource.type === 'video' ? <Play className="h-4 w-4" /> :
                                 resource.type === 'article' ? <BookOpen className="h-4 w-4" /> :
                                 resource.type === 'quiz' ? <Target className="h-4 w-4" /> :
                                 resource.type === 'calculator' ? <BarChart3 className="h-4 w-4" /> :
                                 <BookOpen className="h-4 w-4" />}
                              </div>
                              <Badge variant="outline" className="text-xs border-slate-500 text-slate-300 capitalize">
                                {resource.type}
                              </Badge>
                            </div>
                            <h5 className="text-white font-medium mb-1">{resource.title}</h5>
                            <div className="flex items-center gap-1 text-xs text-slate-400">
                              <Clock className="h-3 w-3" />
                              {resource.duration}
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}