import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import Navigation from '@/components/Navigation';
import AuthSessionProvider from '@/components/providers/session-provider';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'AI Financial Strategist - Your Personal Finance Assistant',
  description: 'AI-powered personal finance strategist with portfolio simulation, risk analysis, and adaptive learning',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <AuthSessionProvider>
          <Navigation />
          {children}
        </AuthSessionProvider>
      </body>
    </html>
  );
}