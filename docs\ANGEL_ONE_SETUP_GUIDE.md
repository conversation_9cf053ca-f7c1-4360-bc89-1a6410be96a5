# Angel One API Setup Guide

## The TOTP Issue Explained

The error you're seeing (`Invalid totp`) occurs because Angel One requires Two-Factor Authentication (2FA/TOTP) for API access. Here's how to resolve it:

## Solution Options

### Option 1: Disable 2FA (Recommended for Testing)

**Steps to disable 2FA in Angel One:**

1. **Login to Angel One App/Website**
   - Open Angel One mobile app or website
   - Login with your credentials

2. **Navigate to Security Settings**
   - Go to Profile → Security Settings
   - Find "Two-Factor Authentication" or "TOTP" section

3. **Disable 2FA/TOTP**
   - Turn off Two-Factor Authentication
   - Confirm the action

4. **Test API Access**
   - Your API should now work without TOTP

### Option 2: Get API Credentials Without 2FA

**Contact Angel One Support:**

1. **Call Angel One Customer Care**
   - Phone: 040-47 47 47 47
   - Email: <EMAIL>

2. **Request API Access**
   - Mention you need API access for portfolio monitoring
   - Ask for credentials that don't require 2FA
   - Explain it's for read-only portfolio access

3. **Alternative: SmartAPI Registration**
   - Visit: https://smartapi.angelbroking.com/
   - Register specifically for API access
   - These accounts often have different 2FA requirements

### Option 3: Use TOTP (Advanced)

If you want to keep 2FA enabled, you'll need to:

1. **Get Your TOTP Secret**
   - When setting up 2FA, Angel One shows a QR code
   - The QR code contains a secret key
   - You need this secret to generate TOTP codes programmatically

2. **Install Required Dependencies**
   ```bash
   npm install otplib base32
   ```

3. **Update Environment Variables**
   ```env
   ANGEL_ONE_API_KEY=your_api_key
   ANGEL_ONE_CLIENT_CODE=your_client_code
   ANGEL_ONE_PASSWORD=your_password
   ANGEL_ONE_TOTP_SECRET=your_totp_secret_here
   ```

## Current Implementation Status

### What's Working Now:
- ✅ **Error Detection**: System detects TOTP requirement
- ✅ **Helpful Error Messages**: Clear explanation of the issue
- ✅ **Fallback to Demo Data**: App continues working with mock data
- ✅ **Multiple Solutions**: Different approaches for different users

### Error Messages You Might See:

#### "TOTP_REQUIRED"
```
Your Angel One account has 2FA enabled. 
Please disable 2FA in your Angel One account settings, 
or contact support for API access without 2FA.
```
**Solution**: Disable 2FA in Angel One settings

#### "INVALID_CREDENTIALS"
```
Please verify your Angel One API key, 
client code, and password are correct.
```
**Solution**: Double-check your credentials

## Step-by-Step Setup

### Step 1: Get Angel One API Credentials

1. **Visit**: https://smartapi.angelbroking.com/
2. **Register**: Create developer account
3. **Get Credentials**:
   - API Key
   - Client Code (your trading account ID)
   - Password (your trading password)

### Step 2: Configure Environment Variables

Create `.env.local` file:
```env
# Angel One SmartAPI (without 2FA)
ANGEL_ONE_API_KEY=your_api_key_here
ANGEL_ONE_CLIENT_CODE=your_client_code_here
ANGEL_ONE_PASSWORD=your_password_here

# Optional: If you have TOTP enabled
ANGEL_ONE_TOTP_SECRET=your_totp_secret_here
```

### Step 3: Test Connection

1. **Start Development Server**:
   ```bash
   npm run dev
   ```

2. **Navigate to Portfolio**: http://localhost:3000/portfolio

3. **Toggle Real API**: Switch to "Real API" mode

4. **Select Angel One**: Click on Angel One broker card

5. **Connect**: Click "Connect to Angel One"

### Step 4: Troubleshooting

#### If you see "TOTP_REQUIRED":
1. Disable 2FA in Angel One account
2. Or contact Angel One support
3. Or provide TOTP secret in environment variables

#### If you see "INVALID_CREDENTIALS":
1. Verify API key is correct
2. Check client code (should be your trading account ID)
3. Confirm password is your trading password
4. Ensure account is active and has API access

## Alternative: Use Demo Mode

If you can't get real API access working immediately:

1. **Keep Demo Mode**: Toggle stays on "Demo Mode"
2. **Realistic Data**: System shows realistic portfolio data
3. **Full Functionality**: All features work with mock data
4. **No Setup Required**: Works immediately without credentials

## Production Considerations

### Security Best Practices:
- Never commit API credentials to version control
- Use environment variables for all sensitive data
- Rotate API keys regularly
- Monitor API usage and logs

### Rate Limiting:
- Angel One has API rate limits
- Implement caching to reduce API calls
- Use websockets for real-time data when available

### Error Handling:
- Always have fallback to cached/demo data
- Implement retry logic with exponential backoff
- Log errors for debugging but don't expose credentials

## Support Resources

### Angel One Support:
- **Website**: https://smartapi.angelbroking.com/
- **Documentation**: https://smartapi.angelbroking.com/docs
- **Support Email**: <EMAIL>
- **Phone**: 040-47 47 47 47

### Common Issues Forum:
- **GitHub Issues**: Report integration bugs
- **Stack Overflow**: Search for Angel One API issues
- **Angel One Developer Community**: Join their Telegram/Discord

## Next Steps

1. **Try Option 1**: Disable 2FA (easiest)
2. **Contact Support**: If Option 1 doesn't work
3. **Use Demo Mode**: While setting up real API
4. **Test Thoroughly**: Verify all features work
5. **Deploy Securely**: Use proper environment variables

## Quick Test Script

You can test your credentials directly:

```javascript
// test-angel-one.js
const { AngelOneSimpleAPI } = require('./lib/brokers/angelone-simple');

const angelOne = new AngelOneSimpleAPI({
  apiKey: 'your_api_key',
  clientCode: 'your_client_code',
  password: 'your_password'
});

angelOne.testConnection()
  .then(() => console.log('✅ Connection successful!'))
  .catch(error => console.error('❌ Connection failed:', error.message));
```

Run with: `node test-angel-one.js`
