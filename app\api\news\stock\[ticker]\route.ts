import { NextRequest, NextResponse } from 'next/server';

// News API configuration
const NEWS_API_KEY = process.env.NEWS_API_KEY || process.env.NEXT_PUBLIC_NEWS_API_KEY || '';
const NEWS_API_BASE_URL = 'https://newsapi.org/v2';

// Alpha Vantage News API as backup
const ALPHA_VANTAGE_API_KEY = process.env.ALPHA_VANTAGE_API_KEY || '';
const ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query';

interface NewsArticle {
  title: string;
  description: string;
  url: string;
  source: string;
  publishedAt: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  relevanceScore: number;
  summary?: string;
}

interface NewsResponse {
  symbol: string;
  articles: NewsArticle[];
  sentiment: {
    overall: 'positive' | 'negative' | 'neutral';
    score: number;
    distribution: {
      positive: number;
      negative: number;
      neutral: number;
    };
  };
  keyTopics: string[];
}

export async function GET(
  request: NextRequest,
  { params }: { params: { ticker: string } }
) {
  try {
    const ticker = params.ticker.toUpperCase();
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    
    // Try to fetch from News API first
    let articles = await fetchFromNewsAPI(ticker, limit);
    
    // If News API fails, try Alpha Vantage
    if (!articles || articles.length === 0) {
      articles = await fetchFromAlphaVantageNews(ticker, limit);
    }
    
    // If both fail, return mock data
    if (!articles || articles.length === 0) {
      articles = generateMockNews(ticker, limit);
    }
    
    // Analyze sentiment and extract key topics
    const sentiment = analyzeSentiment(articles);
    const keyTopics = extractKeyTopics(articles, ticker);
    
    const response: NewsResponse = {
      symbol: ticker,
      articles: articles,
      sentiment: sentiment,
      keyTopics: keyTopics
    };
    
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error fetching news:', error);
    
    // Return mock data as fallback
    const mockArticles = generateMockNews(params.ticker.toUpperCase(), 10);
    const response: NewsResponse = {
      symbol: params.ticker.toUpperCase(),
      articles: mockArticles,
      sentiment: {
        overall: 'positive',
        score: 0.65,
        distribution: { positive: 60, negative: 20, neutral: 20 }
      },
      keyTopics: ['Earnings', 'Growth', 'Market Performance']
    };
    
    return NextResponse.json(response);
  }
}

async function fetchFromNewsAPI(ticker: string, limit: number): Promise<NewsArticle[]> {
  try {
    if (!NEWS_API_KEY) {
      throw new Error('News API key not configured');
    }
    
    const query = `${ticker} stock OR "${ticker}" earnings OR "${ticker}" financial`;
    const url = `${NEWS_API_BASE_URL}/everything?q=${encodeURIComponent(query)}&sortBy=publishedAt&pageSize=${limit}&apiKey=${NEWS_API_KEY}`;
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error('News API request failed');
    }
    
    const data = await response.json();
    
    if (!data.articles || data.articles.length === 0) {
      throw new Error('No articles found');
    }
    
    return data.articles.map((article: any) => ({
      title: article.title || 'No title',
      description: article.description || 'No description available',
      url: article.url || '',
      source: article.source?.name || 'Unknown',
      publishedAt: article.publishedAt || new Date().toISOString(),
      sentiment: analyzeSentimentSimple(article.title + ' ' + article.description),
      relevanceScore: calculateRelevanceScore(article, ticker),
      summary: article.description?.substring(0, 150) + '...'
    }));
  } catch (error) {
    console.error('News API fetch error:', error);
    return [];
  }
}

async function fetchFromAlphaVantageNews(ticker: string, limit: number): Promise<NewsArticle[]> {
  try {
    if (!ALPHA_VANTAGE_API_KEY) {
      throw new Error('Alpha Vantage API key not configured');
    }
    
    const url = `${ALPHA_VANTAGE_BASE_URL}?function=NEWS_SENTIMENT&tickers=${ticker}&limit=${limit}&apikey=${ALPHA_VANTAGE_API_KEY}`;
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error('Alpha Vantage News API request failed');
    }
    
    const data = await response.json();
    
    if (!data.feed || data.feed.length === 0) {
      throw new Error('No news feed available');
    }
    
    return data.feed.map((article: any) => ({
      title: article.title || 'No title',
      description: article.summary || 'No description available',
      url: article.url || '',
      source: article.source || 'Unknown',
      publishedAt: article.time_published || new Date().toISOString(),
      sentiment: mapAlphaVantageSentiment(article.overall_sentiment_label),
      relevanceScore: parseFloat(article.relevance_score || '0.5'),
      summary: article.summary?.substring(0, 150) + '...'
    }));
  } catch (error) {
    console.error('Alpha Vantage News fetch error:', error);
    return [];
  }
}

function generateMockNews(ticker: string, limit: number): NewsArticle[] {
  const mockArticles: NewsArticle[] = [
    {
      title: `${ticker} Reports Strong Q3 Earnings, Beats Expectations`,
      description: `${ticker} Corporation announced quarterly earnings that exceeded analyst expectations, driven by strong revenue growth and improved margins.`,
      url: `https://example.com/news/${ticker.toLowerCase()}-earnings`,
      source: 'Financial Times',
      publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      sentiment: 'positive',
      relevanceScore: 0.95,
      summary: `${ticker} beats earnings expectations with strong quarterly performance...`
    },
    {
      title: `Analyst Upgrades ${ticker} to Buy Rating`,
      description: `Goldman Sachs upgraded ${ticker} to a Buy rating, citing strong fundamentals and growth prospects in the current market environment.`,
      url: `https://example.com/news/${ticker.toLowerCase()}-upgrade`,
      source: 'Reuters',
      publishedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
      sentiment: 'positive',
      relevanceScore: 0.88,
      summary: `Goldman Sachs sees strong upside potential for ${ticker}...`
    },
    {
      title: `${ticker} Announces New Product Line Expansion`,
      description: `The company revealed plans to expand its product portfolio with innovative solutions targeting emerging markets and new customer segments.`,
      url: `https://example.com/news/${ticker.toLowerCase()}-expansion`,
      source: 'Bloomberg',
      publishedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
      sentiment: 'positive',
      relevanceScore: 0.82,
      summary: `${ticker} expands product line to capture new market opportunities...`
    },
    {
      title: `Market Volatility Affects ${ticker} Trading Volume`,
      description: `Recent market uncertainty has led to increased trading volume in ${ticker} shares as investors reassess their positions.`,
      url: `https://example.com/news/${ticker.toLowerCase()}-volatility`,
      source: 'MarketWatch',
      publishedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
      sentiment: 'neutral',
      relevanceScore: 0.65,
      summary: `Market conditions create trading opportunities in ${ticker}...`
    }
  ];
  
  return mockArticles.slice(0, limit);
}

function analyzeSentimentSimple(text: string): 'positive' | 'negative' | 'neutral' {
  const positiveWords = ['beat', 'strong', 'growth', 'upgrade', 'buy', 'positive', 'gain', 'rise', 'increase', 'profit'];
  const negativeWords = ['miss', 'weak', 'decline', 'downgrade', 'sell', 'negative', 'loss', 'fall', 'decrease', 'concern'];
  
  const lowerText = text.toLowerCase();
  const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
  const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;
  
  if (positiveCount > negativeCount) return 'positive';
  if (negativeCount > positiveCount) return 'negative';
  return 'neutral';
}

function calculateRelevanceScore(article: any, ticker: string): number {
  const title = (article.title || '').toLowerCase();
  const description = (article.description || '').toLowerCase();
  const tickerLower = ticker.toLowerCase();
  
  let score = 0.5; // Base score
  
  // Higher score if ticker is in title
  if (title.includes(tickerLower)) score += 0.3;
  
  // Medium score if ticker is in description
  if (description.includes(tickerLower)) score += 0.2;
  
  // Check for financial keywords
  const financialKeywords = ['earnings', 'revenue', 'profit', 'stock', 'shares', 'analyst', 'rating'];
  const keywordCount = financialKeywords.filter(keyword => 
    title.includes(keyword) || description.includes(keyword)
  ).length;
  
  score += keywordCount * 0.05;
  
  return Math.min(score, 1.0);
}

function mapAlphaVantageSentiment(label: string): 'positive' | 'negative' | 'neutral' {
  switch (label?.toLowerCase()) {
    case 'bullish':
    case 'somewhat-bullish':
      return 'positive';
    case 'bearish':
    case 'somewhat-bearish':
      return 'negative';
    default:
      return 'neutral';
  }
}

function analyzeSentiment(articles: NewsArticle[]) {
  if (articles.length === 0) {
    return {
      overall: 'neutral' as const,
      score: 0.5,
      distribution: { positive: 0, negative: 0, neutral: 100 }
    };
  }
  
  const sentimentCounts = articles.reduce((acc, article) => {
    acc[article.sentiment]++;
    return acc;
  }, { positive: 0, negative: 0, neutral: 0 });
  
  const total = articles.length;
  const distribution = {
    positive: Math.round((sentimentCounts.positive / total) * 100),
    negative: Math.round((sentimentCounts.negative / total) * 100),
    neutral: Math.round((sentimentCounts.neutral / total) * 100)
  };
  
  // Calculate overall sentiment
  let overall: 'positive' | 'negative' | 'neutral' = 'neutral';
  let score = 0.5;
  
  if (sentimentCounts.positive > sentimentCounts.negative) {
    overall = 'positive';
    score = 0.5 + (sentimentCounts.positive / total) * 0.5;
  } else if (sentimentCounts.negative > sentimentCounts.positive) {
    overall = 'negative';
    score = 0.5 - (sentimentCounts.negative / total) * 0.5;
  }
  
  return {
    overall,
    score: parseFloat(score.toFixed(2)),
    distribution
  };
}

function extractKeyTopics(articles: NewsArticle[], ticker: string): string[] {
  const topics = new Map<string, number>();
  
  const keywords = [
    'earnings', 'revenue', 'profit', 'growth', 'expansion', 'acquisition',
    'product', 'market', 'competition', 'regulation', 'technology', 'innovation',
    'partnership', 'investment', 'dividend', 'buyback', 'guidance', 'outlook'
  ];
  
  articles.forEach(article => {
    const text = (article.title + ' ' + article.description).toLowerCase();
    keywords.forEach(keyword => {
      if (text.includes(keyword)) {
        topics.set(keyword, (topics.get(keyword) || 0) + 1);
      }
    });
  });
  
  // Return top 5 topics
  return Array.from(topics.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([topic]) => topic.charAt(0).toUpperCase() + topic.slice(1));
}
