// Market Correlation and Risk Assessment Engine

import { NewsArticle } from '../news/types';
import { 
  MarketCorrelationMatrix, 
  CorrelationData, 
  RiskAssessment, 
  RiskFactor, 
  VolatilityMetrics,
  VolatilityData,
  LiveMarketData 
} from './types';

export class CorrelationRiskEngine {
  private correlationHistory = new Map<string, number[]>();
  private volatilityHistory = new Map<string, number[]>();
  private riskFactorCache = new Map<string, RiskFactor[]>();

  /**
   * Calculate comprehensive market correlations
   */
  async calculateMarketCorrelations(
    liveData: LiveMarketData,
    articles: NewsArticle[]
  ): Promise<MarketCorrelationMatrix> {
    try {
      // Get price data for correlation calculation
      const priceData = this.extractPriceData(liveData);
      
      // Calculate correlations across different timeframes
      const correlations = await Promise.all([
        this.calculatePairwiseCorrelation('indian', 'foreign', priceData),
        this.calculatePairwiseCorrelation('indian', 'crypto', priceData),
        this.calculatePairwiseCorrelation('foreign', 'crypto', priceData)
      ]);

      // Calculate sector correlations
      const sectorCorrelations = await this.calculateSectorCorrelations(liveData);

      // Calculate timeframed correlations
      const timeframedCorrelations = await this.calculateTimeframedCorrelations(priceData);

      return {
        indianForeign: correlations[0],
        indianCrypto: correlations[1],
        foreignCrypto: correlations[2],
        sectorCorrelations,
        timeframedCorrelations
      };
    } catch (error) {
      console.error('Error calculating correlations:', error);
      return this.getFallbackCorrelations();
    }
  }

  /**
   * Calculate pairwise correlation between two markets
   */
  private async calculatePairwiseCorrelation(
    market1: string,
    market2: string,
    priceData: Map<string, number[]>
  ): Promise<CorrelationData> {
    const data1 = priceData.get(market1) || [];
    const data2 = priceData.get(market2) || [];

    if (data1.length < 2 || data2.length < 2) {
      return this.getDefaultCorrelation();
    }

    // Calculate Pearson correlation coefficient
    const correlation = this.calculatePearsonCorrelation(data1, data2);
    
    // Determine correlation strength and direction
    const strength = this.classifyCorrelationStrength(Math.abs(correlation));
    const direction = correlation >= 0 ? 'positive' : 'negative';
    
    // Calculate reliability based on data quality and sample size
    const reliability = this.calculateCorrelationReliability(data1, data2);
    
    // Update historical trend
    this.updateCorrelationHistory(`${market1}_${market2}`, correlation);
    const historicalTrend = this.correlationHistory.get(`${market1}_${market2}`) || [];

    return {
      coefficient: correlation,
      strength,
      direction,
      reliability,
      lastUpdated: new Date(),
      historicalTrend: historicalTrend.slice(-20) // Keep last 20 data points
    };
  }

  /**
   * Calculate Pearson correlation coefficient
   */
  private calculatePearsonCorrelation(x: number[], y: number[]): number {
    const n = Math.min(x.length, y.length);
    if (n < 2) return 0;

    const xSlice = x.slice(-n);
    const ySlice = y.slice(-n);

    const sumX = xSlice.reduce((a, b) => a + b, 0);
    const sumY = ySlice.reduce((a, b) => a + b, 0);
    const sumXY = xSlice.reduce((acc, xi, i) => acc + xi * ySlice[i], 0);
    const sumX2 = xSlice.reduce((acc, xi) => acc + xi * xi, 0);
    const sumY2 = ySlice.reduce((acc, yi) => acc + yi * yi, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));

    return denominator === 0 ? 0 : numerator / denominator;
  }

  /**
   * Assess comprehensive market risk
   */
  async assessMarketRisk(
    liveData: LiveMarketData,
    articles: NewsArticle[],
    correlations: MarketCorrelationMatrix,
    volatility: VolatilityMetrics
  ): Promise<RiskAssessment> {
    try {
      // Identify risk factors from multiple sources
      const riskFactors = await this.identifyRiskFactors(articles, liveData, volatility);
      
      // Calculate overall risk score
      const riskScore = this.calculateOverallRiskScore(riskFactors, correlations, volatility);
      
      // Classify risk level
      const overallRisk = this.classifyRiskLevel(riskScore);
      
      // Generate mitigation strategies
      const mitigationStrategies = this.generateMitigationStrategies(riskFactors, overallRisk);
      
      // Determine time horizon for risk assessment
      const timeHorizon = this.determineRiskTimeHorizon(riskFactors);
      
      // Calculate confidence in risk assessment
      const confidenceLevel = this.calculateRiskConfidence(riskFactors, liveData);

      return {
        overallRisk,
        riskScore,
        riskFactors,
        mitigationStrategies,
        timeHorizon,
        confidenceLevel
      };
    } catch (error) {
      console.error('Error in risk assessment:', error);
      return this.getFallbackRiskAssessment();
    }
  }

  /**
   * Calculate comprehensive volatility metrics
   */
  async calculateVolatilityMetrics(liveData: LiveMarketData): Promise<VolatilityMetrics> {
    try {
      const [indianVol, foreignVol, cryptoVol] = await Promise.all([
        this.calculateMarketVolatility('indian', liveData),
        this.calculateMarketVolatility('foreign', liveData),
        this.calculateMarketVolatility('crypto', liveData)
      ]);

      // Calculate cross-market volatility
      const crossMarketVolatility = this.calculateCrossMarketVolatility([indianVol, foreignVol, cryptoVol]);
      
      // Determine overall volatility trend
      const volatilityTrend = this.determineVolatilityTrend([indianVol, foreignVol, cryptoVol]);

      return {
        indian: indianVol,
        foreign: foreignVol,
        crypto: cryptoVol,
        crossMarketVolatility,
        volatilityTrend
      };
    } catch (error) {
      console.error('Error calculating volatility:', error);
      return this.getFallbackVolatility();
    }
  }

  /**
   * Calculate volatility for specific market
   */
  private async calculateMarketVolatility(market: string, liveData: LiveMarketData): Promise<VolatilityData> {
    // Get relevant indices for the market
    const relevantIndices = this.getRelevantIndices(market, liveData.indices);
    
    if (relevantIndices.length === 0) {
      return this.getDefaultVolatilityData();
    }

    // Calculate current volatility (simplified using change percentages)
    const changePercentages = relevantIndices.map(index => Math.abs(index.changePercent));
    const currentVolatility = changePercentages.reduce((a, b) => a + b, 0) / changePercentages.length;

    // Simulate 30-day average (in production, use historical data)
    const average30d = currentVolatility * (0.8 + Math.random() * 0.4);

    // Calculate percentile ranking
    const percentile = this.calculateVolatilityPercentile(currentVolatility, market);

    // Determine trend
    const trend = this.determineVolatilityTrendForMarket(currentVolatility, average30d);

    // Identify volatility drivers
    const drivers = this.identifyVolatilityDrivers(market, liveData);

    return {
      current: currentVolatility,
      average30d,
      percentile,
      trend,
      drivers
    };
  }

  /**
   * Identify risk factors from news and market data
   */
  private async identifyRiskFactors(
    articles: NewsArticle[],
    liveData: LiveMarketData,
    volatility: VolatilityMetrics
  ): Promise<RiskFactor[]> {
    const riskFactors: RiskFactor[] = [];

    // 1. News-based risk factors
    const newsRisks = this.extractNewsRiskFactors(articles);
    riskFactors.push(...newsRisks);

    // 2. Market-based risk factors
    const marketRisks = this.extractMarketRiskFactors(liveData);
    riskFactors.push(...marketRisks);

    // 3. Volatility-based risk factors
    const volatilityRisks = this.extractVolatilityRiskFactors(volatility);
    riskFactors.push(...volatilityRisks);

    // 4. Correlation-based risk factors
    const correlationRisks = this.extractCorrelationRiskFactors();
    riskFactors.push(...correlationRisks);

    // Sort by impact and probability
    return riskFactors.sort((a, b) => {
      const scoreA = this.getRiskFactorScore(a);
      const scoreB = this.getRiskFactorScore(b);
      return scoreB - scoreA;
    }).slice(0, 10); // Top 10 risk factors
  }

  /**
   * Extract risk factors from news articles
   */
  private extractNewsRiskFactors(articles: NewsArticle[]): RiskFactor[] {
    const riskKeywords = {
      economic: ['recession', 'inflation', 'unemployment', 'debt crisis', 'economic slowdown'],
      geopolitical: ['war', 'conflict', 'sanctions', 'trade war', 'political instability'],
      regulatory: ['regulation', 'ban', 'restriction', 'compliance', 'legal action'],
      market: ['crash', 'correction', 'bubble', 'volatility', 'liquidity crisis'],
      technical: ['cyber attack', 'system failure', 'technical glitch', 'outage']
    };

    const riskFactors: RiskFactor[] = [];

    articles.forEach(article => {
      const content = `${article.title} ${article.description}`.toLowerCase();
      
      Object.entries(riskKeywords).forEach(([type, keywords]) => {
        keywords.forEach(keyword => {
          if (content.includes(keyword)) {
            riskFactors.push({
              type: type as any,
              description: `${keyword} mentioned in: ${article.title}`,
              impact: this.assessRiskImpact(keyword),
              probability: this.assessRiskProbability(content, keyword),
              timeframe: this.assessRiskTimeframe(content),
              affectedMarkets: this.identifyAffectedMarkets(content)
            });
          }
        });
      });
    });

    return riskFactors;
  }

  /**
   * Helper methods for risk assessment
   */
  private calculateOverallRiskScore(
    riskFactors: RiskFactor[],
    correlations: MarketCorrelationMatrix,
    volatility: VolatilityMetrics
  ): number {
    let score = 0;

    // Risk factors contribution (0-40 points)
    const riskScore = riskFactors.reduce((acc, factor) => {
      return acc + this.getRiskFactorScore(factor);
    }, 0);
    score += Math.min(40, riskScore);

    // Volatility contribution (0-30 points)
    try {
      const avgVolatility = (
        (volatility?.indian?.current || 0.25) +
        (volatility?.foreign?.current || 0.18) +
        (volatility?.crypto?.current || 0.45)
      ) / 3;
      score += avgVolatility * 30;
    } catch (error) {
      console.warn('Error calculating volatility score, using default');
      score += 7.5; // Default volatility contribution
    }

    // Correlation contribution (0-30 points)
    try {
      const highCorrelations = [
        Math.abs(correlations?.indianForeign?.coefficient || 0),
        Math.abs(correlations?.indianCrypto?.coefficient || 0),
        Math.abs(correlations?.foreignCrypto?.coefficient || 0)
      ].filter(corr => corr > 0.7).length;

      score += (highCorrelations / 3) * 30;
    } catch (error) {
      console.warn('Error calculating correlation score, using default');
      score += 10; // Default correlation contribution
    }

    return Math.min(100, score);
  }

  private getRiskFactorScore(factor: RiskFactor): number {
    const impactScores = { low: 1, medium: 2, high: 3, critical: 4 };
    return impactScores[factor.impact] * factor.probability * 2;
  }

  private classifyRiskLevel(score: number): 'very_low' | 'low' | 'medium' | 'high' | 'very_high' | 'extreme' {
    if (score < 15) return 'very_low';
    if (score < 30) return 'low';
    if (score < 50) return 'medium';
    if (score < 70) return 'high';
    if (score < 85) return 'very_high';
    return 'extreme';
  }

  /**
   * Utility methods
   */
  private extractPriceData(liveData: LiveMarketData): Map<string, number[]> {
    const priceData = new Map<string, number[]>();
    
    // Simulate historical price data (in production, fetch real historical data)
    const indianPrices = this.simulateHistoricalPrices(65000, 20);
    const foreignPrices = this.simulateHistoricalPrices(4500, 20);
    const cryptoPrices = this.simulateHistoricalPrices(45000, 20);
    
    priceData.set('indian', indianPrices);
    priceData.set('foreign', foreignPrices);
    priceData.set('crypto', cryptoPrices);
    
    return priceData;
  }

  private simulateHistoricalPrices(basePrice: number, count: number): number[] {
    const prices: number[] = [];
    let currentPrice = basePrice;
    
    for (let i = 0; i < count; i++) {
      const change = (Math.random() - 0.5) * 0.04; // ±2% daily change
      currentPrice *= (1 + change);
      prices.push(currentPrice);
    }
    
    return prices;
  }

  private classifyCorrelationStrength(correlation: number): 'very_strong' | 'strong' | 'moderate' | 'weak' | 'very_weak' {
    if (correlation >= 0.8) return 'very_strong';
    if (correlation >= 0.6) return 'strong';
    if (correlation >= 0.4) return 'moderate';
    if (correlation >= 0.2) return 'weak';
    return 'very_weak';
  }

  private updateCorrelationHistory(key: string, value: number): void {
    const history = this.correlationHistory.get(key) || [];
    history.push(value);
    if (history.length > 100) history.shift(); // Keep last 100 values
    this.correlationHistory.set(key, history);
  }

  /**
   * Fallback methods
   */
  private getFallbackCorrelations(): MarketCorrelationMatrix {
    const defaultCorrelation: CorrelationData = {
      coefficient: 0.3,
      strength: 'moderate',
      direction: 'positive',
      reliability: 0.6,
      lastUpdated: new Date(),
      historicalTrend: [0.2, 0.25, 0.3, 0.28, 0.3]
    };

    return {
      indianForeign: defaultCorrelation,
      indianCrypto: { ...defaultCorrelation, coefficient: 0.1 },
      foreignCrypto: { ...defaultCorrelation, coefficient: 0.2 },
      sectorCorrelations: [],
      timeframedCorrelations: {
        '1h': 0.3,
        '24h': 0.25,
        '7d': 0.2,
        '30d': 0.15
      }
    };
  }

  private getFallbackRiskAssessment(): RiskAssessment {
    return {
      overallRisk: 'medium',
      riskScore: 45,
      riskFactors: [],
      mitigationStrategies: ['Diversify portfolio', 'Monitor market conditions', 'Maintain cash reserves'],
      timeHorizon: 'medium_term',
      confidenceLevel: 0.6
    };
  }

  private getFallbackVolatility(): VolatilityMetrics {
    const defaultVol: VolatilityData = {
      current: 0.25,
      average30d: 0.22,
      percentile: 60,
      trend: 'stable',
      drivers: ['Market uncertainty', 'Economic indicators']
    };

    return {
      indian: defaultVol,
      foreign: { ...defaultVol, current: 0.18 },
      crypto: { ...defaultVol, current: 0.45 },
      crossMarketVolatility: 0.29,
      volatilityTrend: 'stable'
    };
  }

  // Additional helper methods would be implemented here...
  private getDefaultCorrelation(): CorrelationData {
    return {
      coefficient: 0,
      strength: 'very_weak',
      direction: 'positive',
      reliability: 0.3,
      lastUpdated: new Date(),
      historicalTrend: []
    };
  }

  private calculateSectorCorrelations = async (liveData: LiveMarketData) => [];
  private calculateTimeframedCorrelations = async (priceData: Map<string, number[]>) => ({ '1h': 0, '24h': 0, '7d': 0, '30d': 0 });
  private calculateCorrelationReliability = (data1: number[], data2: number[]) => 0.7;
  private getDefaultVolatilityData = (): VolatilityData => ({ current: 0.2, average30d: 0.18, percentile: 50, trend: 'stable', drivers: [] });
  private getRelevantIndices = (market: string, indices: any[]) => indices.slice(0, 2);
  private calculateVolatilityPercentile = (vol: number, market: string) => 50;
  private determineVolatilityTrendForMarket = (current: number, avg: number) => current > avg ? 'rising' : 'falling';
  private identifyVolatilityDrivers = (market: string, data: any) => ['Market conditions'];
  private extractMarketRiskFactors = (data: any): RiskFactor[] => [];
  private extractVolatilityRiskFactors = (vol: any): RiskFactor[] => [];
  private extractCorrelationRiskFactors = (): RiskFactor[] => [];
  private assessRiskImpact = (keyword: string): 'low' | 'medium' | 'high' | 'critical' => 'medium';
  private assessRiskProbability = (content: string, keyword: string) => 0.5;
  private assessRiskTimeframe = (content: string) => 'Short term';
  private identifyAffectedMarkets = (content: string) => ['indian'];
  private generateMitigationStrategies = (factors: RiskFactor[], level: string) => ['Monitor closely'];
  private determineRiskTimeHorizon = (factors: RiskFactor[]) => 'medium_term' as const;
  private calculateRiskConfidence = (factors: RiskFactor[], data: any) => 0.7;
  private calculateCrossMarketVolatility = (vols: VolatilityData[]) => 0.25;
  private determineVolatilityTrend = (vols: VolatilityData[]) => 'stable' as const;
}

export const correlationRiskEngine = new CorrelationRiskEngine();
