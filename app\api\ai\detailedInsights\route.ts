import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || process.env.NEXT_PUBLIC_GEMINI_API_KEY || '');

interface DetailedInsight {
  overview: string;
  technicalAnalysis: {
    trend: string;
    momentum: string;
    support: string;
    resistance: string;
    indicators: string;
  };
  fundamentalAnalysis: {
    valuation: string;
    financialHealth: string;
    growth: string;
    profitability: string;
  };
  marketContext: {
    sectorPerformance: string;
    marketSentiment: string;
    economicFactors: string;
  };
  riskAssessment: {
    shortTerm: string;
    mediumTerm: string;
    longTerm: string;
    keyRisks: string[];
  };
  opportunities: {
    entryPoints: string;
    priceTargets: string;
    catalysts: string;
    timeframe: string;
  };
  actionPlan: {
    recommendation: 'Strong Buy' | 'Buy' | 'Hold' | 'Sell' | 'Strong Sell';
    confidence: number;
    reasoning: string;
    nextSteps: string[];
  };
}

export async function POST(request: NextRequest) {
  try {
    const { ticker, stockData, market } = await request.json();
    
    if (!ticker || !stockData) {
      return NextResponse.json({ error: 'Missing ticker or stock data' }, { status: 400 });
    }
    
    // Generate comprehensive AI analysis
    const detailedInsight = await generateDetailedInsight(ticker, stockData, market);
    
    return NextResponse.json(detailedInsight);
  } catch (error) {
    console.error('Error generating detailed insights:', error);
    
    // Return mock detailed insight as fallback
    const mockInsight = generateMockDetailedInsight(request.body?.ticker || 'UNKNOWN');
    return NextResponse.json(mockInsight);
  }
}

async function generateDetailedInsight(ticker: string, stockData: any, market: string = 'US'): Promise<DetailedInsight> {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    // Calculate key metrics for context
    const pricePosition = ((stockData.price - stockData.low52Week) / (stockData.high52Week - stockData.low52Week)) * 100;
    const marketInfo = market === 'IN' ? 'Indian stock market (NSE/BSE)' : 'US stock market';
    const currency = stockData.currency || (market === 'IN' ? 'INR' : 'USD');
    
    const prompt = `
    As a senior financial analyst, provide a comprehensive investment analysis for ${ticker} in the ${marketInfo}.

    Current Stock Data:
    - Symbol: ${ticker}
    - Current Price: ${currency} ${stockData.price}
    - Daily Change: ${stockData.change >= 0 ? '+' : ''}${stockData.change} (${stockData.changePercent}%)
    - Volume: ${stockData.volume?.toLocaleString() || 'N/A'}
    - Market Cap: ${currency} ${stockData.marketCap?.toLocaleString() || 'N/A'}
    - 52-Week Range: ${currency} ${stockData.low52Week} - ${currency} ${stockData.high52Week}
    - Current position in range: ${pricePosition.toFixed(1)}%
    - P/E Ratio: ${stockData.pe}
    - Market: ${market === 'IN' ? 'India' : 'United States'}

    Provide a detailed analysis in the following structure (return as JSON):

    {
      "overview": "2-3 sentence executive summary of the investment thesis",
      "technicalAnalysis": {
        "trend": "Current trend analysis and direction",
        "momentum": "Momentum indicators and strength",
        "support": "Key support levels and significance",
        "resistance": "Key resistance levels and breakout potential",
        "indicators": "RSI, MACD, volume analysis summary"
      },
      "fundamentalAnalysis": {
        "valuation": "Valuation assessment vs peers and historical levels",
        "financialHealth": "Balance sheet and financial strength analysis",
        "growth": "Growth prospects and revenue trends",
        "profitability": "Profit margins and efficiency metrics"
      },
      "marketContext": {
        "sectorPerformance": "How the sector is performing",
        "marketSentiment": "Overall market sentiment impact",
        "economicFactors": "Relevant economic factors affecting the stock"
      },
      "riskAssessment": {
        "shortTerm": "1-3 month risk factors",
        "mediumTerm": "3-12 month risk considerations",
        "longTerm": "1-3 year strategic risks",
        "keyRisks": ["Risk 1", "Risk 2", "Risk 3"]
      },
      "opportunities": {
        "entryPoints": "Optimal entry strategies and price levels",
        "priceTargets": "Short and long-term price targets with rationale",
        "catalysts": "Upcoming events or factors that could drive price",
        "timeframe": "Expected timeframe for opportunities to materialize"
      },
      "actionPlan": {
        "recommendation": "Strong Buy|Buy|Hold|Sell|Strong Sell",
        "confidence": 85,
        "reasoning": "Detailed reasoning for the recommendation",
        "nextSteps": ["Step 1", "Step 2", "Step 3"]
      }
    }

    Focus on ${market === 'IN' ? 'Indian market dynamics, regulatory environment, and rupee-denominated analysis' : 'US market conditions, Federal Reserve policy, and dollar-denominated analysis'}.
    `;
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    try {
      // Clean the response to extract JSON
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const insight = JSON.parse(jsonMatch[0]);
        return validateDetailedInsight(insight);
      }
    } catch (parseError) {
      console.error('Failed to parse AI response:', parseError);
    }
    
    // If parsing fails, generate structured insight from text
    return generateStructuredInsight(ticker, stockData, market);
    
  } catch (error) {
    console.error('Gemini AI detailed analysis error:', error);
    return generateStructuredInsight(ticker, stockData, market);
  }
}

function validateDetailedInsight(insight: any): DetailedInsight {
  return {
    overview: insight.overview || 'Comprehensive analysis pending.',
    technicalAnalysis: {
      trend: insight.technicalAnalysis?.trend || 'Trend analysis in progress.',
      momentum: insight.technicalAnalysis?.momentum || 'Momentum assessment pending.',
      support: insight.technicalAnalysis?.support || 'Support levels being identified.',
      resistance: insight.technicalAnalysis?.resistance || 'Resistance levels under review.',
      indicators: insight.technicalAnalysis?.indicators || 'Technical indicators being analyzed.'
    },
    fundamentalAnalysis: {
      valuation: insight.fundamentalAnalysis?.valuation || 'Valuation analysis in progress.',
      financialHealth: insight.fundamentalAnalysis?.financialHealth || 'Financial health assessment pending.',
      growth: insight.fundamentalAnalysis?.growth || 'Growth analysis under review.',
      profitability: insight.fundamentalAnalysis?.profitability || 'Profitability metrics being evaluated.'
    },
    marketContext: {
      sectorPerformance: insight.marketContext?.sectorPerformance || 'Sector analysis pending.',
      marketSentiment: insight.marketContext?.marketSentiment || 'Market sentiment being assessed.',
      economicFactors: insight.marketContext?.economicFactors || 'Economic factors under review.'
    },
    riskAssessment: {
      shortTerm: insight.riskAssessment?.shortTerm || 'Short-term risks being evaluated.',
      mediumTerm: insight.riskAssessment?.mediumTerm || 'Medium-term risks under assessment.',
      longTerm: insight.riskAssessment?.longTerm || 'Long-term risks being analyzed.',
      keyRisks: Array.isArray(insight.riskAssessment?.keyRisks) ? insight.riskAssessment.keyRisks : [
        'Market volatility',
        'Sector-specific risks',
        'Economic uncertainty'
      ]
    },
    opportunities: {
      entryPoints: insight.opportunities?.entryPoints || 'Entry strategies being formulated.',
      priceTargets: insight.opportunities?.priceTargets || 'Price targets under calculation.',
      catalysts: insight.opportunities?.catalysts || 'Catalysts being identified.',
      timeframe: insight.opportunities?.timeframe || 'Timeframe assessment pending.'
    },
    actionPlan: {
      recommendation: ['Strong Buy', 'Buy', 'Hold', 'Sell', 'Strong Sell'].includes(insight.actionPlan?.recommendation) 
        ? insight.actionPlan.recommendation : 'Hold',
      confidence: Math.min(Math.max(insight.actionPlan?.confidence || 70, 0), 100),
      reasoning: insight.actionPlan?.reasoning || 'Comprehensive analysis indicates moderate confidence.',
      nextSteps: Array.isArray(insight.actionPlan?.nextSteps) ? insight.actionPlan.nextSteps : [
        'Monitor key technical levels',
        'Watch for earnings updates',
        'Assess market conditions'
      ]
    }
  };
}

function generateStructuredInsight(ticker: string, stockData: any, market: string): DetailedInsight {
  const pricePosition = ((stockData.price - stockData.low52Week) / (stockData.high52Week - stockData.low52Week)) * 100;
  const isIndian = market === 'IN';
  
  return {
    overview: `${ticker} is currently trading at ${pricePosition.toFixed(1)}% of its 52-week range in the ${isIndian ? 'Indian' : 'US'} market. The stock shows ${stockData.changePercent > 0 ? 'positive' : 'negative'} momentum with ${Math.abs(stockData.changePercent).toFixed(1)}% daily movement.`,
    technicalAnalysis: {
      trend: pricePosition > 60 ? 'Uptrend with strong momentum' : pricePosition < 40 ? 'Downtrend requiring caution' : 'Sideways consolidation pattern',
      momentum: stockData.changePercent > 2 ? 'Strong bullish momentum' : stockData.changePercent < -2 ? 'Bearish pressure evident' : 'Neutral momentum',
      support: `Key support around ${(stockData.price * 0.95).toFixed(2)} level`,
      resistance: `Resistance expected near ${(stockData.price * 1.05).toFixed(2)} level`,
      indicators: 'Technical indicators suggest mixed signals requiring careful monitoring'
    },
    fundamentalAnalysis: {
      valuation: stockData.pe > 25 ? 'Premium valuation requires growth justification' : stockData.pe < 15 ? 'Attractive valuation presents opportunity' : 'Fair valuation at current levels',
      financialHealth: 'Financial metrics indicate stable operational performance',
      growth: 'Growth prospects aligned with sector trends',
      profitability: 'Profitability margins within industry standards'
    },
    marketContext: {
      sectorPerformance: `${isIndian ? 'Indian' : 'US'} sector showing mixed performance`,
      marketSentiment: 'Market sentiment remains cautiously optimistic',
      economicFactors: `${isIndian ? 'Indian economic indicators' : 'US economic data'} supporting moderate growth`
    },
    riskAssessment: {
      shortTerm: 'Short-term volatility expected due to market conditions',
      mediumTerm: 'Medium-term outlook depends on sector performance',
      longTerm: 'Long-term prospects tied to fundamental execution',
      keyRisks: [
        'Market volatility',
        'Sector-specific challenges',
        isIndian ? 'Regulatory changes' : 'Interest rate sensitivity'
      ]
    },
    opportunities: {
      entryPoints: `Consider entry on pullbacks to ${(stockData.price * 0.97).toFixed(2)} support`,
      priceTargets: `Target ${(stockData.price * 1.08).toFixed(2)} in 3-6 months`,
      catalysts: 'Earnings announcements and sector developments',
      timeframe: '3-6 months for meaningful price movement'
    },
    actionPlan: {
      recommendation: pricePosition > 80 ? 'Hold' : pricePosition < 30 ? 'Buy' : 'Hold',
      confidence: 75,
      reasoning: 'Analysis based on current technical and fundamental factors',
      nextSteps: [
        'Monitor key support/resistance levels',
        'Track sector performance trends',
        'Watch for earnings updates'
      ]
    }
  };
}

function generateMockDetailedInsight(ticker: string): DetailedInsight {
  return generateStructuredInsight(ticker, {
    price: 150,
    change: 2.5,
    changePercent: 1.7,
    low52Week: 120,
    high52Week: 180,
    pe: 22
  }, 'US');
}
