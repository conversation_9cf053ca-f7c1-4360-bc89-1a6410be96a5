// News fetching service with NewsAPI integration

import {
  NewsArticle,
  NewsQuery,
  NewsResponse,
  NewsAPIResponse,
  NewsCategory,
  NEWS_CATEGORIES,
  NEWS_API_CONFIG
} from './types';
import { newsSegmentationService } from '../news-segmentation';

class NewsFetchService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_NEWS_API_KEY || '';
    this.baseUrl = NEWS_API_CONFIG.BASE_URL;
    
    if (!this.apiKey) {
      console.warn('NewsAPI key not found. Using mock data for development.');
    }
  }

  /**
   * Fetch news articles based on query parameters
   */
  async fetchNews(query: NewsQuery): Promise<NewsResponse> {
    try {
      if (!this.apiKey) {
        return this.getMockNews(query);
      }

      const url = this.buildApiUrl(query);
      const response = await fetch(url, {
        headers: {
          'X-API-Key': this.apiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`NewsAPI error: ${response.status} ${response.statusText}`);
      }

      const data: NewsAPIResponse = await response.json();
      
      if (data.status !== 'ok') {
        throw new Error(`NewsAPI returned error status: ${data.status}`);
      }

      return this.transformApiResponse(data, query.category);
    } catch (error) {
      console.error('Error fetching news from API, falling back to mock data:', error);
      // Fall back to mock data instead of returning error
      return this.getMockNews(query);
    }
  }

  /**
   * Fetch news by category with optimized queries
   */
  async fetchNewsByCategory(category: NewsCategory): Promise<NewsResponse> {
    const query: NewsQuery = {
      query: this.buildCategoryQuery(category),
      category,
      sortBy: 'publishedAt',
      pageSize: 50 // Fetch more articles for better segmentation
    };

    const response = await this.fetchNews(query);

    if (response.status === 'ok' && response.articles.length > 0) {
      try {
        // Use advanced segmentation to get the most relevant articles
        const segmentId = this.mapCategoryToSegment(category);
        const segmentedResult = await newsSegmentationService.getSegmentedNews(
          response.articles,
          segmentId,
          {
            minRelevanceScore: 0.2, // Lower threshold for better coverage
            maxArticles: 20,
            sortBy: 'relevance'
          }
        );

        // If segmentation returns articles, use them; otherwise fall back to original
        if (segmentedResult.articles.length > 0) {
          return {
            status: 'ok',
            totalResults: segmentedResult.totalRelevant,
            articles: segmentedResult.articles
          };
        } else {
          console.warn(`No articles found for segment ${segmentId}, using fallback`);
          // Fall back to basic categorization
          const filteredArticles = this.basicCategoryFilter(response.articles, category);
          return {
            status: 'ok',
            totalResults: filteredArticles.length,
            articles: filteredArticles.slice(0, 20)
          };
        }
      } catch (error) {
        console.error('Error in advanced segmentation, using fallback:', error);
        // Fall back to basic categorization
        const filteredArticles = this.basicCategoryFilter(response.articles, category);
        return {
          status: 'ok',
          totalResults: filteredArticles.length,
          articles: filteredArticles.slice(0, 20)
        };
      }
    }

    return response;
  }

  /**
   * Search news with custom query
   */
  async searchNews(searchQuery: string, filters?: Partial<NewsQuery>): Promise<NewsResponse> {
    const query: NewsQuery = {
      query: this.enhanceSearchQuery(searchQuery),
      sortBy: 'relevancy',
      pageSize: 30,
      ...filters
    };

    return this.fetchNews(query);
  }

  /**
   * Get trending financial news
   */
  async getTrendingNews(): Promise<NewsResponse> {
    const query: NewsQuery = {
      query: 'finance OR economy OR stock market OR investment',
      sortBy: 'popularity',
      pageSize: 15
    };

    return this.fetchNews(query);
  }

  /**
   * Build API URL with query parameters
   */
  private buildApiUrl(query: NewsQuery): string {
    const endpoint = query.query ? 
      NEWS_API_CONFIG.ENDPOINTS.EVERYTHING : 
      NEWS_API_CONFIG.ENDPOINTS.TOP_HEADLINES;
    
    const url = new URL(this.baseUrl + endpoint);
    
    // Add query parameters
    if (query.query) {
      url.searchParams.append('q', query.query);
    }
    
    if (query.sortBy) {
      url.searchParams.append('sortBy', query.sortBy);
    }
    
    if (query.from) {
      url.searchParams.append('from', query.from);
    }
    
    if (query.to) {
      url.searchParams.append('to', query.to);
    }
    
    url.searchParams.append('language', query.language || NEWS_API_CONFIG.DEFAULT_PARAMS.language);
    url.searchParams.append('pageSize', (query.pageSize || NEWS_API_CONFIG.DEFAULT_PARAMS.pageSize).toString());
    
    if (query.page) {
      url.searchParams.append('page', query.page.toString());
    }

    return url.toString();
  }

  /**
   * Build optimized query for specific categories
   */
  private buildCategoryQuery(category: NewsCategory): string {
    const config = NEWS_CATEGORIES[category];
    const keywords = config.keywords.slice(0, 5); // Limit to avoid URL length issues
    
    // Create OR query with keywords
    return keywords.map(keyword => `"${keyword}"`).join(' OR ');
  }

  /**
   * Enhance search query with financial context
   */
  private enhanceSearchQuery(query: string): string {
    const financialTerms = ['finance', 'financial', 'economy', 'market', 'investment'];
    const hasFinancialContext = financialTerms.some(term => 
      query.toLowerCase().includes(term)
    );
    
    if (!hasFinancialContext) {
      return `${query} AND (finance OR economy OR market OR investment)`;
    }
    
    return query;
  }

  /**
   * Transform NewsAPI response to our format
   */
  private transformApiResponse(data: NewsAPIResponse, category?: NewsCategory): NewsResponse {
    const articles: NewsArticle[] = data.articles
      .filter(article => article.title && article.description)
      .map(article => ({
        id: this.generateArticleId(article.url),
        title: article.title,
        description: article.description,
        content: article.content || undefined,
        url: article.url,
        urlToImage: article.urlToImage || undefined,
        publishedAt: article.publishedAt,
        source: article.source,
        category: category || this.categorizeArticle(article.title + ' ' + article.description),
        relevanceScore: this.calculateRelevanceScore(article, category)
      }));

    return {
      status: 'ok',
      totalResults: data.totalResults,
      articles
    };
  }

  /**
   * Generate unique ID for article
   */
  private generateArticleId(url: string): string {
    return btoa(url).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
  }

  /**
   * Map category to segmentation ID
   */
  private mapCategoryToSegment(category: NewsCategory): string {
    const mapping: Record<NewsCategory, string> = {
      'india-specific': 'indian-markets',
      'global-markets': 'global-markets',
      'crypto-web3': 'crypto-web3',
      'stocks-companies': 'indian-markets', // Can be either, but default to Indian
      'macro-policy': 'global-markets',
      'general': 'indian-markets'
    };

    return mapping[category] || 'indian-markets';
  }

  /**
   * Basic category filtering as fallback with strict relevance
   */
  private basicCategoryFilter(articles: NewsArticle[], category: NewsCategory): NewsArticle[] {
    const categoryConfig = NEWS_CATEGORIES[category];
    if (!categoryConfig) return articles;

    // Get category-specific keywords for strict filtering
    const strictKeywords = this.getCategoryStrictKeywords(category);

    return articles.filter(article => {
      const content = `${article.title} ${article.description}`.toLowerCase();

      // Strict keyword matching - must contain at least 2 relevant keywords
      const keywordMatches = strictKeywords.filter(keyword =>
        content.includes(keyword.toLowerCase())
      ).length;

      // Check source preference
      const isPreferredSource = categoryConfig.sources?.some(source =>
        article.source.name.toLowerCase().includes(source.toLowerCase())
      ) || false;

      // Geographic relevance for India-specific
      const hasGeographicRelevance = category === 'india-specific' ?
        this.hasIndianRelevance(content) : true;

      // Must have strong keyword matches AND geographic relevance
      const isRelevant = keywordMatches >= 2 && hasGeographicRelevance;

      return isRelevant || (isPreferredSource && keywordMatches >= 1);
    }).map(article => ({
      ...article,
      category,
      relevanceScore: this.calculateBasicRelevance(article, categoryConfig)
    })).sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));
  }

  /**
   * Get strict keywords for each category
   */
  private getCategoryStrictKeywords(category: NewsCategory): string[] {
    const strictKeywords: Record<NewsCategory, string[]> = {
      'india-specific': [
        'rbi', 'reserve bank of india', 'sensex', 'nifty', 'bse', 'nse',
        'indian', 'india', 'rupee', 'inr', 'mumbai', 'delhi', 'bangalore',
        'tcs', 'infosys', 'hdfc', 'icici', 'sbi', 'reliance', 'adani',
        'sebi', 'gst', 'fii', 'dii', 'indian banking', 'indian markets'
      ],
      'global-markets': [
        'fed', 'federal reserve', 'dow jones', 's&p 500', 'nasdaq', 'wall street',
        'dollar', 'usd', 'euro', 'pound', 'yen', 'ecb', 'fomc',
        'apple', 'microsoft', 'amazon', 'google', 'tesla', 'usa', 'us', 'america'
      ],
      'crypto-web3': [
        'bitcoin', 'ethereum', 'crypto', 'cryptocurrency', 'blockchain', 'defi',
        'web3', 'nft', 'btc', 'eth', 'binance', 'coinbase', 'altcoin'
      ],
      'stocks-companies': [
        'earnings', 'quarterly', 'revenue', 'profit', 'stock', 'share', 'ipo',
        'dividend', 'market cap', 'pe ratio', 'eps'
      ],
      'macro-policy': [
        'inflation', 'gdp', 'monetary policy', 'fiscal policy', 'interest rate',
        'economic growth', 'recession', 'recovery'
      ],
      'general': []
    };

    return strictKeywords[category] || [];
  }

  /**
   * Check if content has Indian market relevance
   */
  private hasIndianRelevance(content: string): boolean {
    const indianMarkers = [
      'india', 'indian', 'rbi', 'sensex', 'nifty', 'bse', 'nse', 'rupee', 'inr',
      'mumbai', 'delhi', 'bangalore', 'chennai', 'hyderabad', 'pune',
      'tcs', 'infosys', 'wipro', 'hdfc', 'icici', 'sbi', 'reliance', 'adani',
      'bajaj', 'tata', 'mahindra', 'bharti', 'asian paints', 'itc'
    ];

    return indianMarkers.some(marker => content.includes(marker));
  }

  /**
   * Calculate basic relevance score
   */
  private calculateBasicRelevance(article: NewsArticle, categoryConfig: any): number {
    const content = `${article.title} ${article.description}`.toLowerCase();
    let score = 0.3; // Base score

    // Keyword matches
    const keywordMatches = categoryConfig.keywords.filter((keyword: string) =>
      content.includes(keyword.toLowerCase())
    ).length;
    score += (keywordMatches / categoryConfig.keywords.length) * 0.4;

    // Source preference
    const isPreferredSource = categoryConfig.sources?.some((source: string) =>
      article.source.name.toLowerCase().includes(source.toLowerCase())
    );
    if (isPreferredSource) score += 0.2;

    // Recency bonus
    const hoursAgo = (Date.now() - new Date(article.publishedAt).getTime()) / (1000 * 60 * 60);
    if (hoursAgo < 24) score += 0.1;

    return Math.min(score, 1.0);
  }

  /**
   * Categorize article based on content
   */
  private categorizeArticle(content: string): NewsCategory {
    const lowerContent = content.toLowerCase();
    
    for (const [category, config] of Object.entries(NEWS_CATEGORIES)) {
      const matchCount = config.keywords.filter(keyword => 
        lowerContent.includes(keyword.toLowerCase())
      ).length;
      
      if (matchCount >= 2) {
        return category as NewsCategory;
      }
    }
    
    return 'general';
  }

  /**
   * Calculate relevance score for article
   */
  private calculateRelevanceScore(article: any, category?: NewsCategory): number {
    let score = 0.5; // Base score
    
    if (category) {
      const config = NEWS_CATEGORIES[category];
      const content = (article.title + ' ' + article.description).toLowerCase();
      
      const matchCount = config.keywords.filter(keyword => 
        content.includes(keyword.toLowerCase())
      ).length;
      
      score += (matchCount / config.keywords.length) * 0.5;
    }
    
    // Boost score for recent articles
    const publishedDate = new Date(article.publishedAt);
    const hoursAgo = (Date.now() - publishedDate.getTime()) / (1000 * 60 * 60);
    
    if (hoursAgo < 24) {
      score += 0.2;
    } else if (hoursAgo < 72) {
      score += 0.1;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * Get mock news data for development
   */
  private getMockNews(query: NewsQuery): NewsResponse {
    const mockArticles: NewsArticle[] = [
      {
        id: 'mock1',
        title: 'RBI Announces New Monetary Policy Changes for Indian Banking Sector',
        description: 'Reserve Bank of India announces key policy rate changes affecting banking sector and inflation targets for the upcoming quarter. The repo rate decision impacts Sensex and Nifty movements.',
        url: 'https://example.com/rbi-policy',
        publishedAt: new Date().toISOString(),
        source: { id: 'rbi', name: 'Economic Times' },
        category: 'india-specific',
        relevanceScore: 0.9
      },
      {
        id: 'mock2',
        title: 'Sensex Hits New Record High as Indian Markets Rally',
        description: 'BSE Sensex and NSE Nifty reach unprecedented levels amid positive investor sentiment and strong corporate earnings from TCS, HDFC Bank, and Reliance Industries.',
        url: 'https://example.com/sensex-record',
        publishedAt: new Date(Date.now() - 1800000).toISOString(),
        source: { id: 'bse', name: 'Business Standard' },
        category: 'india-specific',
        relevanceScore: 0.95
      },
      {
        id: 'mock3',
        title: 'HDFC Bank Reports Strong Q3 Earnings, Stock Jumps 5%',
        description: 'HDFC Bank beats analyst expectations with robust quarterly results driven by improved NII and lower NPAs. Indian banking sector shows resilience.',
        url: 'https://example.com/hdfc-earnings',
        publishedAt: new Date(Date.now() - 3600000).toISOString(),
        source: { id: 'economic-times', name: 'Economic Times' },
        category: 'india-specific',
        relevanceScore: 0.85
      },
      {
        id: 'mock4',
        title: 'TCS Reports Strong Q3 Earnings, IT Sector Outlook Positive',
        description: 'Tata Consultancy Services beats analyst expectations with robust quarterly results, driven by digital transformation projects. Indian IT sector shows strong momentum.',
        url: 'https://example.com/tcs-earnings',
        publishedAt: new Date(Date.now() - 5400000).toISOString(),
        source: { id: 'economic-times', name: 'Economic Times' },
        category: 'india-specific',
        relevanceScore: 0.85
      },
      {
        id: 'mock5',
        title: 'India Inflation Data Shows Cooling Trend, RBI Policy Impact',
        description: 'Latest inflation figures indicate a gradual cooling in price pressures, providing relief to RBI policymakers and Indian consumers. Rupee strengthens against dollar.',
        url: 'https://example.com/inflation-data',
        publishedAt: new Date(Date.now() - 7200000).toISOString(),
        source: { id: 'rbi', name: 'Livemint' },
        category: 'india-specific',
        relevanceScore: 0.9
      },
      {
        id: 'mock6',
        title: 'Reliance Industries Announces Major Investment in Green Energy',
        description: 'Reliance Industries announces massive investment in renewable energy sector, boosting green energy stocks. Mukesh Ambani outlines sustainability roadmap for India.',
        url: 'https://example.com/reliance-green',
        publishedAt: new Date(Date.now() - 9000000).toISOString(),
        source: { id: 'reliance', name: 'Business Standard' },
        category: 'india-specific',
        relevanceScore: 0.8
      },
      {
        id: 'mock7',
        title: 'Indian Banking Sector Shows Strong Growth in Digital Payments',
        description: 'SBI, ICICI Bank, and other major Indian banks report significant growth in digital payment volumes. UPI transactions reach new milestone.',
        url: 'https://example.com/banking-digital',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'sbi', name: 'Economic Times' },
        category: 'india-specific',
        relevanceScore: 0.75
      },
      {
        id: 'mock8',
        title: 'Nifty 50 Crosses 22,000 Mark Amid FII Inflows',
        description: 'NSE Nifty 50 index crosses historic 22,000 level as Foreign Institutional Investors increase stake in Indian equities. Market cap hits new high.',
        url: 'https://example.com/nifty-22000',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'nse', name: 'Moneycontrol' },
        category: 'india-specific',
        relevanceScore: 0.9
      },
      // Global Markets Articles
      {
        id: 'global1',
        title: 'Federal Reserve Signals Potential Rate Cuts in 2024',
        description: 'Fed Chair Jerome Powell indicates possible interest rate reductions as US inflation shows signs of cooling. Wall Street reacts positively to dovish stance.',
        url: 'https://example.com/fed-rates',
        publishedAt: new Date(Date.now() - 1800000).toISOString(),
        source: { id: 'fed', name: 'Reuters' },
        category: 'global-markets',
        relevanceScore: 0.9
      },
      {
        id: 'global2',
        title: 'S&P 500 Hits Record High as Tech Stocks Rally',
        description: 'US stock market benchmark S&P 500 reaches new all-time high driven by strong performance from Apple, Microsoft, and Google. Nasdaq also surges.',
        url: 'https://example.com/sp500-record',
        publishedAt: new Date(Date.now() - 3600000).toISOString(),
        source: { id: 'nyse', name: 'Bloomberg' },
        category: 'global-markets',
        relevanceScore: 0.85
      },
      // Crypto Articles
      {
        id: 'crypto1',
        title: 'Bitcoin Surges Past $45,000 on ETF Approval Hopes',
        description: 'Bitcoin cryptocurrency rallies to $45,000 as investors anticipate potential approval of spot Bitcoin ETFs. Ethereum and other altcoins follow suit.',
        url: 'https://example.com/bitcoin-surge',
        publishedAt: new Date(Date.now() - 2700000).toISOString(),
        source: { id: 'crypto', name: 'CoinDesk' },
        category: 'crypto-web3',
        relevanceScore: 0.8
      },
      {
        id: 'crypto2',
        title: 'Ethereum Network Upgrade Reduces Transaction Fees',
        description: 'Latest Ethereum blockchain upgrade successfully reduces gas fees by 30%. DeFi protocols and NFT marketplaces benefit from lower transaction costs.',
        url: 'https://example.com/ethereum-upgrade',
        publishedAt: new Date(Date.now() - 5400000).toISOString(),
        source: { id: 'ethereum', name: 'Cointelegraph' },
        category: 'crypto-web3',
        relevanceScore: 0.75
      },
      // Additional India-Specific Articles (to ensure 9+ articles)
      {
        id: 'india9',
        title: 'SEBI Introduces New Regulations for Mutual Fund Industry',
        description: 'Securities and Exchange Board of India announces comprehensive reforms for mutual fund sector. New rules aim to enhance investor protection and market transparency.',
        url: 'https://example.com/sebi-mf-rules',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'sebi', name: 'Economic Times' },
        category: 'india-specific',
        relevanceScore: 0.8
      },
      {
        id: 'india10',
        title: 'Indian Rupee Strengthens Against Dollar on FII Inflows',
        description: 'INR gains 0.5% against USD as Foreign Institutional Investors pump ₹5,000 crore into Indian equity markets. RBI intervention supports currency stability.',
        url: 'https://example.com/rupee-strength',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'rbi', name: 'Business Standard' },
        category: 'india-specific',
        relevanceScore: 0.85
      },
      {
        id: 'india11',
        title: 'Bajaj Finance Reports 25% Growth in Quarterly Profits',
        description: 'Bajaj Finance announces strong Q3 results with 25% YoY profit growth. Indian NBFC sector shows resilience amid economic headwinds.',
        url: 'https://example.com/bajaj-results',
        publishedAt: new Date(Date.now() - 18000000).toISOString(),
        source: { id: 'bajaj', name: 'Livemint' },
        category: 'india-specific',
        relevanceScore: 0.8
      },
      {
        id: 'india12',
        title: 'Indian IT Sector Outlook Positive Despite Global Slowdown',
        description: 'TCS, Infosys, and Wipro maintain optimistic guidance for FY24. Indian IT services companies benefit from digital transformation demand.',
        url: 'https://example.com/it-outlook',
        publishedAt: new Date(Date.now() - 19800000).toISOString(),
        source: { id: 'nasscom', name: 'Economic Times' },
        category: 'india-specific',
        relevanceScore: 0.75
      },
      {
        id: 'india13',
        title: 'Mumbai Real Estate Prices Surge 15% YoY',
        description: 'Mumbai property market shows strong growth with 15% annual price appreciation. Demand from HNIs and NRIs drives luxury segment growth.',
        url: 'https://example.com/mumbai-realty',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'realty', name: 'Moneycontrol' },
        category: 'india-specific',
        relevanceScore: 0.7
      },
      // Additional Global Markets Articles
      {
        id: 'global3',
        title: 'European Central Bank Maintains Hawkish Stance on Inflation',
        description: 'ECB President Christine Lagarde signals continued aggressive monetary policy. European markets react to potential for further rate hikes.',
        url: 'https://example.com/ecb-hawkish',
        publishedAt: new Date(Date.now() - 7200000).toISOString(),
        source: { id: 'ecb', name: 'Financial Times' },
        category: 'global-markets',
        relevanceScore: 0.8
      },
      {
        id: 'global4',
        title: 'Tesla Stock Rallies 8% on Strong China Sales Data',
        description: 'Tesla shares surge after reporting record quarterly deliveries in China. Electric vehicle demand in Asia drives optimism for global expansion.',
        url: 'https://example.com/tesla-china',
        publishedAt: new Date(Date.now() - 9000000).toISOString(),
        source: { id: 'tesla', name: 'Bloomberg' },
        category: 'global-markets',
        relevanceScore: 0.75
      },
      {
        id: 'global5',
        title: 'US Dollar Weakens as Fed Signals Dovish Pivot',
        description: 'Dollar index falls to 3-month low as Federal Reserve hints at slower pace of rate increases. Global currencies gain against USD.',
        url: 'https://example.com/dollar-weak',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'fed', name: 'Reuters' },
        category: 'global-markets',
        relevanceScore: 0.85
      },
      {
        id: 'global6',
        title: 'Apple Reports Record iPhone Sales in Holiday Quarter',
        description: 'Apple Inc. announces strongest ever Q1 results driven by iPhone 15 demand. Services revenue also hits new milestone.',
        url: 'https://example.com/apple-record',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'apple', name: 'Wall Street Journal' },
        category: 'global-markets',
        relevanceScore: 0.8
      },
      {
        id: 'global7',
        title: 'FTSE 100 Hits All-Time High on Banking Sector Rally',
        description: 'London stock market reaches record levels as UK banking stocks surge. HSBC and Barclays lead gains on improved outlook.',
        url: 'https://example.com/ftse-record',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'lse', name: 'Financial Times' },
        category: 'global-markets',
        relevanceScore: 0.75
      },
      {
        id: 'global8',
        title: 'China GDP Growth Beats Expectations at 5.2%',
        description: 'Chinese economy shows resilience with better-than-expected GDP growth. Manufacturing and services sectors drive economic recovery.',
        url: 'https://example.com/china-gdp',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'china', name: 'Bloomberg' },
        category: 'global-markets',
        relevanceScore: 0.8
      },
      {
        id: 'global9',
        title: 'Microsoft Azure Revenue Surges 30% on AI Demand',
        description: 'Microsoft reports exceptional cloud growth driven by artificial intelligence services. Azure AI capabilities attract enterprise customers.',
        url: 'https://example.com/microsoft-ai',
        publishedAt: new Date(Date.now() - 18000000).toISOString(),
        source: { id: 'microsoft', name: 'Reuters' },
        category: 'global-markets',
        relevanceScore: 0.85
      },
      // Additional Crypto Articles
      {
        id: 'crypto3',
        title: 'Solana Network Experiences 400% Surge in DeFi Activity',
        description: 'Solana blockchain sees massive growth in decentralized finance protocols. SOL token rallies 25% on increased network usage.',
        url: 'https://example.com/solana-defi',
        publishedAt: new Date(Date.now() - 7200000).toISOString(),
        source: { id: 'solana', name: 'CoinDesk' },
        category: 'crypto-web3',
        relevanceScore: 0.8
      },
      {
        id: 'crypto4',
        title: 'Binance Launches New Crypto Derivatives Trading Platform',
        description: 'World\'s largest crypto exchange introduces advanced derivatives trading with enhanced security features. BNB token gains on news.',
        url: 'https://example.com/binance-derivatives',
        publishedAt: new Date(Date.now() - 9000000).toISOString(),
        source: { id: 'binance', name: 'Cointelegraph' },
        category: 'crypto-web3',
        relevanceScore: 0.75
      },
      {
        id: 'crypto5',
        title: 'NFT Market Shows Signs of Recovery with 60% Volume Increase',
        description: 'Non-fungible token trading volumes surge as new utility-focused projects gain traction. Ethereum-based NFTs lead the recovery.',
        url: 'https://example.com/nft-recovery',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'nft', name: 'The Block' },
        category: 'crypto-web3',
        relevanceScore: 0.7
      },
      {
        id: 'crypto6',
        title: 'Cardano Completes Major Smart Contract Upgrade',
        description: 'Cardano blockchain implements Plutus V3 upgrade, enhancing smart contract capabilities. ADA price rallies 15% on technical milestone.',
        url: 'https://example.com/cardano-upgrade',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'cardano', name: 'CoinDesk' },
        category: 'crypto-web3',
        relevanceScore: 0.75
      },
      {
        id: 'crypto7',
        title: 'Institutional Bitcoin Holdings Reach $100 Billion Milestone',
        description: 'Corporate and institutional Bitcoin holdings surpass $100B as MicroStrategy and Tesla maintain large positions. Adoption accelerates.',
        url: 'https://example.com/btc-institutional',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'institutional', name: 'Cointelegraph' },
        category: 'crypto-web3',
        relevanceScore: 0.85
      },
      {
        id: 'crypto8',
        title: 'Polygon Network Processes 1 Million Daily Transactions',
        description: 'Polygon achieves new milestone with over 1M daily transactions. Layer-2 scaling solution gains adoption from major DApps.',
        url: 'https://example.com/polygon-milestone',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'polygon', name: 'The Block' },
        category: 'crypto-web3',
        relevanceScore: 0.8
      },
      {
        id: 'crypto9',
        title: 'Chainlink Oracles Secure $50 Billion in DeFi Value',
        description: 'Chainlink decentralized oracle network now secures over $50B in total value locked across DeFi protocols. LINK token benefits.',
        url: 'https://example.com/chainlink-tvl',
        publishedAt: new Date(Date.now() - 18000000).toISOString(),
        source: { id: 'chainlink', name: 'CoinDesk' },
        category: 'crypto-web3',
        relevanceScore: 0.75
      }
    ];

    // Filter articles based on query and apply deduplication
    let filteredArticles = mockArticles;

    if (query.category) {
      // Use the deduplication service for category filtering with minimum 9 articles
      const { newsDeduplicationService } = require('./deduplication');
      filteredArticles = newsDeduplicationService.filterAndDeduplicateForCategory(
        mockArticles,
        query.category,
        15, // Max articles
        9   // Minimum 9 articles per category
      );
    }

    // If no articles match the category, return a subset of all articles
    if (filteredArticles.length === 0) {
      filteredArticles = mockArticles.slice(0, 8); // Limit to prevent repetition
    }

    return {
      status: 'ok',
      totalResults: filteredArticles.length,
      articles: filteredArticles
    };
  }
}

export const newsFetchService = new NewsFetchService();
