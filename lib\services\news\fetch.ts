// News fetching service with NewsAPI integration

import {
  NewsArticle,
  NewsQuery,
  NewsResponse,
  NewsAPIResponse,
  NewsCategory,
  NEWS_CATEGORIES,
  NEWS_API_CONFIG
} from './types';
import { newsSegmentationService } from '../news-segmentation';

class NewsFetchService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_NEWS_API_KEY || '';
    this.baseUrl = NEWS_API_CONFIG.BASE_URL;
    
    if (!this.apiKey) {
      console.warn('NewsAPI key not found. Using mock data for development.');
    }
  }

  /**
   * Fetch news articles based on query parameters
   */
  async fetchNews(query: NewsQuery): Promise<NewsResponse> {
    try {
      if (!this.apiKey) {
        return this.getMockNews(query);
      }

      const url = this.buildApiUrl(query);
      const response = await fetch(url, {
        headers: {
          'X-API-Key': this.apiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`NewsAPI error: ${response.status} ${response.statusText}`);
      }

      const data: NewsAPIResponse = await response.json();
      
      if (data.status !== 'ok') {
        throw new Error(`NewsAPI returned error status: ${data.status}`);
      }

      return this.transformApiResponse(data, query.category);
    } catch (error) {
      console.error('Error fetching news from API, falling back to mock data:', error);
      // Fall back to mock data instead of returning error
      return this.getMockNews(query);
    }
  }

  /**
   * Fetch news by category with optimized queries
   */
  async fetchNewsByCategory(category: NewsCategory): Promise<NewsResponse> {
    const query: NewsQuery = {
      query: this.buildCategoryQuery(category),
      category,
      sortBy: 'publishedAt',
      pageSize: 50 // Fetch more articles for better segmentation
    };

    const response = await this.fetchNews(query);

    if (response.status === 'ok' && response.articles.length > 0) {
      try {
        // Use advanced segmentation to get the most relevant articles
        const segmentId = this.mapCategoryToSegment(category);
        const segmentedResult = await newsSegmentationService.getSegmentedNews(
          response.articles,
          segmentId,
          {
            minRelevanceScore: 0.2, // Lower threshold for better coverage
            maxArticles: 20,
            sortBy: 'relevance'
          }
        );

        // If segmentation returns articles, use them; otherwise fall back to original
        if (segmentedResult.articles.length > 0) {
          return {
            status: 'ok',
            totalResults: segmentedResult.totalRelevant,
            articles: segmentedResult.articles
          };
        } else {
          console.warn(`No articles found for segment ${segmentId}, using fallback`);
          // Fall back to basic categorization
          const filteredArticles = this.basicCategoryFilter(response.articles, category);
          return {
            status: 'ok',
            totalResults: filteredArticles.length,
            articles: filteredArticles.slice(0, 20)
          };
        }
      } catch (error) {
        console.error('Error in advanced segmentation, using fallback:', error);
        // Fall back to basic categorization
        const filteredArticles = this.basicCategoryFilter(response.articles, category);
        return {
          status: 'ok',
          totalResults: filteredArticles.length,
          articles: filteredArticles.slice(0, 20)
        };
      }
    }

    return response;
  }

  /**
   * Search news with custom query
   */
  async searchNews(searchQuery: string, filters?: Partial<NewsQuery>): Promise<NewsResponse> {
    const query: NewsQuery = {
      query: this.enhanceSearchQuery(searchQuery),
      sortBy: 'relevancy',
      pageSize: 30,
      ...filters
    };

    return this.fetchNews(query);
  }

  /**
   * Get trending financial news
   */
  async getTrendingNews(): Promise<NewsResponse> {
    const query: NewsQuery = {
      query: 'finance OR economy OR stock market OR investment',
      sortBy: 'popularity',
      pageSize: 15
    };

    return this.fetchNews(query);
  }

  /**
   * Build API URL with query parameters
   */
  private buildApiUrl(query: NewsQuery): string {
    const endpoint = query.query ? 
      NEWS_API_CONFIG.ENDPOINTS.EVERYTHING : 
      NEWS_API_CONFIG.ENDPOINTS.TOP_HEADLINES;
    
    const url = new URL(this.baseUrl + endpoint);
    
    // Add query parameters
    if (query.query) {
      url.searchParams.append('q', query.query);
    }
    
    if (query.sortBy) {
      url.searchParams.append('sortBy', query.sortBy);
    }
    
    if (query.from) {
      url.searchParams.append('from', query.from);
    }
    
    if (query.to) {
      url.searchParams.append('to', query.to);
    }
    
    url.searchParams.append('language', query.language || NEWS_API_CONFIG.DEFAULT_PARAMS.language);
    url.searchParams.append('pageSize', (query.pageSize || NEWS_API_CONFIG.DEFAULT_PARAMS.pageSize).toString());
    
    if (query.page) {
      url.searchParams.append('page', query.page.toString());
    }

    return url.toString();
  }

  /**
   * Build optimized query for specific categories
   */
  private buildCategoryQuery(category: NewsCategory): string {
    const config = NEWS_CATEGORIES[category];
    const keywords = config.keywords.slice(0, 5); // Limit to avoid URL length issues
    
    // Create OR query with keywords
    return keywords.map(keyword => `"${keyword}"`).join(' OR ');
  }

  /**
   * Enhance search query with financial context
   */
  private enhanceSearchQuery(query: string): string {
    const financialTerms = ['finance', 'financial', 'economy', 'market', 'investment'];
    const hasFinancialContext = financialTerms.some(term => 
      query.toLowerCase().includes(term)
    );
    
    if (!hasFinancialContext) {
      return `${query} AND (finance OR economy OR market OR investment)`;
    }
    
    return query;
  }

  /**
   * Transform NewsAPI response to our format
   */
  private transformApiResponse(data: NewsAPIResponse, category?: NewsCategory): NewsResponse {
    const articles: NewsArticle[] = data.articles
      .filter(article => article.title && article.description)
      .map(article => ({
        id: this.generateArticleId(article.url),
        title: article.title,
        description: article.description,
        content: article.content || undefined,
        url: article.url,
        urlToImage: article.urlToImage || undefined,
        publishedAt: article.publishedAt,
        source: article.source,
        category: category || this.categorizeArticle(article.title + ' ' + article.description),
        relevanceScore: this.calculateRelevanceScore(article, category)
      }));

    return {
      status: 'ok',
      totalResults: data.totalResults,
      articles
    };
  }

  /**
   * Generate unique ID for article
   */
  private generateArticleId(url: string): string {
    return btoa(url).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
  }

  /**
   * Map category to segmentation ID
   */
  private mapCategoryToSegment(category: NewsCategory): string {
    const mapping: Record<NewsCategory, string> = {
      'india-specific': 'indian-markets',
      'global-markets': 'global-markets',
      'crypto-web3': 'crypto-web3',
      'stocks-companies': 'indian-markets', // Can be either, but default to Indian
      'macro-policy': 'global-markets',
      'general': 'indian-markets'
    };

    return mapping[category] || 'indian-markets';
  }

  /**
   * Basic category filtering as fallback
   */
  private basicCategoryFilter(articles: NewsArticle[], category: NewsCategory): NewsArticle[] {
    const categoryConfig = NEWS_CATEGORIES[category];
    if (!categoryConfig) return articles;

    return articles.filter(article => {
      const content = `${article.title} ${article.description}`.toLowerCase();

      // Check if article contains category keywords
      const keywordMatches = categoryConfig.keywords.filter(keyword =>
        content.includes(keyword.toLowerCase())
      ).length;

      // Check source preference
      const isPreferredSource = categoryConfig.sources?.some(source =>
        article.source.name.toLowerCase().includes(source.toLowerCase())
      ) || false;

      // Basic scoring: keyword matches or preferred source
      return keywordMatches >= 1 || isPreferredSource;
    }).map(article => ({
      ...article,
      category,
      relevanceScore: this.calculateBasicRelevance(article, categoryConfig)
    })).sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));
  }

  /**
   * Calculate basic relevance score
   */
  private calculateBasicRelevance(article: NewsArticle, categoryConfig: any): number {
    const content = `${article.title} ${article.description}`.toLowerCase();
    let score = 0.3; // Base score

    // Keyword matches
    const keywordMatches = categoryConfig.keywords.filter((keyword: string) =>
      content.includes(keyword.toLowerCase())
    ).length;
    score += (keywordMatches / categoryConfig.keywords.length) * 0.4;

    // Source preference
    const isPreferredSource = categoryConfig.sources?.some((source: string) =>
      article.source.name.toLowerCase().includes(source.toLowerCase())
    );
    if (isPreferredSource) score += 0.2;

    // Recency bonus
    const hoursAgo = (Date.now() - new Date(article.publishedAt).getTime()) / (1000 * 60 * 60);
    if (hoursAgo < 24) score += 0.1;

    return Math.min(score, 1.0);
  }

  /**
   * Categorize article based on content
   */
  private categorizeArticle(content: string): NewsCategory {
    const lowerContent = content.toLowerCase();
    
    for (const [category, config] of Object.entries(NEWS_CATEGORIES)) {
      const matchCount = config.keywords.filter(keyword => 
        lowerContent.includes(keyword.toLowerCase())
      ).length;
      
      if (matchCount >= 2) {
        return category as NewsCategory;
      }
    }
    
    return 'general';
  }

  /**
   * Calculate relevance score for article
   */
  private calculateRelevanceScore(article: any, category?: NewsCategory): number {
    let score = 0.5; // Base score
    
    if (category) {
      const config = NEWS_CATEGORIES[category];
      const content = (article.title + ' ' + article.description).toLowerCase();
      
      const matchCount = config.keywords.filter(keyword => 
        content.includes(keyword.toLowerCase())
      ).length;
      
      score += (matchCount / config.keywords.length) * 0.5;
    }
    
    // Boost score for recent articles
    const publishedDate = new Date(article.publishedAt);
    const hoursAgo = (Date.now() - publishedDate.getTime()) / (1000 * 60 * 60);
    
    if (hoursAgo < 24) {
      score += 0.2;
    } else if (hoursAgo < 72) {
      score += 0.1;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * Get mock news data for development
   */
  private getMockNews(query: NewsQuery): NewsResponse {
    const mockArticles: NewsArticle[] = [
      {
        id: 'mock1',
        title: 'RBI Announces New Monetary Policy Changes for Indian Banking Sector',
        description: 'Reserve Bank of India announces key policy rate changes affecting banking sector and inflation targets for the upcoming quarter. The repo rate decision impacts Sensex and Nifty movements.',
        url: 'https://example.com/rbi-policy',
        publishedAt: new Date().toISOString(),
        source: { id: 'rbi', name: 'Economic Times' },
        category: 'india-specific',
        relevanceScore: 0.9
      },
      {
        id: 'mock2',
        title: 'Sensex Hits New Record High as Indian Markets Rally',
        description: 'BSE Sensex and NSE Nifty reach unprecedented levels amid positive investor sentiment and strong corporate earnings from TCS, HDFC Bank, and Reliance Industries.',
        url: 'https://example.com/sensex-record',
        publishedAt: new Date(Date.now() - 1800000).toISOString(),
        source: { id: 'bse', name: 'Business Standard' },
        category: 'india-specific',
        relevanceScore: 0.95
      },
      {
        id: 'mock3',
        title: 'HDFC Bank Reports Strong Q3 Earnings, Stock Jumps 5%',
        description: 'HDFC Bank beats analyst expectations with robust quarterly results driven by improved NII and lower NPAs. Indian banking sector shows resilience.',
        url: 'https://example.com/hdfc-earnings',
        publishedAt: new Date(Date.now() - 3600000).toISOString(),
        source: { id: 'economic-times', name: 'Economic Times' },
        category: 'india-specific',
        relevanceScore: 0.85
      },
      {
        id: 'mock4',
        title: 'TCS Reports Strong Q3 Earnings, IT Sector Outlook Positive',
        description: 'Tata Consultancy Services beats analyst expectations with robust quarterly results, driven by digital transformation projects. Indian IT sector shows strong momentum.',
        url: 'https://example.com/tcs-earnings',
        publishedAt: new Date(Date.now() - 5400000).toISOString(),
        source: { id: 'economic-times', name: 'Economic Times' },
        category: 'india-specific',
        relevanceScore: 0.85
      },
      {
        id: 'mock5',
        title: 'India Inflation Data Shows Cooling Trend, RBI Policy Impact',
        description: 'Latest inflation figures indicate a gradual cooling in price pressures, providing relief to RBI policymakers and Indian consumers. Rupee strengthens against dollar.',
        url: 'https://example.com/inflation-data',
        publishedAt: new Date(Date.now() - 7200000).toISOString(),
        source: { id: 'rbi', name: 'Livemint' },
        category: 'india-specific',
        relevanceScore: 0.9
      },
      {
        id: 'mock6',
        title: 'Reliance Industries Announces Major Investment in Green Energy',
        description: 'Reliance Industries announces massive investment in renewable energy sector, boosting green energy stocks. Mukesh Ambani outlines sustainability roadmap for India.',
        url: 'https://example.com/reliance-green',
        publishedAt: new Date(Date.now() - 9000000).toISOString(),
        source: { id: 'reliance', name: 'Business Standard' },
        category: 'india-specific',
        relevanceScore: 0.8
      },
      {
        id: 'mock7',
        title: 'Indian Banking Sector Shows Strong Growth in Digital Payments',
        description: 'SBI, ICICI Bank, and other major Indian banks report significant growth in digital payment volumes. UPI transactions reach new milestone.',
        url: 'https://example.com/banking-digital',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'sbi', name: 'Economic Times' },
        category: 'india-specific',
        relevanceScore: 0.75
      },
      {
        id: 'mock8',
        title: 'Nifty 50 Crosses 22,000 Mark Amid FII Inflows',
        description: 'NSE Nifty 50 index crosses historic 22,000 level as Foreign Institutional Investors increase stake in Indian equities. Market cap hits new high.',
        url: 'https://example.com/nifty-22000',
        publishedAt: new Date(Date.now() - ********).toISOString(),
        source: { id: 'nse', name: 'Moneycontrol' },
        category: 'india-specific',
        relevanceScore: 0.9
      }
    ];

    // Filter articles based on query
    let filteredArticles = mockArticles;

    if (query.category) {
      filteredArticles = mockArticles.filter(article => article.category === query.category);
    }

    // If no articles match the category, return all articles (better than empty)
    if (filteredArticles.length === 0) {
      filteredArticles = mockArticles;
    }

    return {
      status: 'ok',
      totalResults: filteredArticles.length,
      articles: filteredArticles
    };
  }
}

export const newsFetchService = new NewsFetchService();
