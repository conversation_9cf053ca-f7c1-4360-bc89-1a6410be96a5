// News fetching service with NewsAPI integration

import {
  NewsArticle,
  NewsQuery,
  NewsResponse,
  NewsAPIResponse,
  NewsCategory,
  NEWS_CATEGORIES,
  NEWS_API_CONFIG
} from './types';
import { newsSegmentationService } from '../news-segmentation';

class NewsFetchService {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.NEXT_PUBLIC_NEWS_API_KEY || '';
    this.baseUrl = NEWS_API_CONFIG.BASE_URL;
    
    if (!this.apiKey) {
      console.warn('NewsAPI key not found. Using mock data for development.');
    }
  }

  /**
   * Fetch news articles based on query parameters
   */
  async fetchNews(query: NewsQuery): Promise<NewsResponse> {
    try {
      if (!this.apiKey) {
        return this.getMockNews(query);
      }

      const url = this.buildApiUrl(query);
      const response = await fetch(url, {
        headers: {
          'X-API-Key': this.apiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`NewsAPI error: ${response.status} ${response.statusText}`);
      }

      const data: NewsAPIResponse = await response.json();
      
      if (data.status !== 'ok') {
        throw new Error(`NewsAPI returned error status: ${data.status}`);
      }

      return this.transformApiResponse(data, query.category);
    } catch (error) {
      console.error('Error fetching news:', error);
      return {
        status: 'error',
        totalResults: 0,
        articles: [],
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Fetch news by category with optimized queries
   */
  async fetchNewsByCategory(category: NewsCategory): Promise<NewsResponse> {
    const query: NewsQuery = {
      query: this.buildCategoryQuery(category),
      category,
      sortBy: 'publishedAt',
      pageSize: 50 // Fetch more articles for better segmentation
    };

    const response = await this.fetchNews(query);

    if (response.status === 'ok' && response.articles.length > 0) {
      // Use advanced segmentation to get the most relevant articles
      const segmentId = this.mapCategoryToSegment(category);
      const segmentedResult = await newsSegmentationService.getSegmentedNews(
        response.articles,
        segmentId,
        {
          minRelevanceScore: 0.4,
          maxArticles: 20,
          sortBy: 'relevance'
        }
      );

      return {
        status: 'ok',
        totalResults: segmentedResult.totalRelevant,
        articles: segmentedResult.articles
      };
    }

    return response;
  }

  /**
   * Search news with custom query
   */
  async searchNews(searchQuery: string, filters?: Partial<NewsQuery>): Promise<NewsResponse> {
    const query: NewsQuery = {
      query: this.enhanceSearchQuery(searchQuery),
      sortBy: 'relevancy',
      pageSize: 30,
      ...filters
    };

    return this.fetchNews(query);
  }

  /**
   * Get trending financial news
   */
  async getTrendingNews(): Promise<NewsResponse> {
    const query: NewsQuery = {
      query: 'finance OR economy OR stock market OR investment',
      sortBy: 'popularity',
      pageSize: 15
    };

    return this.fetchNews(query);
  }

  /**
   * Build API URL with query parameters
   */
  private buildApiUrl(query: NewsQuery): string {
    const endpoint = query.query ? 
      NEWS_API_CONFIG.ENDPOINTS.EVERYTHING : 
      NEWS_API_CONFIG.ENDPOINTS.TOP_HEADLINES;
    
    const url = new URL(this.baseUrl + endpoint);
    
    // Add query parameters
    if (query.query) {
      url.searchParams.append('q', query.query);
    }
    
    if (query.sortBy) {
      url.searchParams.append('sortBy', query.sortBy);
    }
    
    if (query.from) {
      url.searchParams.append('from', query.from);
    }
    
    if (query.to) {
      url.searchParams.append('to', query.to);
    }
    
    url.searchParams.append('language', query.language || NEWS_API_CONFIG.DEFAULT_PARAMS.language);
    url.searchParams.append('pageSize', (query.pageSize || NEWS_API_CONFIG.DEFAULT_PARAMS.pageSize).toString());
    
    if (query.page) {
      url.searchParams.append('page', query.page.toString());
    }

    return url.toString();
  }

  /**
   * Build optimized query for specific categories
   */
  private buildCategoryQuery(category: NewsCategory): string {
    const config = NEWS_CATEGORIES[category];
    const keywords = config.keywords.slice(0, 5); // Limit to avoid URL length issues
    
    // Create OR query with keywords
    return keywords.map(keyword => `"${keyword}"`).join(' OR ');
  }

  /**
   * Enhance search query with financial context
   */
  private enhanceSearchQuery(query: string): string {
    const financialTerms = ['finance', 'financial', 'economy', 'market', 'investment'];
    const hasFinancialContext = financialTerms.some(term => 
      query.toLowerCase().includes(term)
    );
    
    if (!hasFinancialContext) {
      return `${query} AND (finance OR economy OR market OR investment)`;
    }
    
    return query;
  }

  /**
   * Transform NewsAPI response to our format
   */
  private transformApiResponse(data: NewsAPIResponse, category?: NewsCategory): NewsResponse {
    const articles: NewsArticle[] = data.articles
      .filter(article => article.title && article.description)
      .map(article => ({
        id: this.generateArticleId(article.url),
        title: article.title,
        description: article.description,
        content: article.content || undefined,
        url: article.url,
        urlToImage: article.urlToImage || undefined,
        publishedAt: article.publishedAt,
        source: article.source,
        category: category || this.categorizeArticle(article.title + ' ' + article.description),
        relevanceScore: this.calculateRelevanceScore(article, category)
      }));

    return {
      status: 'ok',
      totalResults: data.totalResults,
      articles
    };
  }

  /**
   * Generate unique ID for article
   */
  private generateArticleId(url: string): string {
    return btoa(url).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
  }

  /**
   * Map category to segmentation ID
   */
  private mapCategoryToSegment(category: NewsCategory): string {
    const mapping: Record<NewsCategory, string> = {
      'india-specific': 'indian-markets',
      'global-markets': 'global-markets',
      'crypto-web3': 'crypto-web3',
      'stocks-companies': 'indian-markets', // Can be either, but default to Indian
      'macro-policy': 'global-markets',
      'general': 'indian-markets'
    };

    return mapping[category] || 'indian-markets';
  }

  /**
   * Categorize article based on content
   */
  private categorizeArticle(content: string): NewsCategory {
    const lowerContent = content.toLowerCase();
    
    for (const [category, config] of Object.entries(NEWS_CATEGORIES)) {
      const matchCount = config.keywords.filter(keyword => 
        lowerContent.includes(keyword.toLowerCase())
      ).length;
      
      if (matchCount >= 2) {
        return category as NewsCategory;
      }
    }
    
    return 'general';
  }

  /**
   * Calculate relevance score for article
   */
  private calculateRelevanceScore(article: any, category?: NewsCategory): number {
    let score = 0.5; // Base score
    
    if (category) {
      const config = NEWS_CATEGORIES[category];
      const content = (article.title + ' ' + article.description).toLowerCase();
      
      const matchCount = config.keywords.filter(keyword => 
        content.includes(keyword.toLowerCase())
      ).length;
      
      score += (matchCount / config.keywords.length) * 0.5;
    }
    
    // Boost score for recent articles
    const publishedDate = new Date(article.publishedAt);
    const hoursAgo = (Date.now() - publishedDate.getTime()) / (1000 * 60 * 60);
    
    if (hoursAgo < 24) {
      score += 0.2;
    } else if (hoursAgo < 72) {
      score += 0.1;
    }
    
    return Math.min(score, 1.0);
  }

  /**
   * Get mock news data for development
   */
  private getMockNews(query: NewsQuery): NewsResponse {
    const mockArticles: NewsArticle[] = [
      {
        id: 'mock1',
        title: 'RBI Announces New Monetary Policy Changes',
        description: 'Reserve Bank of India announces key policy rate changes affecting banking sector and inflation targets for the upcoming quarter.',
        url: 'https://example.com/rbi-policy',
        publishedAt: new Date().toISOString(),
        source: { id: 'rbi', name: 'Reserve Bank of India' },
        category: 'india-specific',
        relevanceScore: 0.9
      },
      {
        id: 'mock2',
        title: 'Global Markets React to Fed Decision',
        description: 'International markets show mixed reactions following Federal Reserve interest rate announcement, with Asian markets leading the volatility.',
        url: 'https://example.com/fed-markets',
        publishedAt: new Date(Date.now() - 3600000).toISOString(),
        source: { id: 'reuters', name: 'Reuters' },
        category: 'global-markets',
        relevanceScore: 0.8
      },
      {
        id: 'mock3',
        title: 'Bitcoin Surges on Institutional Adoption',
        description: 'Cryptocurrency markets rally as major institutions announce Bitcoin adoption strategies, pushing prices to new monthly highs.',
        url: 'https://example.com/bitcoin-surge',
        publishedAt: new Date(Date.now() - 7200000).toISOString(),
        source: { id: 'coindesk', name: 'CoinDesk' },
        category: 'crypto-web3',
        relevanceScore: 0.7
      },
      {
        id: 'mock4',
        title: 'TCS Reports Strong Q3 Earnings',
        description: 'Tata Consultancy Services beats analyst expectations with robust quarterly results, driven by digital transformation projects.',
        url: 'https://example.com/tcs-earnings',
        publishedAt: new Date(Date.now() - 10800000).toISOString(),
        source: { id: 'economic-times', name: 'Economic Times' },
        category: 'stocks-companies',
        relevanceScore: 0.85
      },
      {
        id: 'mock5',
        title: 'Inflation Data Shows Cooling Trend',
        description: 'Latest inflation figures indicate a gradual cooling in price pressures, providing relief to policymakers and consumers.',
        url: 'https://example.com/inflation-data',
        publishedAt: new Date(Date.now() - 14400000).toISOString(),
        source: { id: 'bloomberg', name: 'Bloomberg' },
        category: 'macro-policy',
        relevanceScore: 0.9
      },
      {
        id: 'mock6',
        title: 'Sensex Hits New Record High',
        description: 'Indian stock market benchmark Sensex reaches unprecedented levels amid positive investor sentiment and strong corporate earnings.',
        url: 'https://example.com/sensex-record',
        publishedAt: new Date(Date.now() - 18000000).toISOString(),
        source: { id: 'bse', name: 'BSE' },
        category: 'india-specific',
        relevanceScore: 0.95
      }
    ];

    return {
      status: 'ok',
      totalResults: mockArticles.length,
      articles: mockArticles.filter(article =>
        !query.category || article.category === query.category
      )
    };
  }
}

export const newsFetchService = new NewsFetchService();
