// News search and filtering service

import { NewsArticle, NewsQuery, NewsResponse, SearchFilters, NewsCategory } from './types';
import { newsFetchService } from './fetch';

class NewsSearchService {
  private searchCache = new Map<string, { data: NewsResponse; timestamp: number }>();
  private readonly cacheTimeout = 5 * 60 * 1000; // 5 minutes

  /**
   * Search news with advanced filtering and sorting
   */
  async searchNews(
    query: string, 
    filters?: SearchFilters,
    sortBy: 'publishedAt' | 'relevancy' | 'popularity' = 'publishedAt'
  ): Promise<NewsResponse> {
    try {
      const cacheKey = this.generateCacheKey(query, filters, sortBy);
      
      // Check cache first
      const cached = this.getCachedResult(cacheKey);
      if (cached) {
        return cached;
      }

      // Build search query
      const searchQuery = this.buildSearchQuery(query, filters);
      
      // Fetch news
      const response = await newsFetchService.searchNews(searchQuery, {
        sortBy,
        pageSize: 50, // Get more results for better filtering
        category: filters?.category
      });

      if (response.status === 'error') {
        return response;
      }

      // Apply additional filtering
      let filteredArticles = this.applyFilters(response.articles, filters);
      
      // Apply custom sorting
      filteredArticles = this.sortArticles(filteredArticles, sortBy);
      
      // Calculate relevance scores
      filteredArticles = this.enhanceRelevanceScores(filteredArticles, query);

      const result: NewsResponse = {
        status: 'ok',
        totalResults: filteredArticles.length,
        articles: filteredArticles.slice(0, 30) // Limit final results
      };

      // Cache result
      this.cacheResult(cacheKey, result);
      
      return result;
    } catch (error) {
      console.error('Error searching news:', error);
      return {
        status: 'error',
        totalResults: 0,
        articles: [],
        message: error instanceof Error ? error.message : 'Search failed'
      };
    }
  }

  /**
   * Get trending searches and topics
   */
  async getTrendingTopics(): Promise<string[]> {
    const trendingQueries = [
      'inflation India',
      'Federal Reserve policy',
      'Bitcoin price',
      'stock market today',
      'RBI interest rates',
      'Sensex Nifty',
      'cryptocurrency regulation',
      'earnings report',
      'IPO news',
      'economic indicators'
    ];

    return trendingQueries;
  }

  /**
   * Get search suggestions based on partial query
   */
  async getSearchSuggestions(partialQuery: string): Promise<string[]> {
    const suggestions: string[] = [];
    const query = partialQuery.toLowerCase().trim();

    if (query.length < 2) {
      return [];
    }

    // Financial terms and companies
    const financialTerms = [
      'inflation in India',
      'interest rates',
      'stock market crash',
      'cryptocurrency news',
      'RBI monetary policy',
      'Sensex today',
      'Nifty 50',
      'Bitcoin price',
      'Ethereum news',
      'Federal Reserve',
      'economic recession',
      'GDP growth',
      'unemployment rate',
      'fiscal policy',
      'banking sector',
      'mutual funds',
      'IPO listing',
      'earnings report',
      'dividend announcement',
      'merger acquisition'
    ];

    // Company names
    const companies = [
      'TCS earnings',
      'Reliance Industries',
      'HDFC Bank',
      'Infosys results',
      'Wipro news',
      'ICICI Bank',
      'SBI updates',
      'Adani Group',
      'Tata Motors',
      'Bajaj Finance'
    ];

    const allSuggestions = [...financialTerms, ...companies];

    // Filter suggestions based on partial query
    for (const suggestion of allSuggestions) {
      if (suggestion.toLowerCase().includes(query)) {
        suggestions.push(suggestion);
      }
    }

    return suggestions.slice(0, 8); // Limit to 8 suggestions
  }

  /**
   * Build optimized search query
   */
  private buildSearchQuery(query: string, filters?: SearchFilters): string {
    let enhancedQuery = query.trim();

    // Add financial context if not present
    const hasFinancialContext = /\b(finance|financial|economy|market|investment|stock|crypto|bank|money|trading|business)\b/i.test(query);
    
    if (!hasFinancialContext) {
      enhancedQuery += ' AND (finance OR economy OR market OR business)';
    }

    // Add category-specific terms
    if (filters?.category && filters.category !== 'general') {
      const categoryTerms = this.getCategoryTerms(filters.category);
      if (categoryTerms.length > 0) {
        enhancedQuery += ` AND (${categoryTerms.join(' OR ')})`;
      }
    }

    return enhancedQuery;
  }

  /**
   * Get search terms for specific category
   */
  private getCategoryTerms(category: NewsCategory): string[] {
    const categoryTerms: Record<NewsCategory, string[]> = {
      'india-specific': ['India', 'Indian', 'RBI', 'Sensex', 'Nifty', 'rupee'],
      'global-markets': ['global', 'international', 'world', 'Fed', 'Federal Reserve'],
      'crypto-web3': ['crypto', 'Bitcoin', 'Ethereum', 'blockchain', 'DeFi'],
      'stocks-companies': ['stock', 'shares', 'company', 'earnings', 'IPO'],
      'macro-policy': ['policy', 'inflation', 'interest rate', 'GDP', 'economic'],
      'general': []
    };

    return categoryTerms[category] || [];
  }

  /**
   * Apply advanced filters to articles
   */
  private applyFilters(articles: NewsArticle[], filters?: SearchFilters): NewsArticle[] {
    if (!filters) {
      return articles;
    }

    return articles.filter(article => {
      // Date range filter
      if (filters.dateRange) {
        const articleDate = new Date(article.publishedAt);
        if (articleDate < filters.dateRange.from || articleDate > filters.dateRange.to) {
          return false;
        }
      }

      // Source filter
      if (filters.sources && filters.sources.length > 0) {
        if (!filters.sources.includes(article.source.name.toLowerCase())) {
          return false;
        }
      }

      // Sentiment filter
      if (filters.sentiment && article.sentiment && article.sentiment !== filters.sentiment) {
        return false;
      }

      // Minimum relevance score
      if (filters.minRelevanceScore && article.relevanceScore && article.relevanceScore < filters.minRelevanceScore) {
        return false;
      }

      return true;
    });
  }

  /**
   * Sort articles by specified criteria
   */
  private sortArticles(articles: NewsArticle[], sortBy: string): NewsArticle[] {
    const sorted = [...articles];

    switch (sortBy) {
      case 'publishedAt':
        return sorted.sort((a, b) => new Date(b.publishedAt).getTime() - new Date(a.publishedAt).getTime());
      
      case 'relevancy':
        return sorted.sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0));
      
      case 'popularity':
        // Sort by source credibility and recency
        return sorted.sort((a, b) => {
          const aScore = this.calculatePopularityScore(a);
          const bScore = this.calculatePopularityScore(b);
          return bScore - aScore;
        });
      
      default:
        return sorted;
    }
  }

  /**
   * Calculate popularity score for sorting
   */
  private calculatePopularityScore(article: NewsArticle): number {
    let score = article.relevanceScore || 0.5;

    // Boost score for reputable sources
    const reputableSources = ['reuters', 'bloomberg', 'financial times', 'wall street journal', 'economic times'];
    if (reputableSources.some(source => article.source.name.toLowerCase().includes(source))) {
      score += 0.2;
    }

    // Boost score for recent articles
    const hoursAgo = (Date.now() - new Date(article.publishedAt).getTime()) / (1000 * 60 * 60);
    if (hoursAgo < 6) {
      score += 0.3;
    } else if (hoursAgo < 24) {
      score += 0.1;
    }

    return score;
  }

  /**
   * Enhance relevance scores based on search query
   */
  private enhanceRelevanceScores(articles: NewsArticle[], query: string): NewsArticle[] {
    const queryTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 2);

    return articles.map(article => {
      let relevanceBoost = 0;
      const content = (article.title + ' ' + article.description).toLowerCase();

      // Count query term matches
      const matchCount = queryTerms.filter(term => content.includes(term)).length;
      relevanceBoost += (matchCount / queryTerms.length) * 0.3;

      // Boost for title matches
      const titleMatches = queryTerms.filter(term => article.title.toLowerCase().includes(term)).length;
      relevanceBoost += (titleMatches / queryTerms.length) * 0.2;

      return {
        ...article,
        relevanceScore: Math.min((article.relevanceScore || 0.5) + relevanceBoost, 1.0)
      };
    });
  }

  /**
   * Generate cache key for search results
   */
  private generateCacheKey(query: string, filters?: SearchFilters, sortBy?: string): string {
    const filterStr = filters ? JSON.stringify(filters) : '';
    return `${query}|${filterStr}|${sortBy}`;
  }

  /**
   * Get cached search result if valid
   */
  private getCachedResult(cacheKey: string): NewsResponse | null {
    const cached = this.searchCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  /**
   * Cache search result
   */
  private cacheResult(cacheKey: string, result: NewsResponse): void {
    this.searchCache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    });

    // Clean old cache entries
    if (this.searchCache.size > 100) {
      const oldestKey = this.searchCache.keys().next().value;
      this.searchCache.delete(oldestKey);
    }
  }
}

export const newsSearchService = new NewsSearchService();
