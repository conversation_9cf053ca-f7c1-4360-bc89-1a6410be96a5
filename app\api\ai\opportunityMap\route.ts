import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || process.env.NEXT_PUBLIC_GEMINI_API_KEY || '');

interface StockData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
  high52Week: number;
  low52Week: number;
  pe: number;
  dividend: number;
  lastUpdate: string;
}

interface AIInsight {
  type: 'opportunity' | 'risk' | 'neutral';
  confidence: number;
  signal: string;
  reasoning: string;
  timeframe: string;
  actionSuggestion: string;
}

export async function POST(request: NextRequest) {
  try {
    const { ticker, stockData }: { ticker: string; stockData: StockData } = await request.json();
    
    if (!ticker || !stockData) {
      return NextResponse.json({ error: 'Missing ticker or stock data' }, { status: 400 });
    }
    
    // Generate AI insights using Gemini
    const insights = await generateAIInsights(ticker, stockData);
    
    return NextResponse.json(insights);
  } catch (error) {
    console.error('Error generating AI insights:', error);
    
    // Return mock insights as fallback
    const mockInsights = generateMockInsights(request.body?.ticker || 'UNKNOWN');
    return NextResponse.json(mockInsights);
  }
}

async function generateAIInsights(ticker: string, stockData: StockData): Promise<AIInsight[]> {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    // Calculate technical indicators for context
    const pricePosition = ((stockData.price - stockData.low52Week) / (stockData.high52Week - stockData.low52Week)) * 100;
    const volumeAnalysis = stockData.volume > 10000000 ? 'high' : stockData.volume > 1000000 ? 'normal' : 'low';
    const peAnalysis = stockData.pe > 30 ? 'high' : stockData.pe > 15 ? 'normal' : 'low';
    
    const prompt = `
    As a professional financial analyst, analyze the following stock data for ${ticker} and provide 2-3 specific investment insights:

    Stock Data:
    - Symbol: ${stockData.symbol}
    - Current Price: $${stockData.price}
    - Daily Change: ${stockData.change >= 0 ? '+' : ''}${stockData.change} (${stockData.changePercent}%)
    - Volume: ${stockData.volume.toLocaleString()}
    - Market Cap: $${stockData.marketCap.toLocaleString()}
    - 52-Week Range: $${stockData.low52Week} - $${stockData.high52Week}
    - P/E Ratio: ${stockData.pe}
    - Current position in 52-week range: ${pricePosition.toFixed(1)}%
    - Volume level: ${volumeAnalysis}
    - P/E level: ${peAnalysis}

    For each insight, provide:
    1. Type: "opportunity", "risk", or "neutral"
    2. Confidence level (0-100)
    3. Signal name (e.g., "Bullish Momentum", "Overbought Warning")
    4. Detailed reasoning (2-3 sentences)
    5. Timeframe (e.g., "Short-term (1-2 weeks)")
    6. Action suggestion (specific advice)

    Focus on technical analysis, valuation metrics, and market positioning. Be specific and actionable.
    
    Return the response as a JSON array of insights.
    `;
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    // Try to parse the AI response as JSON
    try {
      const insights = JSON.parse(text);
      if (Array.isArray(insights) && insights.length > 0) {
        return insights.map(insight => ({
          type: insight.type || 'neutral',
          confidence: Math.min(Math.max(insight.confidence || 50, 0), 100),
          signal: insight.signal || 'Market Analysis',
          reasoning: insight.reasoning || 'Analysis based on current market conditions.',
          timeframe: insight.timeframe || 'Medium-term',
          actionSuggestion: insight.actionSuggestion || 'Monitor closely for changes.'
        }));
      }
    } catch (parseError) {
      console.error('Failed to parse AI response as JSON:', parseError);
    }
    
    // If JSON parsing fails, generate insights based on the stock data
    return generateInsightsFromData(ticker, stockData);
    
  } catch (error) {
    console.error('Gemini AI error:', error);
    return generateInsightsFromData(ticker, stockData);
  }
}

function generateInsightsFromData(ticker: string, stockData: StockData): AIInsight[] {
  const insights: AIInsight[] = [];
  
  // Calculate position in 52-week range
  const pricePosition = ((stockData.price - stockData.low52Week) / (stockData.high52Week - stockData.low52Week)) * 100;
  
  // Momentum analysis
  if (stockData.changePercent > 3) {
    insights.push({
      type: 'opportunity',
      confidence: 75,
      signal: 'Strong Bullish Momentum',
      reasoning: `${ticker} is showing strong upward momentum with a ${stockData.changePercent.toFixed(1)}% gain today. High volume suggests institutional interest and potential continuation of the trend.`,
      timeframe: 'Short-term (1-2 weeks)',
      actionSuggestion: 'Consider buying on any pullbacks to support levels. Set stop-loss at recent swing low.'
    });
  } else if (stockData.changePercent < -3) {
    insights.push({
      type: 'risk',
      confidence: 70,
      signal: 'Bearish Pressure',
      reasoning: `${ticker} is experiencing significant selling pressure with a ${Math.abs(stockData.changePercent).toFixed(1)}% decline. This could indicate profit-taking or negative sentiment.`,
      timeframe: 'Short-term (1-2 weeks)',
      actionSuggestion: 'Wait for stabilization before entering. Look for support levels to hold.'
    });
  }
  
  // Position in range analysis
  if (pricePosition > 85) {
    insights.push({
      type: 'risk',
      confidence: 65,
      signal: 'Near 52-Week High',
      reasoning: `${ticker} is trading at ${pricePosition.toFixed(1)}% of its 52-week range, suggesting potential resistance. May face selling pressure from profit-taking.`,
      timeframe: 'Medium-term (2-4 weeks)',
      actionSuggestion: 'Consider taking partial profits or waiting for a pullback to enter.'
    });
  } else if (pricePosition < 25) {
    insights.push({
      type: 'opportunity',
      confidence: 70,
      signal: 'Value Territory',
      reasoning: `${ticker} is trading in the lower ${pricePosition.toFixed(1)}% of its 52-week range, potentially offering good value for long-term investors.`,
      timeframe: 'Long-term (3-6 months)',
      actionSuggestion: 'Consider accumulating on weakness. Good risk-reward ratio at current levels.'
    });
  }
  
  // P/E analysis
  if (stockData.pe > 35) {
    insights.push({
      type: 'risk',
      confidence: 60,
      signal: 'High Valuation',
      reasoning: `With a P/E ratio of ${stockData.pe}, ${ticker} appears expensive relative to historical norms. Vulnerable to multiple compression if growth slows.`,
      timeframe: 'Medium-term (1-3 months)',
      actionSuggestion: 'Monitor earnings quality and growth sustainability. Consider reducing position size.'
    });
  } else if (stockData.pe < 15 && stockData.pe > 0) {
    insights.push({
      type: 'opportunity',
      confidence: 65,
      signal: 'Attractive Valuation',
      reasoning: `${ticker}'s P/E ratio of ${stockData.pe} suggests the stock may be undervalued relative to the market. Could indicate a buying opportunity.`,
      timeframe: 'Medium-term (2-6 months)',
      actionSuggestion: 'Research fundamentals to confirm value. Consider gradual accumulation.'
    });
  }
  
  // Ensure we have at least 2 insights
  if (insights.length < 2) {
    insights.push({
      type: 'neutral',
      confidence: 55,
      signal: 'Consolidation Phase',
      reasoning: `${ticker} appears to be in a consolidation phase with moderate trading activity. Waiting for a clear directional breakout.`,
      timeframe: 'Short-term (1-2 weeks)',
      actionSuggestion: 'Monitor for breakout above resistance or breakdown below support levels.'
    });
  }
  
  return insights.slice(0, 3); // Return maximum 3 insights
}

function generateMockInsights(ticker: string): AIInsight[] {
  return [
    {
      type: 'opportunity',
      confidence: 75,
      signal: 'Bullish Momentum',
      reasoning: `${ticker} is showing strong upward momentum with increasing volume and breaking above key resistance levels. Technical indicators suggest continued strength.`,
      timeframe: 'Short-term (1-2 weeks)',
      actionSuggestion: 'Consider buying on pullbacks to support levels. Set stop-loss below recent swing low.'
    },
    {
      type: 'risk',
      confidence: 60,
      signal: 'Overbought Conditions',
      reasoning: 'RSI is approaching overbought territory above 70, indicating potential for short-term correction or consolidation.',
      timeframe: 'Immediate (1-3 days)',
      actionSuggestion: 'Wait for RSI to cool down before entering new positions. Consider taking partial profits.'
    }
  ];
}
