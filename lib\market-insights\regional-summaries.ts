// Regional Market Summaries Engine

import { RegionalMarketSummary, MarketData } from './types';

export class RegionalSummariesEngine {
  
  // Market Indices Configuration
  static readonly MARKET_INDICES = {
    india: [
      { name: 'Nifty 50', symbol: 'NIFTY', description: 'NSE benchmark index' },
      { name: 'Sensex', symbol: 'SENSEX', description: 'BSE benchmark index' },
      { name: 'Nifty Bank', symbol: 'BANKNIFTY', description: 'Banking sector index' },
      { name: 'Nifty IT', symbol: 'NIFTYIT', description: 'IT sector index' },
      { name: 'Nifty Pharma', symbol: 'NIFTYPHARMA', description: 'Pharma sector index' }
    ],
    global: [
      { name: 'S&P 500', symbol: 'SPX', description: 'US large-cap index' },
      { name: 'Nasdaq', symbol: 'IXIC', description: 'US tech-heavy index' },
      { name: '<PERSON> Jones', symbol: 'DJI', description: 'US industrial average' },
      { name: 'FTSE 100', symbol: 'UKX', description: 'UK benchmark index' },
      { name: 'Nikkei 225', symbol: 'NKY', description: 'Japan benchmark index' },
      { name: 'DAX', symbol: 'DAX', description: 'German benchmark index' },
      { name: 'CAC 40', symbol: 'CAC', description: 'French benchmark index' }
    ],
    crypto: [
      { name: 'Bitcoin', symbol: 'BTC', description: 'Leading cryptocurrency' },
      { name: 'Ethereum', symbol: 'ETH', description: 'Smart contract platform' },
      { name: 'Solana', symbol: 'SOL', description: 'High-performance blockchain' },
      { name: 'Cardano', symbol: 'ADA', description: 'Proof-of-stake blockchain' },
      { name: 'Polygon', symbol: 'MATIC', description: 'Ethereum scaling solution' }
    ]
  };
  
  // Indian Market Sectors
  static readonly INDIAN_SECTORS = [
    'IT', 'Banking', 'Pharma', 'Auto', 'FMCG', 'Energy', 'Metals', 'Telecom'
  ];
  
  // Generate India Market Summary
  static generateIndiaMarketSummary(
    indicesData: Map<string, MarketData>,
    stocksData: MarketData[],
    sectorPerformance: Map<string, number>
  ): RegionalMarketSummary {
    // Get index data
    const indices = this.MARKET_INDICES.india.map(index => {
      const data = indicesData.get(index.symbol);
      return {
        name: index.name,
        symbol: index.symbol,
        value: data?.price || 0,
        change: data?.change || 0,
        changePercent: data?.changePercent || 0
      };
    });
    
    // Get top gainers and losers
    const sortedStocks = [...stocksData].sort((a, b) => b.changePercent - a.changePercent);
    const topGainers = sortedStocks.slice(0, 5);
    const topLosers = sortedStocks.slice(-5).reverse();
    
    // Generate sector heatmap
    const sectorHeatmap = this.INDIAN_SECTORS.map(sector => ({
      sector,
      performance: sectorPerformance.get(sector) || 0,
      color: this.getSectorColor(sectorPerformance.get(sector) || 0)
    }));
    
    // Generate summary
    const niftyData = indicesData.get('NIFTY');
    const sensexData = indicesData.get('SENSEX');
    const summary = this.generateIndiaSummary(niftyData, sensexData, sectorHeatmap);
    
    // Generate key events
    const keyEvents = this.generateIndiaKeyEvents(indices, topGainers, topLosers);
    
    return {
      region: 'india',
      indices,
      topGainers,
      topLosers,
      sectorHeatmap,
      summary,
      keyEvents
    };
  }
  
  // Generate Global Market Summary
  static generateGlobalMarketSummary(
    indicesData: Map<string, MarketData>
  ): RegionalMarketSummary {
    // Get index data
    const indices = this.MARKET_INDICES.global.map(index => {
      const data = indicesData.get(index.symbol);
      return {
        name: index.name,
        symbol: index.symbol,
        value: data?.price || 0,
        change: data?.change || 0,
        changePercent: data?.changePercent || 0
      };
    });
    
    // Generate summary
    const summary = this.generateGlobalSummary(indices);
    
    // Generate key events
    const keyEvents = this.generateGlobalKeyEvents(indices);
    
    return {
      region: 'global',
      indices,
      topGainers: [], // Would need global stocks data
      topLosers: [],
      sectorHeatmap: [], // Would need global sector data
      summary,
      keyEvents
    };
  }
  
  // Generate Crypto Market Summary
  static generateCryptoMarketSummary(
    cryptoData: Map<string, MarketData>
  ): RegionalMarketSummary {
    // Get crypto data as indices
    const indices = this.MARKET_INDICES.crypto.map(crypto => {
      const data = cryptoData.get(crypto.symbol);
      return {
        name: crypto.name,
        symbol: crypto.symbol,
        value: data?.price || 0,
        change: data?.change || 0,
        changePercent: data?.changePercent || 0
      };
    });
    
    // Sort for top gainers/losers
    const sortedCrypto = [...indices].sort((a, b) => b.changePercent - a.changePercent);
    const topGainers = sortedCrypto.slice(0, 3);
    const topLosers = sortedCrypto.slice(-3).reverse();
    
    // Generate summary
    const summary = this.generateCryptoSummary(indices);
    
    // Generate key events
    const keyEvents = this.generateCryptoKeyEvents(indices);
    
    return {
      region: 'crypto',
      indices,
      topGainers: topGainers.map(this.convertToMarketData),
      topLosers: topLosers.map(this.convertToMarketData),
      sectorHeatmap: [], // Crypto doesn't have traditional sectors
      summary,
      keyEvents
    };
  }
  
  // Generate India Summary
  static generateIndiaSummary(
    niftyData?: MarketData,
    sensexData?: MarketData,
    sectorHeatmap?: any[]
  ): string {
    if (!niftyData || !sensexData) {
      return "Indian markets data unavailable";
    }
    
    const niftyDirection = niftyData.changePercent > 0 ? "gained" : "declined";
    const sensexDirection = sensexData.changePercent > 0 ? "gained" : "declined";
    
    let summary = `Indian markets ${niftyDirection} today with Nifty ${niftyDirection} ${Math.abs(niftyData.changePercent).toFixed(2)}% to ${niftyData.price.toFixed(0)} and Sensex ${sensexDirection} ${Math.abs(sensexData.changePercent).toFixed(2)}% to ${sensexData.price.toFixed(0)}. `;
    
    // Add sector performance
    if (sectorHeatmap && sectorHeatmap.length > 0) {
      const bestSector = sectorHeatmap.reduce((best, sector) => 
        sector.performance > best.performance ? sector : best
      );
      const worstSector = sectorHeatmap.reduce((worst, sector) => 
        sector.performance < worst.performance ? sector : worst
      );
      
      summary += `${bestSector.sector} sector led gains (+${bestSector.performance.toFixed(2)}%) while ${worstSector.sector} lagged (${worstSector.performance.toFixed(2)}%). `;
    }
    
    // Add market sentiment
    const avgChange = (niftyData.changePercent + sensexData.changePercent) / 2;
    if (Math.abs(avgChange) > 1) {
      summary += avgChange > 0 
        ? "Broad-based buying supported by institutional flows and positive sentiment."
        : "Selling pressure across sectors amid concerns over global cues and profit booking.";
    } else {
      summary += "Markets traded in a narrow range with mixed sectoral performance.";
    }
    
    return summary;
  }
  
  // Generate Global Summary
  static generateGlobalSummary(indices: any[]): string {
    const usIndices = indices.filter(idx => ['SPX', 'IXIC', 'DJI'].includes(idx.symbol));
    const europeanIndices = indices.filter(idx => ['UKX', 'DAX', 'CAC'].includes(idx.symbol));
    const asianIndices = indices.filter(idx => ['NKY'].includes(idx.symbol));
    
    let summary = "Global markets showed ";
    
    // US markets
    const usAvg = usIndices.reduce((sum, idx) => sum + idx.changePercent, 0) / usIndices.length;
    summary += `mixed performance with US markets ${usAvg > 0 ? 'gaining' : 'declining'} ${Math.abs(usAvg).toFixed(2)}% on average. `;
    
    // European markets
    if (europeanIndices.length > 0) {
      const euAvg = europeanIndices.reduce((sum, idx) => sum + idx.changePercent, 0) / europeanIndices.length;
      summary += `European indices ${euAvg > 0 ? 'advanced' : 'retreated'} ${Math.abs(euAvg).toFixed(2)}%. `;
    }
    
    // Asian markets
    if (asianIndices.length > 0) {
      const asiaAvg = asianIndices.reduce((sum, idx) => sum + idx.changePercent, 0) / asianIndices.length;
      summary += `Asian markets ${asiaAvg > 0 ? 'closed higher' : 'ended lower'} by ${Math.abs(asiaAvg).toFixed(2)}%.`;
    }
    
    return summary;
  }
  
  // Generate Crypto Summary
  static generateCryptoSummary(indices: any[]): string {
    const btcData = indices.find(idx => idx.symbol === 'BTC');
    const ethData = indices.find(idx => idx.symbol === 'ETH');
    
    if (!btcData || !ethData) {
      return "Cryptocurrency market data unavailable";
    }
    
    const avgChange = indices.reduce((sum, idx) => sum + idx.changePercent, 0) / indices.length;
    const direction = avgChange > 0 ? "rallied" : "declined";
    
    let summary = `Cryptocurrency markets ${direction} with Bitcoin ${btcData.changePercent > 0 ? 'gaining' : 'falling'} ${Math.abs(btcData.changePercent).toFixed(2)}% to $${btcData.value.toFixed(0)} and Ethereum ${ethData.changePercent > 0 ? 'rising' : 'dropping'} ${Math.abs(ethData.changePercent).toFixed(2)}% to $${ethData.value.toFixed(0)}. `;
    
    // Add market sentiment
    if (Math.abs(avgChange) > 5) {
      summary += avgChange > 0 
        ? "Strong buying momentum across altcoins suggests renewed institutional interest and risk-on sentiment."
        : "Broad-based selling pressure indicates risk-off sentiment and potential regulatory concerns.";
    } else {
      summary += "Consolidation continues with mixed performance across major cryptocurrencies.";
    }
    
    return summary;
  }
  
  // Generate India Key Events
  static generateIndiaKeyEvents(indices: any[], topGainers: MarketData[], topLosers: MarketData[]): string[] {
    const events: string[] = [];
    
    // Index movements
    const nifty = indices.find(idx => idx.symbol === 'NIFTY');
    if (nifty && Math.abs(nifty.changePercent) > 1) {
      events.push(`Nifty ${nifty.changePercent > 0 ? 'surged' : 'tumbled'} ${Math.abs(nifty.changePercent).toFixed(2)}% in broad-based ${nifty.changePercent > 0 ? 'rally' : 'selloff'}`);
    }
    
    // Top movers
    if (topGainers.length > 0) {
      events.push(`${topGainers[0].name} led gains with ${topGainers[0].changePercent.toFixed(2)}% surge`);
    }
    if (topLosers.length > 0) {
      events.push(`${topLosers[0].name} declined ${Math.abs(topLosers[0].changePercent).toFixed(2)}% amid selling pressure`);
    }
    
    return events;
  }
  
  // Generate Global Key Events
  static generateGlobalKeyEvents(indices: any[]): string[] {
    const events: string[] = [];
    
    const spx = indices.find(idx => idx.symbol === 'SPX');
    const nasdaq = indices.find(idx => idx.symbol === 'IXIC');
    
    if (spx && Math.abs(spx.changePercent) > 1) {
      events.push(`S&P 500 ${spx.changePercent > 0 ? 'rallied' : 'declined'} ${Math.abs(spx.changePercent).toFixed(2)}%`);
    }
    
    if (nasdaq && Math.abs(nasdaq.changePercent) > 1) {
      events.push(`Nasdaq ${nasdaq.changePercent > 0 ? 'surged' : 'fell'} ${Math.abs(nasdaq.changePercent).toFixed(2)}%`);
    }
    
    return events;
  }
  
  // Generate Crypto Key Events
  static generateCryptoKeyEvents(indices: any[]): string[] {
    const events: string[] = [];
    
    const btc = indices.find(idx => idx.symbol === 'BTC');
    const eth = indices.find(idx => idx.symbol === 'ETH');
    
    if (btc && Math.abs(btc.changePercent) > 3) {
      events.push(`Bitcoin ${btc.changePercent > 0 ? 'jumped' : 'plunged'} ${Math.abs(btc.changePercent).toFixed(2)}% to $${btc.value.toFixed(0)}`);
    }
    
    if (eth && Math.abs(eth.changePercent) > 3) {
      events.push(`Ethereum ${eth.changePercent > 0 ? 'soared' : 'crashed'} ${Math.abs(eth.changePercent).toFixed(2)}% to $${eth.value.toFixed(0)}`);
    }
    
    return events;
  }
  
  // Get Sector Color
  static getSectorColor(performance: number): string {
    if (performance > 2) return '#22c55e';
    if (performance > 0) return '#84cc16';
    if (performance > -2) return '#f97316';
    return '#ef4444';
  }
  
  // Convert Index to MarketData
  static convertToMarketData(index: any): MarketData {
    return {
      symbol: index.symbol,
      name: index.name,
      price: index.value,
      change: index.change,
      changePercent: index.changePercent,
      volume: 0,
      volumeChange: 0
    };
  }
}
