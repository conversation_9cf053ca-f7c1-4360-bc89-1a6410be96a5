// Main Market Intelligence Service

export * from './types';
export * from './sentiment-analyzer';
export * from './intelligence-engine';

import { NewsArticle } from '../news/types';
import { marketIntelligenceEngine } from './intelligence-engine';
import { sentimentAnalyzer } from './sentiment-analyzer';
import { MarketIntelligence, MarketSentiment, AdvancedInsight } from './types';

class MarketIntelligenceService {
  /**
   * Get comprehensive market intelligence with advanced insights
   */
  async getMarketIntelligence(articles: NewsArticle[]): Promise<{
    intelligence: MarketIntelligence;
    insights: string;
    sentimentAnalysis: MarketSentiment[];
  }> {
    try {
      // Generate comprehensive intelligence
      const [intelligence, insights, sentimentAnalysis] = await Promise.all([
        marketIntelligenceEngine.generateMarketIntelligence(articles),
        marketIntelligenceEngine.generateAdvancedInsights(articles),
        sentimentAnalyzer.analyzeMultiMarketSentiment(articles)
      ]);

      return {
        intelligence,
        insights,
        sentimentAnalysis
      };
    } catch (error) {
      console.error('Error generating market intelligence:', error);
      return this.getFallbackIntelligence(articles);
    }
  }

  /**
   * Get real-time sentiment analysis for all markets
   */
  async getMultiMarketSentiment(articles: NewsArticle[]): Promise<MarketSentiment[]> {
    try {
      return await sentimentAnalyzer.analyzeMultiMarketSentiment(articles);
    } catch (error) {
      console.error('Error analyzing sentiment:', error);
      return this.getDefaultSentiments();
    }
  }

  /**
   * Get advanced AI-powered market insights
   */
  async getAdvancedInsights(articles: NewsArticle[]): Promise<string> {
    try {
      return await marketIntelligenceEngine.generateAdvancedInsights(articles);
    } catch (error) {
      console.error('Error generating insights:', error);
      return this.getFallbackInsights(articles);
    }
  }

  /**
   * Get market-specific sentiment analysis
   */
  async getMarketSpecificSentiment(
    articles: NewsArticle[], 
    market: 'indian' | 'foreign' | 'crypto'
  ): Promise<MarketSentiment> {
    try {
      const allSentiments = await sentimentAnalyzer.analyzeMultiMarketSentiment(articles);
      return allSentiments.find(s => s.market === market) || this.getDefaultSentiment(market);
    } catch (error) {
      console.error(`Error analyzing ${market} sentiment:`, error);
      return this.getDefaultSentiment(market);
    }
  }

  /**
   * Get top impact news with detailed scoring
   */
  async getTopImpactNews(articles: NewsArticle[], limit: number = 10) {
    try {
      const impactScores = await sentimentAnalyzer.analyzeNewsImpact(articles);
      return impactScores.slice(0, limit);
    } catch (error) {
      console.error('Error analyzing news impact:', error);
      return [];
    }
  }

  /**
   * Generate market summary for quick overview
   */
  async getMarketSummary(articles: NewsArticle[]): Promise<{
    totalArticles: number;
    recentArticles: number;
    overallSentiment: string;
    topMarket: string;
    riskLevel: string;
    lastUpdated: Date;
  }> {
    try {
      const intelligence = await marketIntelligenceEngine.generateMarketIntelligence(articles);
      const sentiments = intelligence.overallSentiment;
      
      // Calculate overall sentiment
      const avgSentiment = sentiments.reduce((acc, s) => acc + s.score, 0) / sentiments.length;
      const overallSentiment = avgSentiment > 0.1 ? 'Bullish' : avgSentiment < -0.1 ? 'Bearish' : 'Neutral';
      
      // Find most active market
      const marketActivity = sentiments.map(s => ({
        market: s.market,
        activity: Math.abs(s.score) * s.confidence
      })).sort((a, b) => b.activity - a.activity);
      
      const topMarket = marketActivity[0]?.market || 'indian';
      
      // Count recent articles
      const recentArticles = articles.filter(a => {
        const hoursAgo = (Date.now() - new Date(a.publishedAt).getTime()) / (1000 * 60 * 60);
        return hoursAgo < 6;
      }).length;

      return {
        totalArticles: articles.length,
        recentArticles,
        overallSentiment,
        topMarket: topMarket.charAt(0).toUpperCase() + topMarket.slice(1),
        riskLevel: intelligence.riskAssessment.level.charAt(0).toUpperCase() + intelligence.riskAssessment.level.slice(1),
        lastUpdated: new Date()
      };
    } catch (error) {
      console.error('Error generating market summary:', error);
      return {
        totalArticles: articles.length,
        recentArticles: 0,
        overallSentiment: 'Neutral',
        topMarket: 'Mixed',
        riskLevel: 'Medium',
        lastUpdated: new Date()
      };
    }
  }

  /**
   * Get market correlations and cross-market analysis
   */
  async getMarketCorrelations(articles: NewsArticle[]) {
    try {
      const intelligence = await marketIntelligenceEngine.generateMarketIntelligence(articles);
      return {
        correlations: intelligence.marketCorrelations,
        volatilityIndex: intelligence.volatilityIndex,
        trends: intelligence.trends
      };
    } catch (error) {
      console.error('Error calculating correlations:', error);
      return {
        correlations: { indianForeign: 0, indianCrypto: 0, foreignCrypto: 0 },
        volatilityIndex: { indian: 0, foreign: 0, crypto: 0 },
        trends: []
      };
    }
  }

  /**
   * Fallback methods
   */
  private getFallbackIntelligence(articles: NewsArticle[]): {
    intelligence: MarketIntelligence;
    insights: string;
    sentimentAnalysis: MarketSentiment[];
  } {
    const defaultSentiments = this.getDefaultSentiments();
    
    return {
      intelligence: {
        timestamp: new Date(),
        overallSentiment: defaultSentiments,
        trends: [],
        topImpactNews: [],
        marketCorrelations: { indianForeign: 0, indianCrypto: 0, foreignCrypto: 0 },
        volatilityIndex: { indian: 0, foreign: 0, crypto: 0 },
        riskAssessment: { level: 'medium', factors: [], recommendation: 'Monitor market conditions' }
      },
      insights: this.getFallbackInsights(articles),
      sentimentAnalysis: defaultSentiments
    };
  }

  private getFallbackInsights(articles: NewsArticle[]): string {
    return `## 📊 **Market Intelligence - Limited Mode**

**System Status:** Advanced AI analysis temporarily unavailable

### 📈 **Current Market Activity**
- **Total News Articles:** ${articles.length}
- **Coverage:** Multi-market financial news analysis
- **Time Range:** Last 24 hours

### 🎯 **Key Headlines**
${articles.slice(0, 3).map((article, index) => 
  `${index + 1}. **${article.title}** - *${article.source.name}*`
).join('\n')}

### ⚡ **Basic Analysis**
Market activity is ${articles.length > 20 ? 'high' : articles.length > 10 ? 'moderate' : 'low'} based on news volume. 
Key themes include corporate developments, policy changes, and market movements.

**Note:** *Full AI-powered analysis will resume shortly with detailed sentiment scores, trend analysis, and predictive insights.*

---
*Basic analysis mode | ${articles.length} articles processed | ${new Date().toLocaleTimeString()}*`;
  }

  private getDefaultSentiments(): MarketSentiment[] {
    return [
      {
        market: 'indian',
        sentiment: 'neutral',
        score: 0,
        confidence: 0.5,
        trend: 'stable',
        volatility: 'medium',
        lastUpdated: new Date()
      },
      {
        market: 'foreign',
        sentiment: 'neutral',
        score: 0,
        confidence: 0.5,
        trend: 'stable',
        volatility: 'medium',
        lastUpdated: new Date()
      },
      {
        market: 'crypto',
        sentiment: 'neutral',
        score: 0,
        confidence: 0.5,
        trend: 'stable',
        volatility: 'high',
        lastUpdated: new Date()
      }
    ];
  }

  private getDefaultSentiment(market: 'indian' | 'foreign' | 'crypto'): MarketSentiment {
    return {
      market,
      sentiment: 'neutral',
      score: 0,
      confidence: 0.5,
      trend: 'stable',
      volatility: market === 'crypto' ? 'high' : 'medium',
      lastUpdated: new Date()
    };
  }
}

export const marketIntelligenceService = new MarketIntelligenceService();
