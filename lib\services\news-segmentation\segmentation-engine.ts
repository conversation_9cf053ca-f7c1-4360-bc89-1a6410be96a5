// Advanced News Segmentation Engine

import { NewsArticle } from '../news/types';
import { 
  NewsSegment, 
  SegmentationResult, 
  SegmentMatch, 
  ContentAnalysis,
  ADVANCED_NEWS_SEGMENTS 
} from './types';

export class AdvancedSegmentationEngine {
  private segmentationCache = new Map<string, SegmentationResult>();
  private readonly cacheTimeout = 60 * 60 * 1000; // 1 hour

  /**
   * Segment news article using advanced multi-layer analysis
   */
  async segmentArticle(article: NewsArticle): Promise<SegmentationResult> {
    const cacheKey = `${article.id}_${article.publishedAt}`;
    const cached = this.segmentationCache.get(cacheKey);
    
    if (cached && this.isCacheValid(cached)) {
      return cached;
    }

    try {
      // Perform comprehensive content analysis
      const contentAnalysis = await this.analyzeContent(article);
      
      // Test article against all segments
      const segmentMatches: SegmentMatch[] = [];

      for (const [segmentId, segment] of Object.entries(ADVANCED_NEWS_SEGMENTS)) {
        try {
          const match = await this.testSegmentMatch(article, segment, contentAnalysis);
          if (match.relevanceScore >= segment.minimumRelevanceScore) {
            segmentMatches.push(match);
          }
        } catch (error) {
          console.warn(`Error testing segment ${segmentId}:`, error);
          // Continue with other segments
        }
      }

      // Sort by relevance score and determine primary segment
      segmentMatches.sort((a, b) => b.relevanceScore - a.relevanceScore);

      // If no matches found, try with lower threshold
      let primarySegment = segmentMatches[0]?.segmentId;
      if (!primarySegment) {
        // Try to find the best match even if below threshold
        const allMatches: SegmentMatch[] = [];
        for (const [segmentId, segment] of Object.entries(ADVANCED_NEWS_SEGMENTS)) {
          try {
            const match = await this.testSegmentMatch(article, segment, contentAnalysis);
            allMatches.push(match);
          } catch (error) {
            console.warn(`Error in fallback segment test ${segmentId}:`, error);
          }
        }
        allMatches.sort((a, b) => b.relevanceScore - a.relevanceScore);
        primarySegment = allMatches[0]?.segmentId || 'indian-markets'; // Default to Indian markets

        // Add the best match to segmentMatches if it has some relevance
        if (allMatches[0] && allMatches[0].relevanceScore > 0.1) {
          segmentMatches.push(allMatches[0]);
        }
      }
      
      // Calculate overall confidence
      const confidence = this.calculateOverallConfidence(segmentMatches, contentAnalysis);
      
      // Generate explanation reasons
      const reasons = this.generateSegmentationReasons(segmentMatches, contentAnalysis);

      const result: SegmentationResult = {
        articleId: article.id,
        segments: segmentMatches,
        primarySegment,
        confidence,
        relevanceScore: segmentMatches[0]?.relevanceScore || 0,
        reasons
      };

      this.segmentationCache.set(cacheKey, result);
      return result;
    } catch (error) {
      console.error('Error in article segmentation:', error);
      return this.getDefaultSegmentation(article);
    }
  }

  /**
   * Batch segment multiple articles with optimization
   */
  async segmentArticles(articles: NewsArticle[]): Promise<SegmentationResult[]> {
    const results: SegmentationResult[] = [];
    
    // Process in batches for performance
    const batchSize = 10;
    for (let i = 0; i < articles.length; i += batchSize) {
      const batch = articles.slice(i, i + batchSize);
      const batchPromises = batch.map(article => this.segmentArticle(article));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * Get articles for specific segment with enhanced filtering
   */
  async getSegmentArticles(
    articles: NewsArticle[], 
    segmentId: string,
    minRelevanceScore: number = 0.5
  ): Promise<NewsArticle[]> {
    const segmentations = await this.segmentArticles(articles);
    
    return articles.filter((article, index) => {
      const segmentation = segmentations[index];
      return segmentation.primarySegment === segmentId && 
             segmentation.relevanceScore >= minRelevanceScore;
    }).sort((a, b) => {
      // Sort by relevance score (highest first)
      const aSegmentation = segmentations.find(s => s.articleId === a.id);
      const bSegmentation = segmentations.find(s => s.articleId === b.id);
      return (bSegmentation?.relevanceScore || 0) - (aSegmentation?.relevanceScore || 0);
    });
  }

  /**
   * Analyze content using multiple NLP techniques
   */
  private async analyzeContent(article: NewsArticle): Promise<ContentAnalysis> {
    try {
      const fullText = `${article.title} ${article.description} ${article.content || ''}`;
      const words = fullText.toLowerCase().split(/\s+/).filter(word => word.length > 0);

      return {
        topics: this.extractTopics(fullText),
        entities: this.extractEntities(fullText),
        sentiment: this.calculateSentiment(fullText),
        complexity: this.calculateComplexity(words),
        marketRelevance: this.calculateMarketRelevance(fullText),
        geographicFocus: this.extractGeographicFocus(fullText),
        timeRelevance: this.calculateTimeRelevance(article.publishedAt)
      };
    } catch (error) {
      console.warn('Error in content analysis, using defaults:', error);
      return {
        topics: [],
        entities: [],
        sentiment: 0,
        complexity: 0.5,
        marketRelevance: 0.5,
        geographicFocus: [],
        timeRelevance: 0.5
      };
    }
  }

  /**
   * Test if article matches a specific segment
   */
  private async testSegmentMatch(
    article: NewsArticle, 
    segment: NewsSegment, 
    contentAnalysis: ContentAnalysis
  ): Promise<SegmentMatch> {
    const fullText = `${article.title} ${article.description} ${article.content || ''}`.toLowerCase();
    
    // Test keyword matches
    const keywordMatches = this.testKeywordMatches(fullText, segment);
    
    // Test entity matches
    const entityMatches = this.testEntityMatches(fullText, segment);
    
    // Test geographic matches
    const geographicMatches = this.testGeographicMatches(fullText, segment);
    
    // Test source credibility
    const sourceScore = this.calculateSourceScore(article.source.name, segment);
    
    // Apply exclusion rules
    const isExcluded = this.testExclusionRules(fullText, segment);
    
    if (isExcluded) {
      return {
        segmentId: segment.id,
        confidence: 0,
        relevanceScore: 0,
        matchedKeywords: [],
        matchedEntities: [],
        matchedGeographic: [],
        reasons: ['Excluded by segment rules']
      };
    }

    // Calculate weighted relevance score
    const relevanceScore = this.calculateWeightedRelevanceScore(
      article, segment, keywordMatches, entityMatches, geographicMatches, sourceScore, contentAnalysis
    );
    
    // Calculate confidence based on multiple factors
    const confidence = this.calculateMatchConfidence(
      keywordMatches, entityMatches, geographicMatches, sourceScore, contentAnalysis
    );

    // Generate match reasons
    const reasons = this.generateMatchReasons(
      keywordMatches, entityMatches, geographicMatches, sourceScore, segment
    );

    return {
      segmentId: segment.id,
      confidence,
      relevanceScore,
      matchedKeywords: keywordMatches.matches,
      matchedEntities: entityMatches.matches,
      matchedGeographic: geographicMatches.matches,
      reasons
    };
  }

  /**
   * Test keyword matches with context awareness
   */
  private testKeywordMatches(text: string, segment: NewsSegment): {
    score: number;
    matches: string[];
    contextMatches: number;
  } {
    let totalScore = 0;
    const matches: string[] = [];
    let contextMatches = 0;
    let totalPossibleScore = 0;

    for (const keyword of segment.keywords) {
      totalPossibleScore += keyword.weight;
      const mainTerm = keyword.term.toLowerCase();
      let termFound = false;

      // Check main term with word boundaries for better matching
      const wordBoundaryRegex = new RegExp(`\\b${mainTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
      if (wordBoundaryRegex.test(text) || text.includes(mainTerm)) {
        totalScore += keyword.weight;
        matches.push(keyword.term);
        termFound = true;
      }

      // Check synonyms
      if (!termFound && keyword.synonyms) {
        for (const synonym of keyword.synonyms) {
          const synonymRegex = new RegExp(`\\b${synonym.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'i');
          if (synonymRegex.test(text) || text.includes(synonym.toLowerCase())) {
            totalScore += keyword.weight * 0.8; // Slightly lower weight for synonyms
            matches.push(synonym);
            termFound = true;
            break;
          }
        }
      }

      // Partial matching for compound terms
      if (!termFound && mainTerm.includes(' ')) {
        const termParts = mainTerm.split(' ');
        const partMatches = termParts.filter(part => text.includes(part)).length;
        if (partMatches >= Math.ceil(termParts.length / 2)) {
          totalScore += keyword.weight * 0.6; // Partial match bonus
          matches.push(`${keyword.term} (partial)`);
          termFound = true;
        }
      }

      // Check context if term found
      if (termFound && keyword.context) {
        const contextFound = keyword.context.some(ctx => text.includes(ctx.toLowerCase()));
        if (contextFound) {
          totalScore += keyword.weight * 0.2; // Bonus for context match
          contextMatches++;
        }
      }
    }

    // Use total possible score for normalization to be more lenient
    const normalizedScore = totalPossibleScore > 0 ? totalScore / totalPossibleScore : 0;

    return {
      score: Math.min(normalizedScore, 1.0),
      matches,
      contextMatches
    };
  }

  /**
   * Test entity matches with fuzzy matching
   */
  private testEntityMatches(text: string, segment: NewsSegment): {
    score: number;
    matches: string[];
  } {
    let totalScore = 0;
    const matches: string[] = [];

    for (const entity of segment.entities) {
      let entityFound = false;

      // Check main entity name
      if (text.includes(entity.name.toLowerCase())) {
        totalScore += entity.weight;
        matches.push(entity.name);
        entityFound = true;
      }

      // Check aliases
      if (!entityFound) {
        for (const alias of entity.aliases) {
          if (text.includes(alias.toLowerCase())) {
            totalScore += entity.weight * 0.9;
            matches.push(alias);
            entityFound = true;
            break;
          }
        }
      }
    }

    return {
      score: segment.entities.length > 0 ? Math.min(totalScore / segment.entities.length, 1.0) : 0,
      matches
    };
  }

  /**
   * Test geographic matches
   */
  private testGeographicMatches(text: string, segment: NewsSegment): {
    score: number;
    matches: string[];
  } {
    let totalScore = 0;
    const matches: string[] = [];

    for (const geo of segment.geographicMarkers) {
      let geoFound = false;

      // Check main location
      if (text.includes(geo.location.toLowerCase())) {
        totalScore += geo.weight;
        matches.push(geo.location);
        geoFound = true;
      }

      // Check aliases
      if (!geoFound) {
        for (const alias of geo.aliases) {
          if (text.includes(alias.toLowerCase())) {
            totalScore += geo.weight * 0.9;
            matches.push(alias);
            geoFound = true;
            break;
          }
        }
      }
    }

    return {
      score: segment.geographicMarkers.length > 0 ? Math.min(totalScore / segment.geographicMarkers.length, 1.0) : 0,
      matches
    };
  }

  /**
   * Calculate source credibility score
   */
  private calculateSourceScore(sourceName: string, segment: NewsSegment): number {
    const preferredSource = segment.sources.find(source => 
      sourceName.toLowerCase().includes(source.name.toLowerCase())
    );
    
    return preferredSource ? preferredSource.credibilityScore : 0.5; // Default neutral score
  }

  /**
   * Test exclusion rules
   */
  private testExclusionRules(text: string, segment: NewsSegment): boolean {
    for (const rule of segment.exclusionRules) {
      if (rule.type === 'keyword' && text.includes(rule.pattern.toLowerCase())) {
        return true;
      }
      if (rule.type === 'content_pattern') {
        const regex = new RegExp(rule.pattern, 'i');
        if (regex.test(text)) {
          return true;
        }
      }
    }
    return false;
  }

  /**
   * Calculate weighted relevance score using segment weights
   */
  private calculateWeightedRelevanceScore(
    article: NewsArticle,
    segment: NewsSegment,
    keywordMatches: any,
    entityMatches: any,
    geographicMatches: any,
    sourceScore: number,
    contentAnalysis: ContentAnalysis
  ): number {
    const weights = segment.relevanceWeights;
    
    // Title analysis
    const titleScore = this.analyzeTextRelevance(article.title, segment) * weights.titleWeight;
    
    // Description analysis
    const descriptionScore = this.analyzeTextRelevance(article.description, segment) * weights.descriptionWeight;
    
    // Content analysis (if available)
    const contentScore = article.content ? 
      this.analyzeTextRelevance(article.content, segment) * weights.contentWeight : 0;
    
    // Source score
    const weightedSourceScore = sourceScore * weights.sourceWeight;
    
    // Time relevance
    const timeScore = contentAnalysis.timeRelevance * weights.timeWeight;
    
    // Entity score
    const entityScore = entityMatches.score * weights.entityWeight;
    
    // Geographic score
    const geoScore = geographicMatches.score * weights.geographicWeight;

    return Math.min(
      titleScore + descriptionScore + contentScore + weightedSourceScore + 
      timeScore + entityScore + geoScore, 1.0
    );
  }

  /**
   * Analyze text relevance for a specific segment
   */
  private analyzeTextRelevance(text: string, segment: NewsSegment): number {
    const lowerText = text.toLowerCase();
    let relevanceScore = 0;
    let totalWeight = 0;

    // Check keywords
    for (const keyword of segment.keywords) {
      totalWeight += keyword.weight;
      if (lowerText.includes(keyword.term.toLowerCase())) {
        relevanceScore += keyword.weight;
      } else if (keyword.synonyms) {
        for (const synonym of keyword.synonyms) {
          if (lowerText.includes(synonym.toLowerCase())) {
            relevanceScore += keyword.weight * 0.8;
            break;
          }
        }
      }
    }

    return totalWeight > 0 ? relevanceScore / totalWeight : 0;
  }

  /**
   * Helper methods for content analysis
   */
  private extractTopics(text: string): any[] {
    // Simplified topic extraction - in production, use proper NLP
    const topics = ['finance', 'technology', 'policy', 'markets'];
    return topics.filter(topic => text.toLowerCase().includes(topic))
      .map(topic => ({ name: topic, confidence: 0.7, keywords: [topic] }));
  }

  private extractEntities(text: string): any[] {
    // Simplified entity extraction
    return [];
  }

  private calculateSentiment(text: string): number {
    // Simplified sentiment calculation
    const positiveWords = ['growth', 'profit', 'gain', 'rise', 'bullish'];
    const negativeWords = ['loss', 'decline', 'fall', 'bearish', 'crash'];
    
    let score = 0;
    const lowerText = text.toLowerCase();
    
    positiveWords.forEach(word => {
      if (lowerText.includes(word)) score += 0.1;
    });
    
    negativeWords.forEach(word => {
      if (lowerText.includes(word)) score -= 0.1;
    });
    
    return Math.max(-1, Math.min(1, score));
  }

  private calculateComplexity(words: string[]): number {
    const avgWordLength = words.reduce((acc, word) => acc + word.length, 0) / words.length;
    return Math.min(avgWordLength / 10, 1.0);
  }

  private calculateMarketRelevance(text: string): number {
    const marketTerms = ['market', 'stock', 'trading', 'investment', 'finance', 'economy'];
    const lowerText = text.toLowerCase();
    const matches = marketTerms.filter(term => lowerText.includes(term)).length;
    return Math.min(matches / marketTerms.length, 1.0);
  }

  private extractGeographicFocus(text: string): string[] {
    const locations = ['india', 'usa', 'china', 'europe', 'japan'];
    const lowerText = text.toLowerCase();
    return locations.filter(location => lowerText.includes(location));
  }

  private calculateTimeRelevance(publishedAt: string): number {
    const hoursAgo = (Date.now() - new Date(publishedAt).getTime()) / (1000 * 60 * 60);
    return Math.max(0, 1 - hoursAgo / 48); // Decay over 48 hours
  }

  private calculateMatchConfidence(
    keywordMatches: any,
    entityMatches: any,
    geographicMatches: any,
    sourceScore: number,
    contentAnalysis: ContentAnalysis
  ): number {
    const factors = [
      keywordMatches.score,
      entityMatches.score,
      geographicMatches.score,
      sourceScore,
      contentAnalysis.marketRelevance
    ];
    
    const avgScore = factors.reduce((acc, score) => acc + score, 0) / factors.length;
    return Math.min(avgScore, 1.0);
  }

  private calculateOverallConfidence(matches: SegmentMatch[], contentAnalysis: ContentAnalysis): number {
    if (matches.length === 0) return 0;
    
    const topMatch = matches[0];
    const confidenceFactors = [
      topMatch.confidence,
      contentAnalysis.marketRelevance,
      matches.length > 1 ? 0.8 : 1.0 // Slight penalty for ambiguous categorization
    ];
    
    return confidenceFactors.reduce((acc, factor) => acc + factor, 0) / confidenceFactors.length;
  }

  private generateMatchReasons(
    keywordMatches: any,
    entityMatches: any,
    geographicMatches: any,
    sourceScore: number,
    segment: NewsSegment
  ): string[] {
    const reasons: string[] = [];
    
    if (keywordMatches.matches.length > 0) {
      reasons.push(`Matched keywords: ${keywordMatches.matches.slice(0, 3).join(', ')}`);
    }
    
    if (entityMatches.matches.length > 0) {
      reasons.push(`Matched entities: ${entityMatches.matches.slice(0, 3).join(', ')}`);
    }
    
    if (geographicMatches.matches.length > 0) {
      reasons.push(`Geographic relevance: ${geographicMatches.matches.slice(0, 2).join(', ')}`);
    }
    
    if (sourceScore > 0.7) {
      reasons.push('High source credibility for this segment');
    }
    
    return reasons;
  }

  private generateSegmentationReasons(matches: SegmentMatch[], contentAnalysis: ContentAnalysis): string[] {
    const reasons: string[] = [];
    
    if (matches.length > 0) {
      const topMatch = matches[0];
      reasons.push(`Primary segment: ${ADVANCED_NEWS_SEGMENTS[topMatch.segmentId]?.name}`);
      reasons.push(`Relevance score: ${(topMatch.relevanceScore * 100).toFixed(1)}%`);
      reasons.push(...topMatch.reasons.slice(0, 2));
    }
    
    if (contentAnalysis.marketRelevance > 0.7) {
      reasons.push('High market relevance detected');
    }
    
    return reasons;
  }

  private getDefaultSegmentation(article: NewsArticle): SegmentationResult {
    return {
      articleId: article.id,
      segments: [],
      primarySegment: 'general',
      confidence: 0.3,
      relevanceScore: 0.3,
      reasons: ['Default categorization - insufficient matching criteria']
    };
  }

  private isCacheValid(result: SegmentationResult): boolean {
    // Simple cache validation - in production, implement more sophisticated logic
    return true;
  }
}

export const segmentationEngine = new AdvancedSegmentationEngine();
