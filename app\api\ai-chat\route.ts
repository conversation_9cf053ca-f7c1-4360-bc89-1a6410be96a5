import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { message, category, complexity, region, context } = await request.json();

    // Generate AI response based on the question
    const aiResponse = await generateAIResponse(message, category, complexity, region, context);

    return NextResponse.json({
      success: true,
      response: aiResponse,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in AI chat:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to generate AI response',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

async function generateAIResponse(
  message: string, 
  category?: string, 
  complexity?: string, 
  region?: string, 
  context?: string
): Promise<string> {
  
  // If we have Gemini API key, use it
  const geminiApiKey = process.env.GEMINI_API_KEY;
  
  if (geminiApiKey) {
    try {
      const geminiResponse = await callGeminiAPI(message, category, complexity, region, geminiApiKey);
      if (geminiResponse) return geminiResponse;
    } catch (error) {
      console.warn('Gemini API failed, falling back to structured response');
    }
  }

  // Fallback to structured AI-like responses
  return generateStructuredResponse(message, category, complexity, region);
}

async function callGeminiAPI(
  message: string,
  category?: string,
  complexity?: string,
  region?: string,
  apiKey?: string
): Promise<string | null> {
  try {
    const prompt = buildGeminiPrompt(message, category, complexity, region);

    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.8,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 2048,
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_MEDIUM_AND_ABOVE"
          }
        ]
      })
    });

    const data = await response.json();

    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      return data.candidates[0].content.parts[0].text;
    }

    if (data.error) {
      console.error('Gemini API error response:', data.error);
      return null;
    }

    return null;
  } catch (error) {
    console.error('Gemini API error:', error);
    return null;
  }
}

function buildGeminiPrompt(message: string, category?: string, complexity?: string, region?: string): string {
  const regionContext = getRegionContext(region);
  const complexityGuidance = getComplexityGuidance(complexity);

  let prompt = `You are a world-class financial analyst and market educator with deep expertise in ${region || 'global'} markets. Provide a comprehensive, professional analysis for the following question.

**Question:** ${message}

**Context:**
- **Market Focus:** ${regionContext}
- **Category:** ${category || 'Market Analysis'}
- **Complexity Level:** ${complexityGuidance}

**Please provide a detailed response covering:**

1. **Direct Answer:** Clear, concise response to the specific question
2. **Market Context:** Current market conditions and relevant background
3. **Key Factors:** 3-5 most important factors influencing this topic
4. **Real Examples:** Specific, recent examples or case studies
5. **Investment Implications:** Practical insights for different investor types
6. **Risk Assessment:** Important risks and considerations
7. **Actionable Insights:** Specific steps or strategies investors can consider

**Response Guidelines:**
- Use professional financial terminology appropriate for ${complexity || 'intermediate'} level
- Include specific data points, percentages, or metrics where relevant
- Reference recent market events or trends
- Provide balanced perspective covering both opportunities and risks
- Structure response with clear headings and bullet points for readability
- Keep response comprehensive but focused (aim for 400-600 words)

**Market-Specific Considerations:**
${getMarketSpecificGuidance(region, category)}

Please ensure your response is educational, actionable, and reflects current market realities.`;

  return prompt;
}

function getRegionContext(region?: string): string {
  switch (region) {
    case 'india':
      return 'Indian equity markets (NSE/BSE), focusing on domestic factors like RBI policy, FII/DII flows, monsoon impact, and sector-specific dynamics';
    case 'global':
      return 'Global equity markets with emphasis on US markets (NYSE/NASDAQ), Federal Reserve policy, international trade, and cross-border capital flows';
    case 'crypto':
      return 'Cryptocurrency markets including Bitcoin, Ethereum, DeFi protocols, and blockchain technology developments';
    default:
      return 'Global financial markets with cross-regional analysis';
  }
}

function getComplexityGuidance(complexity?: string): string {
  switch (complexity) {
    case 'beginner':
      return 'Beginner level - explain fundamental concepts, avoid heavy jargon, provide basic definitions';
    case 'intermediate':
      return 'Intermediate level - assume basic market knowledge, use standard financial terms, provide moderate depth';
    case 'advanced':
      return 'Advanced level - use sophisticated analysis, complex strategies, detailed technical insights';
    default:
      return 'Intermediate level - balanced approach with clear explanations and practical insights';
  }
}

function getMarketSpecificGuidance(region?: string, category?: string): string {
  if (region === 'india') {
    return `- Consider RBI monetary policy impact
- Factor in FII/DII investment flows
- Include monsoon/seasonal effects where relevant
- Reference major Indian indices (Nifty 50, Sensex)
- Consider rupee movement implications
- Include sector-specific Indian market dynamics`;
  } else if (region === 'global') {
    return `- Consider Federal Reserve policy implications
- Factor in global economic indicators (GDP, inflation, employment)
- Include geopolitical risk assessment
- Reference major global indices (S&P 500, NASDAQ, FTSE)
- Consider USD strength/weakness impact
- Include international trade and supply chain factors`;
  } else if (region === 'crypto') {
    return `- Consider regulatory developments globally
- Factor in institutional adoption trends
- Include on-chain metrics and network fundamentals
- Reference major cryptocurrencies and their correlations
- Consider market sentiment and fear/greed indicators
- Include DeFi and Web3 ecosystem developments`;
  }
  return '- Provide balanced global perspective with regional considerations';
}

function generateStructuredResponse(message: string, category?: string, complexity?: string, region?: string): string {
  const lowerMessage = message.toLowerCase();
  console.log('🔍 Analyzing message:', message);
  console.log('🔍 Lower case:', lowerMessage);

  // FII/Investment flow questions (check this FIRST before sector analysis)
  if (lowerMessage.includes('fii') || lowerMessage.includes('foreign institutional') || lowerMessage.includes('flow') || lowerMessage.includes('foreign investment')) {
    console.log('✅ Matched FII/Flow category');
    return generateFlowResponse(message, region);
  }

  // RBI/Fed policy questions
  if (lowerMessage.includes('rbi') || lowerMessage.includes('fed') || lowerMessage.includes('rate') || lowerMessage.includes('policy')) {
    return generatePolicyResponse(message, region);
  }

  // Technical analysis questions
  if (lowerMessage.includes('technical') || lowerMessage.includes('rsi') || lowerMessage.includes('oversold') || lowerMessage.includes('chart')) {
    return generateTechnicalResponse(message, region);
  }

  // Market sentiment questions
  if (lowerMessage.includes('sentiment') || lowerMessage.includes('bullish') || lowerMessage.includes('bearish')) {
    return generateSentimentResponse(message, region);
  }

  // Sector analysis questions (check this AFTER more specific questions)
  if (lowerMessage.includes('sector') || (lowerMessage.includes('it') && !lowerMessage.includes('fii')) || (lowerMessage.includes('banking') && !lowerMessage.includes('flow'))) {
    console.log('✅ Matched Sector category');
    return generateSectorResponse(message, region);
  }

  // Crypto questions
  if (lowerMessage.includes('crypto') || lowerMessage.includes('bitcoin') || lowerMessage.includes('ethereum') || lowerMessage.includes('defi')) {
    console.log('✅ Matched Crypto category');
    return generateCryptoResponse(message);
  }

  // General market questions
  console.log('✅ Using General response');
  return generateGeneralResponse(message, region);
}

function generateSentimentResponse(message: string, region?: string): string {
  const regionText = region === 'india' ? 'Indian' : region === 'crypto' ? 'cryptocurrency' : 'global';

  return `# Market Sentiment Analysis: ${regionText.charAt(0).toUpperCase() + regionText.slice(1)} Markets

## Current Sentiment Drivers

**Institutional Flows & Liquidity:**
• ${region === 'india' ? 'FII/DII flows are crucial - recent FII selling has created volatility while DII buying provides support' : region === 'crypto' ? 'Institutional adoption continues with ETF inflows and corporate treasury allocations' : 'Central bank liquidity policies and institutional rebalancing drive sentiment'}
• Options flow and derivatives positioning indicate smart money sentiment
• ${region === 'india' ? 'Mutual fund SIP flows provide steady support during corrections' : 'Pension fund and sovereign wealth fund allocations'}

**Economic & Policy Factors:**
• ${region === 'india' ? 'RBI policy stance, inflation targeting, and rupee stability concerns' : region === 'crypto' ? 'Regulatory clarity from major economies (US, EU, Asia)' : 'Federal Reserve policy, inflation data, and employment trends'}
• ${region === 'india' ? 'Monsoon patterns affecting rural demand and FMCG sector sentiment' : region === 'crypto' ? 'Central bank digital currency (CBDC) developments' : 'Geopolitical tensions and trade policy impacts'}

## Key Sentiment Indicators

**Technical Indicators:**
1. **Volatility Measures**: ${region === 'india' ? 'India VIX above 15 suggests caution, below 12 indicates complacency' : region === 'crypto' ? 'Crypto Fear & Greed Index and Bitcoin dominance ratios' : 'VIX levels and term structure'}
2. **Market Breadth**: Advance-decline ratios, new highs/lows, and sector participation
3. **Volume Patterns**: Institutional vs retail participation, block deal activity

**Fundamental Metrics:**
• Earnings revision trends and forward P/E ratios
• ${region === 'india' ? 'Book value multiples and ROE trends for quality assessment' : region === 'crypto' ? 'On-chain metrics like active addresses and network value' : 'Sector rotation patterns and style factor performance'}

## Investment Strategy Framework

**Bullish Sentiment Environment:**
• Focus on momentum strategies and growth stocks
• Increase allocation to cyclical sectors
• Consider leveraged products with proper risk management
• ${region === 'india' ? 'Target export-oriented IT and pharma stocks' : region === 'crypto' ? 'Focus on Layer 1 protocols and DeFi blue chips' : 'Overweight technology and consumer discretionary'}

**Bearish Sentiment Environment:**
• Emphasize defensive sectors (utilities, consumer staples)
• Increase cash allocation and consider hedging strategies
• Focus on quality stocks with strong balance sheets
• ${region === 'india' ? 'FMCG and pharma provide defensive characteristics' : region === 'crypto' ? 'Bitcoin and stablecoins offer relative safety' : 'Healthcare and utilities typically outperform'}

**Neutral/Mixed Sentiment:**
• Stock-specific analysis becomes more important
• Sector rotation strategies can be effective
• Focus on value opportunities and contrarian plays

## Risk Management Considerations

• Sentiment can shift rapidly - maintain position sizing discipline
• Use stop-losses and profit-taking levels
• Monitor correlation breakdowns during stress periods
• ${region === 'india' ? 'Currency hedging for international exposure' : region === 'crypto' ? 'Consider portfolio allocation limits (typically 5-10%)' : 'Geographic diversification remains important'}

**Current Assessment**: Monitor ${region === 'india' ? 'FII flow data, RBI communications, and global risk-off sentiment' : region === 'crypto' ? 'regulatory developments, institutional adoption news, and Bitcoin ETF flows' : 'Fed policy signals, earnings revisions, and geopolitical developments'} for sentiment shifts.`;
}

function generateSectorResponse(message: string, region?: string): string {
  const regionText = region === 'india' ? 'Indian' : region === 'crypto' ? 'crypto' : 'global';
  
  return `Sector analysis in ${regionText} markets requires understanding both cyclical and structural trends:

**Key Sector Dynamics:**
• **Cyclical Sectors**: Banking, auto, metals respond to economic cycles
• **Defensive Sectors**: FMCG, pharma, utilities provide stability during downturns
• **Growth Sectors**: Technology, healthcare offer long-term expansion potential

**Current Sector Trends:**
1. **Technology**: ${region === 'india' ? 'Indian IT benefits from digital transformation and rupee depreciation' : 'AI revolution driving valuations higher'}
2. **Banking**: ${region === 'india' ? 'Credit growth recovery but NIM pressure from rate cycles' : 'Interest rate sensitivity affecting margins'}
3. **Healthcare/Pharma**: Defensive characteristics with growth from aging demographics
4. **Energy**: Transition to renewables creating winners and losers

**Sector Rotation Strategy:**
• **Early Cycle**: Technology, consumer discretionary lead
• **Mid Cycle**: Industrials, materials gain momentum
• **Late Cycle**: Energy, financials outperform
• **Recession**: Utilities, consumer staples provide safety

**Analysis Framework:**
1. **Relative Performance**: Compare sector vs benchmark returns
2. **Earnings Growth**: Sector-wise earnings revision trends
3. **Valuation Metrics**: P/E, P/B ratios relative to historical averages
4. **Policy Impact**: Government policies affecting specific sectors

Focus on sectors with improving fundamentals, reasonable valuations, and positive policy tailwinds for better risk-adjusted returns.`;
}

function generateTechnicalResponse(message: string, region?: string): string {
  return `Technical analysis provides valuable insights for timing and risk management:

**Key Technical Indicators:**
• **RSI (Relative Strength Index)**: Values below 30 suggest oversold conditions, above 70 indicate overbought
• **Moving Averages**: 20, 50, 200-day MAs show trend direction and support/resistance
• **MACD**: Momentum indicator showing trend changes and divergences
• **Volume**: Confirms price movements and indicates conviction

**Oversold Opportunities:**
1. **RSI < 30**: Look for stocks with strong fundamentals trading at oversold levels
2. **Support Levels**: Identify key support zones where buying interest emerges
3. **Bullish Divergence**: Price makes lower lows while indicators make higher lows
4. **Volume Confirmation**: Increased volume on bounces confirms buying interest

**Risk Management:**
• **Stop Losses**: Place below key support levels to limit downside
• **Position Sizing**: Reduce size in volatile or uncertain conditions
• **Time Frames**: Align technical signals with your investment horizon
• **Confirmation**: Use multiple indicators to confirm signals

**Market-Specific Considerations:**
${region === 'india' ? '• India VIX above 20 suggests high volatility - be cautious\n• Watch for FII flow data to confirm technical signals' : 
  region === 'crypto' ? '• Crypto markets are highly volatile - use smaller position sizes\n• Watch Bitcoin dominance for altcoin opportunities' : 
  '• VIX above 25 indicates fear - often good for contrarian plays\n• Watch sector rotation for momentum opportunities'}

Remember: Technical analysis works best when combined with fundamental analysis and proper risk management. No indicator is perfect, so always use multiple confirmations.`;
}

function generatePolicyResponse(message: string, region?: string): string {
  const policyMaker = region === 'india' ? 'RBI' : region === 'crypto' ? 'Regulators' : 'Federal Reserve';
  
  return `${policyMaker} policy decisions have significant market implications:

**Policy Impact on Markets:**
• **Interest Rates**: ${region === 'india' ? 'RBI rate changes affect borrowing costs and sector performance' : region === 'crypto' ? 'Regulatory clarity affects adoption and institutional investment' : 'Fed rates influence global liquidity and risk appetite'}
• **Liquidity**: Policy changes affect money supply and market liquidity
• **Sector Rotation**: Different sectors benefit from different policy stances
• **Currency Impact**: Policy decisions affect exchange rates and international flows

**Sector-wise Impact:**
1. **Banking**: ${region === 'india' ? 'Rate hikes improve NIMs but may increase NPAs' : 'Interest rate changes directly affect net interest margins'}
2. **Real Estate**: Sensitive to interest rate changes and credit availability
3. **FMCG**: Rural demand affected by policy support and inflation
4. **Technology**: ${region === 'india' ? 'Export-oriented, benefits from supportive policies' : 'Growth stocks sensitive to rate changes'}

**Investment Strategy:**
• **Rate Hike Cycle**: Focus on banks, value stocks, and defensive sectors
• **Rate Cut Cycle**: Growth stocks, real estate, and cyclicals outperform
• **Policy Uncertainty**: Maintain diversification and reduce leverage

**Key Indicators to Watch:**
• Policy meeting minutes and forward guidance
• Inflation data influencing policy decisions
• Economic growth indicators
• Global policy coordination

${region === 'india' ? 'For Indian markets, also monitor fiscal policy, GST changes, and government spending patterns as they significantly impact sector performance.' : 
  region === 'crypto' ? 'For crypto markets, regulatory developments in major economies like US, EU, and Asia are crucial for long-term adoption trends.' : 
  'For global markets, coordinate policies between major central banks often drive cross-border capital flows.'}`;
}

function generateFlowResponse(message: string, region?: string): string {
  return `# FII (Foreign Institutional Investor) Flows: Critical Market Driver

## Why FII Flows Matter for Indian Markets

**Market Size & Impact:**
• FIIs manage over $600 billion in Indian equities (approximately 20% of market cap)
• Daily FII flows can range from ₹1,000-5,000 crores, significantly impacting market direction
• Single-day large outflows (>₹3,000 crores) often trigger 1-2% market corrections

**Direct Market Impact:**
1. **Price Discovery**: FII buying/selling directly affects stock prices and index levels
2. **Liquidity Provision**: FIIs are among the largest liquidity providers in Indian markets
3. **Volatility Driver**: Sudden FII moves can trigger circuit breakers and high volatility
4. **Sector Rotation**: FII preferences drive sector-wise performance differences

## Key Mechanisms of FII Impact

**Currency Effects:**
• **FII Inflows**: Strengthen rupee → benefit import-dependent sectors (oil, metals)
• **FII Outflows**: Weaken rupee → benefit export-oriented sectors (IT, pharma, textiles)
• Currency hedging costs affect FII investment decisions

**Market Sentiment Transmission:**
• FII flows often reflect global risk appetite and emerging market sentiment
• During global uncertainty, FIIs typically reduce EM exposure, affecting Indian markets
• Positive FII flows signal confidence, attracting domestic institutional and retail investors

**Sectoral Allocation Impact:**
• **FII Overweights**: IT services, private banks, consumer goods typically favored
• **FII Underweights**: PSU stocks, small-cap stocks often avoided
• Sector rotation by FIIs creates relative performance differences

## Current FII Flow Dynamics

**Key Monitoring Metrics:**
1. **Daily Flow Data**: Available on NSE/BSE websites by 6 PM daily
2. **Monthly Trends**: Sustained patterns more important than daily volatility
3. **Sector-wise Allocation**: Track where FIIs are increasing/decreasing exposure
4. **Derivative Positions**: FII futures/options positions indicate market direction bias

**Global Factors Affecting FII Flows:**
• **US Federal Reserve Policy**: Rate hikes typically reduce FII flows to India
• **Dollar Strength**: Strong USD makes Indian assets relatively expensive
• **Global Growth Concerns**: Economic slowdown fears trigger risk-off sentiment
• **Geopolitical Events**: Regional tensions can cause temporary FII outflows

## Investment Strategy Implications

**During Strong FII Inflows:**
• Markets typically trend higher with reduced volatility
• Growth and momentum strategies tend to outperform
• Large-cap stocks benefit more than small/mid-caps
• Consider increasing equity allocation and reducing cash

**During FII Outflows:**
• Expect higher volatility and potential corrections
• Focus on quality stocks with strong domestic demand
• Defensive sectors (FMCG, pharma) may outperform
• Rupee depreciation benefits export-oriented stocks

**Balanced Approach:**
• **DII Support**: Domestic institutional investors often provide counter-cyclical support
• **SIP Flows**: Retail systematic investment plans provide steady inflows
• **Long-term View**: FII flows are cyclical; focus on fundamental strength

## Risk Management Considerations

**Portfolio Implications:**
• Don't time markets solely based on FII flows
• Combine flow analysis with fundamental and technical factors
• Consider currency hedging for international exposure
• Maintain diversification across market caps and sectors

**Warning Signals:**
• Sustained FII outflows (>₹10,000 crores monthly) indicate potential correction
• Divergence between FII and DII flows suggests market stress
• High FII concentration in few stocks creates single-point-of-failure risk

**Current Assessment**: Monitor US Fed policy, global growth indicators, and India-specific factors (earnings growth, policy reforms) to anticipate FII flow trends. Remember that while FII flows are important short-term drivers, long-term market performance depends on economic fundamentals and corporate earnings growth.`;
}

function generateCryptoResponse(message: string): string {
  return `Cryptocurrency markets have unique characteristics requiring specialized analysis:

**Crypto Market Dynamics:**
• **High Volatility**: 24/7 trading with significant price swings
• **Correlation**: Most altcoins correlate with Bitcoin movements
• **Regulatory Sensitivity**: Policy announcements create major price impacts
• **Institutional Adoption**: Growing institutional interest affects long-term trends

**Key Analysis Factors:**
1. **Bitcoin Dominance**: Higher dominance suggests risk-off sentiment in crypto
2. **On-chain Metrics**: Active addresses, transaction volumes, network hash rate
3. **Exchange Flows**: Large inflows to exchanges often signal selling pressure
4. **Stablecoin Supply**: Increasing supply suggests potential buying power

**Investment Categories:**
• **Store of Value**: Bitcoin as digital gold
• **Smart Contract Platforms**: Ethereum, Solana, Cardano
• **DeFi Tokens**: Uniswap, Aave, Compound
• **Layer 2 Solutions**: Polygon, Arbitrum, Optimism

**Risk Management:**
• **Position Sizing**: Use smaller positions due to high volatility
• **Dollar Cost Averaging**: Reduce timing risk in volatile markets
• **Diversification**: Don't put all funds in crypto
• **Security**: Use hardware wallets for long-term holdings

**Current Trends:**
• ETF approvals bringing institutional legitimacy
• Central Bank Digital Currencies (CBDCs) development
• DeFi innovation continuing despite regulatory challenges
• NFT and gaming token evolution

**Key Metrics to Watch:**
• Fear & Greed Index for sentiment
• Funding rates for leverage levels
• Options flow for institutional activity
• Regulatory developments globally

Remember: Crypto markets are still evolving and highly speculative. Only invest what you can afford to lose and stay updated on regulatory developments.`;
}

function generateGeneralResponse(message: string, region?: string): string {
  return `Thank you for your question about ${region || 'financial'} markets. Here's a comprehensive analysis:

**Market Analysis Framework:**
• **Fundamental Analysis**: Company financials, economic indicators, industry trends
• **Technical Analysis**: Price patterns, volume, momentum indicators
• **Sentiment Analysis**: Market psychology, investor behavior, flow data

**Key Factors to Consider:**
1. **Economic Environment**: GDP growth, inflation, interest rates
2. **Corporate Earnings**: Revenue growth, margin trends, guidance
3. **Valuation Metrics**: P/E ratios, P/B ratios, dividend yields
4. **Market Structure**: Liquidity, volatility, correlation patterns

**Investment Approach:**
• **Long-term**: Focus on quality companies with sustainable competitive advantages
• **Medium-term**: Consider economic cycles and sector rotation
• **Short-term**: Use technical analysis and sentiment indicators

**Risk Management:**
• Diversification across sectors and asset classes
• Position sizing based on conviction and risk tolerance
• Regular portfolio review and rebalancing
• Stop-loss strategies for risk control

**Current Market Considerations:**
${region === 'india' ? '• Strong domestic growth story with demographic dividend\n• Policy support for manufacturing and digitalization\n• Monsoon impact on rural economy and FMCG sector' : 
  region === 'crypto' ? '• Regulatory clarity improving globally\n• Institutional adoption accelerating\n• Technology innovation in DeFi and Layer 2 solutions' : 
  '• Central bank policy transitions affecting liquidity\n• Geopolitical tensions creating uncertainty\n• Technology disruption across industries'}

**Actionable Insights:**
1. Stay informed about policy developments
2. Monitor earnings trends and revisions
3. Watch for technical breakouts or breakdowns
4. Consider global factors affecting local markets

Would you like me to elaborate on any specific aspect of this analysis?`;
}
