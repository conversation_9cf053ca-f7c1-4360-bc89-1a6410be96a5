import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { message, category, complexity, region, context } = await request.json();

    // Generate AI response based on the question
    const aiResponse = await generateAIResponse(message, category, complexity, region, context);

    return NextResponse.json({
      success: true,
      response: aiResponse,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error in AI chat:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to generate AI response',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

async function generateAIResponse(
  message: string, 
  category?: string, 
  complexity?: string, 
  region?: string, 
  context?: string
): Promise<string> {
  
  // If we have Gemini API key, use it
  const geminiApiKey = process.env.GEMINI_API_KEY;
  
  if (geminiApiKey) {
    try {
      const geminiResponse = await callGeminiAPI(message, category, complexity, region, geminiApiKey);
      if (geminiResponse) return geminiResponse;
    } catch (error) {
      console.warn('Gemini API failed, falling back to structured response');
    }
  }

  // Fallback to structured AI-like responses
  return generateStructuredResponse(message, category, complexity, region);
}

async function callGeminiAPI(
  message: string, 
  category?: string, 
  complexity?: string, 
  region?: string,
  apiKey?: string
): Promise<string | null> {
  try {
    const prompt = buildGeminiPrompt(message, category, complexity, region);
    
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        }
      })
    });

    const data = await response.json();
    
    if (data.candidates && data.candidates[0] && data.candidates[0].content) {
      return data.candidates[0].content.parts[0].text;
    }
    
    return null;
  } catch (error) {
    console.error('Gemini API error:', error);
    return null;
  }
}

function buildGeminiPrompt(message: string, category?: string, complexity?: string, region?: string): string {
  let prompt = `You are an expert financial analyst and market educator. Please provide a comprehensive, educational answer to the following question about ${region || 'financial'} markets.

Question: ${message}

Context:
- Category: ${category || 'general market analysis'}
- Complexity Level: ${complexity || 'intermediate'} (adjust your explanation accordingly)
- Region Focus: ${region || 'global'} markets

Please provide:
1. A clear, educational explanation
2. Real-world examples where relevant
3. Key factors to consider
4. Practical insights for investors
5. Any important risks or considerations

Keep your response informative, accurate, and tailored to the ${complexity || 'intermediate'} level. Use specific examples and data points where possible.`;

  return prompt;
}

function generateStructuredResponse(message: string, category?: string, complexity?: string, region?: string): string {
  const lowerMessage = message.toLowerCase();
  
  // Market sentiment questions
  if (lowerMessage.includes('sentiment') || lowerMessage.includes('bullish') || lowerMessage.includes('bearish')) {
    return generateSentimentResponse(message, region);
  }
  
  // Sector analysis questions
  if (lowerMessage.includes('sector') || lowerMessage.includes('it') || lowerMessage.includes('banking') || lowerMessage.includes('pharma')) {
    return generateSectorResponse(message, region);
  }
  
  // Technical analysis questions
  if (lowerMessage.includes('technical') || lowerMessage.includes('rsi') || lowerMessage.includes('oversold') || lowerMessage.includes('chart')) {
    return generateTechnicalResponse(message, region);
  }
  
  // RBI/Fed policy questions
  if (lowerMessage.includes('rbi') || lowerMessage.includes('fed') || lowerMessage.includes('rate') || lowerMessage.includes('policy')) {
    return generatePolicyResponse(message, region);
  }
  
  // FII/Investment flow questions
  if (lowerMessage.includes('fii') || lowerMessage.includes('flow') || lowerMessage.includes('foreign')) {
    return generateFlowResponse(message, region);
  }
  
  // Crypto questions
  if (lowerMessage.includes('crypto') || lowerMessage.includes('bitcoin') || lowerMessage.includes('ethereum') || lowerMessage.includes('defi')) {
    return generateCryptoResponse(message);
  }
  
  // General market questions
  return generateGeneralResponse(message, region);
}

function generateSentimentResponse(message: string, region?: string): string {
  const regionText = region === 'india' ? 'Indian' : region === 'crypto' ? 'crypto' : 'global';
  
  return `Market sentiment in ${regionText} markets is influenced by several key factors:

**Current Sentiment Drivers:**
• **Institutional Flows**: ${region === 'india' ? 'FII and DII buying patterns' : 'Institutional investment trends'} significantly impact sentiment
• **Economic Indicators**: GDP growth, inflation data, and employment figures shape investor confidence
• **Policy Decisions**: ${region === 'india' ? 'RBI monetary policy' : region === 'crypto' ? 'Regulatory developments' : 'Central bank policies'} create directional bias
• **Global Factors**: Geopolitical events and international market movements affect sentiment

**Key Sentiment Indicators to Watch:**
1. **Volume Analysis**: Higher volumes during rallies indicate strong conviction
2. **Volatility Index**: ${region === 'india' ? 'India VIX' : 'VIX'} levels show fear/greed in the market
3. **Breadth Indicators**: Advance-decline ratios reveal underlying strength
4. **Sector Rotation**: Money flow between sectors indicates sentiment shifts

**Investment Implications:**
• **Bullish Sentiment**: Consider momentum strategies, growth stocks, and sector leaders
• **Bearish Sentiment**: Focus on defensive stocks, value opportunities, and risk management
• **Neutral Sentiment**: Range-bound strategies and selective stock picking work best

Remember that sentiment can change quickly, so it's important to combine sentiment analysis with fundamental and technical factors for better investment decisions.`;
}

function generateSectorResponse(message: string, region?: string): string {
  const regionText = region === 'india' ? 'Indian' : region === 'crypto' ? 'crypto' : 'global';
  
  return `Sector analysis in ${regionText} markets requires understanding both cyclical and structural trends:

**Key Sector Dynamics:**
• **Cyclical Sectors**: Banking, auto, metals respond to economic cycles
• **Defensive Sectors**: FMCG, pharma, utilities provide stability during downturns
• **Growth Sectors**: Technology, healthcare offer long-term expansion potential

**Current Sector Trends:**
1. **Technology**: ${region === 'india' ? 'Indian IT benefits from digital transformation and rupee depreciation' : 'AI revolution driving valuations higher'}
2. **Banking**: ${region === 'india' ? 'Credit growth recovery but NIM pressure from rate cycles' : 'Interest rate sensitivity affecting margins'}
3. **Healthcare/Pharma**: Defensive characteristics with growth from aging demographics
4. **Energy**: Transition to renewables creating winners and losers

**Sector Rotation Strategy:**
• **Early Cycle**: Technology, consumer discretionary lead
• **Mid Cycle**: Industrials, materials gain momentum
• **Late Cycle**: Energy, financials outperform
• **Recession**: Utilities, consumer staples provide safety

**Analysis Framework:**
1. **Relative Performance**: Compare sector vs benchmark returns
2. **Earnings Growth**: Sector-wise earnings revision trends
3. **Valuation Metrics**: P/E, P/B ratios relative to historical averages
4. **Policy Impact**: Government policies affecting specific sectors

Focus on sectors with improving fundamentals, reasonable valuations, and positive policy tailwinds for better risk-adjusted returns.`;
}

function generateTechnicalResponse(message: string, region?: string): string {
  return `Technical analysis provides valuable insights for timing and risk management:

**Key Technical Indicators:**
• **RSI (Relative Strength Index)**: Values below 30 suggest oversold conditions, above 70 indicate overbought
• **Moving Averages**: 20, 50, 200-day MAs show trend direction and support/resistance
• **MACD**: Momentum indicator showing trend changes and divergences
• **Volume**: Confirms price movements and indicates conviction

**Oversold Opportunities:**
1. **RSI < 30**: Look for stocks with strong fundamentals trading at oversold levels
2. **Support Levels**: Identify key support zones where buying interest emerges
3. **Bullish Divergence**: Price makes lower lows while indicators make higher lows
4. **Volume Confirmation**: Increased volume on bounces confirms buying interest

**Risk Management:**
• **Stop Losses**: Place below key support levels to limit downside
• **Position Sizing**: Reduce size in volatile or uncertain conditions
• **Time Frames**: Align technical signals with your investment horizon
• **Confirmation**: Use multiple indicators to confirm signals

**Market-Specific Considerations:**
${region === 'india' ? '• India VIX above 20 suggests high volatility - be cautious\n• Watch for FII flow data to confirm technical signals' : 
  region === 'crypto' ? '• Crypto markets are highly volatile - use smaller position sizes\n• Watch Bitcoin dominance for altcoin opportunities' : 
  '• VIX above 25 indicates fear - often good for contrarian plays\n• Watch sector rotation for momentum opportunities'}

Remember: Technical analysis works best when combined with fundamental analysis and proper risk management. No indicator is perfect, so always use multiple confirmations.`;
}

function generatePolicyResponse(message: string, region?: string): string {
  const policyMaker = region === 'india' ? 'RBI' : region === 'crypto' ? 'Regulators' : 'Federal Reserve';
  
  return `${policyMaker} policy decisions have significant market implications:

**Policy Impact on Markets:**
• **Interest Rates**: ${region === 'india' ? 'RBI rate changes affect borrowing costs and sector performance' : region === 'crypto' ? 'Regulatory clarity affects adoption and institutional investment' : 'Fed rates influence global liquidity and risk appetite'}
• **Liquidity**: Policy changes affect money supply and market liquidity
• **Sector Rotation**: Different sectors benefit from different policy stances
• **Currency Impact**: Policy decisions affect exchange rates and international flows

**Sector-wise Impact:**
1. **Banking**: ${region === 'india' ? 'Rate hikes improve NIMs but may increase NPAs' : 'Interest rate changes directly affect net interest margins'}
2. **Real Estate**: Sensitive to interest rate changes and credit availability
3. **FMCG**: Rural demand affected by policy support and inflation
4. **Technology**: ${region === 'india' ? 'Export-oriented, benefits from supportive policies' : 'Growth stocks sensitive to rate changes'}

**Investment Strategy:**
• **Rate Hike Cycle**: Focus on banks, value stocks, and defensive sectors
• **Rate Cut Cycle**: Growth stocks, real estate, and cyclicals outperform
• **Policy Uncertainty**: Maintain diversification and reduce leverage

**Key Indicators to Watch:**
• Policy meeting minutes and forward guidance
• Inflation data influencing policy decisions
• Economic growth indicators
• Global policy coordination

${region === 'india' ? 'For Indian markets, also monitor fiscal policy, GST changes, and government spending patterns as they significantly impact sector performance.' : 
  region === 'crypto' ? 'For crypto markets, regulatory developments in major economies like US, EU, and Asia are crucial for long-term adoption trends.' : 
  'For global markets, coordinate policies between major central banks often drive cross-border capital flows.'}`;
}

function generateFlowResponse(message: string, region?: string): string {
  return `Investment flows are crucial drivers of market performance:

**Types of Flows:**
• **FII (Foreign Institutional Investors)**: International funds investing in domestic markets
• **DII (Domestic Institutional Investors)**: Local institutions like mutual funds, insurance companies
• **Retail Flows**: Individual investor participation through direct and mutual fund investments

**Flow Impact on Markets:**
1. **Positive Flows**: Create upward pressure on prices, improve liquidity
2. **Negative Flows**: Lead to selling pressure, increased volatility
3. **Sector Allocation**: Flows into specific sectors drive relative performance
4. **Currency Impact**: Large flows affect exchange rates

**Key Flow Indicators:**
• Daily FII/DII data from exchanges
• Mutual fund inflows/outflows
• ETF flows by sector and region
• Derivative positions (F&O data)

**Investment Implications:**
• **Strong Inflows**: Markets tend to trend higher, momentum strategies work
• **Outflows**: Focus on quality stocks, defensive positioning
• **Mixed Flows**: Stock-specific performance, active selection important

**Monitoring Strategy:**
1. **Weekly Flow Trends**: Look for sustained patterns rather than daily noise
2. **Sector Flows**: Identify where smart money is moving
3. **Global Context**: Understand if flows are India-specific or part of broader EM trends
4. **Correlation Analysis**: How flows correlate with market performance

**Risk Factors:**
• Sudden flow reversals can cause sharp corrections
• Over-reliance on foreign flows creates vulnerability
• Domestic flows provide more stability than foreign flows

Track flow data regularly but combine with fundamental and technical analysis for comprehensive market understanding.`;
}

function generateCryptoResponse(message: string): string {
  return `Cryptocurrency markets have unique characteristics requiring specialized analysis:

**Crypto Market Dynamics:**
• **High Volatility**: 24/7 trading with significant price swings
• **Correlation**: Most altcoins correlate with Bitcoin movements
• **Regulatory Sensitivity**: Policy announcements create major price impacts
• **Institutional Adoption**: Growing institutional interest affects long-term trends

**Key Analysis Factors:**
1. **Bitcoin Dominance**: Higher dominance suggests risk-off sentiment in crypto
2. **On-chain Metrics**: Active addresses, transaction volumes, network hash rate
3. **Exchange Flows**: Large inflows to exchanges often signal selling pressure
4. **Stablecoin Supply**: Increasing supply suggests potential buying power

**Investment Categories:**
• **Store of Value**: Bitcoin as digital gold
• **Smart Contract Platforms**: Ethereum, Solana, Cardano
• **DeFi Tokens**: Uniswap, Aave, Compound
• **Layer 2 Solutions**: Polygon, Arbitrum, Optimism

**Risk Management:**
• **Position Sizing**: Use smaller positions due to high volatility
• **Dollar Cost Averaging**: Reduce timing risk in volatile markets
• **Diversification**: Don't put all funds in crypto
• **Security**: Use hardware wallets for long-term holdings

**Current Trends:**
• ETF approvals bringing institutional legitimacy
• Central Bank Digital Currencies (CBDCs) development
• DeFi innovation continuing despite regulatory challenges
• NFT and gaming token evolution

**Key Metrics to Watch:**
• Fear & Greed Index for sentiment
• Funding rates for leverage levels
• Options flow for institutional activity
• Regulatory developments globally

Remember: Crypto markets are still evolving and highly speculative. Only invest what you can afford to lose and stay updated on regulatory developments.`;
}

function generateGeneralResponse(message: string, region?: string): string {
  return `Thank you for your question about ${region || 'financial'} markets. Here's a comprehensive analysis:

**Market Analysis Framework:**
• **Fundamental Analysis**: Company financials, economic indicators, industry trends
• **Technical Analysis**: Price patterns, volume, momentum indicators
• **Sentiment Analysis**: Market psychology, investor behavior, flow data

**Key Factors to Consider:**
1. **Economic Environment**: GDP growth, inflation, interest rates
2. **Corporate Earnings**: Revenue growth, margin trends, guidance
3. **Valuation Metrics**: P/E ratios, P/B ratios, dividend yields
4. **Market Structure**: Liquidity, volatility, correlation patterns

**Investment Approach:**
• **Long-term**: Focus on quality companies with sustainable competitive advantages
• **Medium-term**: Consider economic cycles and sector rotation
• **Short-term**: Use technical analysis and sentiment indicators

**Risk Management:**
• Diversification across sectors and asset classes
• Position sizing based on conviction and risk tolerance
• Regular portfolio review and rebalancing
• Stop-loss strategies for risk control

**Current Market Considerations:**
${region === 'india' ? '• Strong domestic growth story with demographic dividend\n• Policy support for manufacturing and digitalization\n• Monsoon impact on rural economy and FMCG sector' : 
  region === 'crypto' ? '• Regulatory clarity improving globally\n• Institutional adoption accelerating\n• Technology innovation in DeFi and Layer 2 solutions' : 
  '• Central bank policy transitions affecting liquidity\n• Geopolitical tensions creating uncertainty\n• Technology disruption across industries'}

**Actionable Insights:**
1. Stay informed about policy developments
2. Monitor earnings trends and revisions
3. Watch for technical breakouts or breakdowns
4. Consider global factors affecting local markets

Would you like me to elaborate on any specific aspect of this analysis?`;
}
