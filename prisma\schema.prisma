// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?   // For credentials login
  role          String    @default("user") // user, admin, premium
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  
  // Profile information
  firstName     String?
  lastName      String?
  phone         String?
  dateOfBirth   DateTime?
  country       String?
  city          String?
  
  // Financial profile
  investmentExperience String? // beginner, intermediate, advanced
  riskTolerance        String? // low, medium, high
  investmentGoals      String[] // retirement, wealth_building, short_term, etc.
  annualIncome         String? // <50k, 50k-100k, 100k-250k, 250k+
  
  // App preferences
  preferredCurrency    String   @default("USD") // USD, INR
  preferredRegion      String   @default("global") // india, global, crypto
  notificationsEnabled Boolean  @default(true)
  darkMode             Boolean  @default(true)
  
  // Subscription & features
  subscriptionTier     String   @default("free") // free, premium, enterprise
  subscriptionExpiry   DateTime?
  apiCallsUsed         Int      @default(0)
  apiCallsLimit        Int      @default(100)
  
  accounts Account[]
  sessions Session[]
  portfolios Portfolio[]
  activities UserActivity[]

  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// Portfolio tracking
model Portfolio {
  id          String   @id @default(cuid())
  userId      String
  name        String
  description String?
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  holdings    Holding[]
  
  @@map("portfolios")
}

model Holding {
  id          String   @id @default(cuid())
  portfolioId String
  symbol      String
  name        String
  quantity    Float
  avgPrice    Float
  currentPrice Float?
  sector      String?
  exchange    String?
  currency    String   @default("USD")
  addedAt     DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  portfolio   Portfolio @relation(fields: [portfolioId], references: [id], onDelete: Cascade)
  
  @@map("holdings")
}

// User activity tracking
model UserActivity {
  id        String   @id @default(cuid())
  userId    String
  action    String   // login, logout, view_insights, ai_query, etc.
  details   Json?    // Additional details about the action
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("user_activities")
}
