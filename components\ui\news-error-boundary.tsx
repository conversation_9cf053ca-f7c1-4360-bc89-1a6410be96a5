"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, Wifi, WifiOff, Clock } from 'lucide-react';

interface NewsErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface NewsErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

export class NewsErrorBoundary extends React.Component<NewsErrorBoundaryProps, NewsErrorBoundaryState> {
  constructor(props: NewsErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): NewsErrorBoundaryState {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('News Error Boundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error!} retry={this.handleRetry} />;
      }

      return (
        <NewsErrorFallback 
          error={this.state.error!} 
          retry={this.handleRetry}
        />
      );
    }

    return this.props.children;
  }
}

interface NewsErrorFallbackProps {
  error: Error;
  retry: () => void;
}

export function NewsErrorFallback({ error, retry }: NewsErrorFallbackProps) {
  const getErrorType = (error: Error) => {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return 'network';
    }
    if (message.includes('api') || message.includes('rate limit')) {
      return 'api';
    }
    if (message.includes('timeout')) {
      return 'timeout';
    }
    return 'unknown';
  };

  const errorType = getErrorType(error);

  const getErrorConfig = (type: string) => {
    switch (type) {
      case 'network':
        return {
          icon: WifiOff,
          title: 'Network Connection Error',
          description: 'Unable to connect to news services. Please check your internet connection.',
          color: 'text-red-400',
          bgColor: 'bg-red-500/10',
          borderColor: 'border-red-500/30'
        };
      case 'api':
        return {
          icon: AlertTriangle,
          title: 'News Service Unavailable',
          description: 'The news service is temporarily unavailable. This might be due to rate limits or service maintenance.',
          color: 'text-yellow-400',
          bgColor: 'bg-yellow-500/10',
          borderColor: 'border-yellow-500/30'
        };
      case 'timeout':
        return {
          icon: Clock,
          title: 'Request Timeout',
          description: 'The request took too long to complete. Please try again.',
          color: 'text-orange-400',
          bgColor: 'bg-orange-500/10',
          borderColor: 'border-orange-500/30'
        };
      default:
        return {
          icon: AlertTriangle,
          title: 'Something Went Wrong',
          description: 'An unexpected error occurred while loading news.',
          color: 'text-red-400',
          bgColor: 'bg-red-500/10',
          borderColor: 'border-red-500/30'
        };
    }
  };

  const config = getErrorConfig(errorType);
  const Icon = config.icon;

  return (
    <Card className={`bg-slate-800/50 border-slate-700 backdrop-blur-sm ${config.borderColor}`}>
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Icon className={`h-5 w-5 ${config.color}`} />
          {config.title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-slate-300">{config.description}</p>
        
        <div className="flex items-center gap-2">
          <Button 
            onClick={retry}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
          
          <Badge variant="outline" className={`${config.bgColor} ${config.color} ${config.borderColor}`}>
            Error: {errorType}
          </Badge>
        </div>

        {process.env.NODE_ENV === 'development' && (
          <details className="mt-4">
            <summary className="text-sm text-slate-400 cursor-pointer hover:text-slate-300">
              Technical Details (Development)
            </summary>
            <pre className="mt-2 p-3 bg-slate-900 rounded text-xs text-slate-300 overflow-auto">
              {error.stack}
            </pre>
          </details>
        )}
      </CardContent>
    </Card>
  );
}

// Network status component
export function NetworkStatus() {
  const [isOnline, setIsOnline] = React.useState(true);

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (isOnline) return null;

  return (
    <div className="fixed top-20 left-1/2 transform -translate-x-1/2 z-50">
      <Badge variant="outline" className="bg-red-500/10 text-red-300 border-red-500/30">
        <WifiOff className="h-3 w-3 mr-1" />
        No Internet Connection
      </Badge>
    </div>
  );
}

// Loading state component for news
export function NewsLoadingState({ message = "Loading financial news..." }: { message?: string }) {
  return (
    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
      <CardContent className="flex items-center justify-center py-12">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto"></div>
          <p className="text-slate-300">{message}</p>
        </div>
      </CardContent>
    </Card>
  );
}

// Empty state component
export function NewsEmptyState({ 
  title = "No News Available", 
  description = "No financial news found for your current filters.",
  onRetry 
}: { 
  title?: string; 
  description?: string; 
  onRetry?: () => void;
}) {
  return (
    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
      <CardContent className="flex items-center justify-center py-12">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-slate-700 rounded-full flex items-center justify-center mx-auto">
            <AlertTriangle className="h-8 w-8 text-slate-500" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-white mb-2">{title}</h3>
            <p className="text-slate-400">{description}</p>
          </div>
          {onRetry && (
            <Button 
              variant="outline" 
              onClick={onRetry}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
