// Advanced Market Intelligence Types

export interface MarketSentiment {
  market: 'indian' | 'foreign' | 'crypto';
  sentiment: 'bullish' | 'bearish' | 'neutral';
  score: number; // -1 to 1 scale
  confidence: number; // 0 to 1 scale
  trend: 'rising' | 'falling' | 'stable';
  volatility: 'low' | 'medium' | 'high';
  lastUpdated: Date;
}

export interface NewsImpactScore {
  articleId: string;
  relevanceScore: number; // 0 to 1
  impactScore: number; // 0 to 1
  sentimentScore: number; // -1 to 1
  urgencyScore: number; // 0 to 1
  marketRelevance: {
    indian: number;
    foreign: number;
    crypto: number;
  };
  keyFactors: string[];
  predictedImpact: 'high' | 'medium' | 'low';
}

export interface MarketTrend {
  market: 'indian' | 'foreign' | 'crypto';
  direction: 'upward' | 'downward' | 'sideways';
  strength: number; // 0 to 1
  duration: 'short' | 'medium' | 'long';
  confidence: number;
  supportingFactors: string[];
  riskFactors: string[];
}

export interface MarketIntelligence {
  timestamp: Date;
  overallSentiment: MarketSentiment[];
  trends: MarketTrend[];
  topImpactNews: NewsImpactScore[];
  marketCorrelations: MarketCorrelationMatrix;
  volatilityIndex: VolatilityMetrics;
  riskAssessment: RiskAssessment;
  liveMarketData: LiveMarketData;
  technicalIndicators: TechnicalIndicators;
  economicIndicators: EconomicIndicators;
  sectorAnalysis: SectorAnalysis[];
  marketOutlook: MarketOutlook;
}

export interface MarketCorrelationMatrix {
  indianForeign: CorrelationData;
  indianCrypto: CorrelationData;
  foreignCrypto: CorrelationData;
  sectorCorrelations: SectorCorrelation[];
  timeframedCorrelations: {
    '1h': number;
    '24h': number;
    '7d': number;
    '30d': number;
  };
}

export interface CorrelationData {
  coefficient: number; // -1 to 1
  strength: 'very_strong' | 'strong' | 'moderate' | 'weak' | 'very_weak';
  direction: 'positive' | 'negative';
  reliability: number; // 0 to 1
  lastUpdated: Date;
  historicalTrend: number[];
}

export interface VolatilityMetrics {
  indian: VolatilityData;
  foreign: VolatilityData;
  crypto: VolatilityData;
  crossMarketVolatility: number;
  volatilityTrend: 'increasing' | 'decreasing' | 'stable';
}

export interface VolatilityData {
  current: number;
  average30d: number;
  percentile: number; // Where current volatility ranks historically
  trend: 'rising' | 'falling' | 'stable';
  drivers: string[];
}

export interface RiskAssessment {
  overallRisk: 'very_low' | 'low' | 'medium' | 'high' | 'very_high' | 'extreme';
  riskScore: number; // 0 to 100
  riskFactors: RiskFactor[];
  mitigationStrategies: string[];
  timeHorizon: 'immediate' | 'short_term' | 'medium_term' | 'long_term';
  confidenceLevel: number;
}

export interface RiskFactor {
  type: 'market' | 'economic' | 'geopolitical' | 'technical' | 'regulatory';
  description: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
  probability: number; // 0 to 1
  timeframe: string;
  affectedMarkets: string[];
}

export interface LiveMarketData {
  indices: MarketIndex[];
  currencies: CurrencyData[];
  commodities: CommodityData[];
  bonds: BondData[];
  lastUpdated: Date;
  marketStatus: MarketStatus;
}

export interface MarketIndex {
  symbol: string;
  name: string;
  value: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  pe?: number;
  high52w: number;
  low52w: number;
  trend: 'bullish' | 'bearish' | 'neutral';
}

export interface CurrencyData {
  pair: string;
  rate: number;
  change: number;
  changePercent: number;
  trend: 'strengthening' | 'weakening' | 'stable';
  volatility: number;
}

export interface TechnicalIndicators {
  indian: TechnicalData;
  foreign: TechnicalData;
  crypto: TechnicalData;
  signals: TechnicalSignal[];
}

export interface TechnicalData {
  rsi: number;
  macd: { value: number; signal: number; histogram: number };
  movingAverages: { ma20: number; ma50: number; ma200: number };
  support: number[];
  resistance: number[];
  trendStrength: number;
  momentum: 'strong_bullish' | 'bullish' | 'neutral' | 'bearish' | 'strong_bearish';
}

export interface EconomicIndicators {
  indian: EconomicData;
  global: EconomicData;
  comparative: ComparativeMetrics;
}

export interface EconomicData {
  gdpGrowth: number;
  inflation: number;
  unemployment: number;
  interestRate: number;
  fiscalDeficit: number;
  currentAccount: number;
  manufacturingPMI: number;
  servicesPMI: number;
  consumerConfidence: number;
}

export interface SectorAnalysis {
  sector: string;
  performance: number;
  sentiment: 'positive' | 'negative' | 'neutral';
  outlook: 'bullish' | 'bearish' | 'neutral';
  keyDrivers: string[];
  risks: string[];
  topPerformers: string[];
  laggards: string[];
}

export interface MarketOutlook {
  shortTerm: OutlookData; // 1-3 months
  mediumTerm: OutlookData; // 3-12 months
  longTerm: OutlookData; // 1-3 years
  keyThemes: string[];
  catalysts: string[];
  risks: string[];
}

export interface OutlookData {
  direction: 'bullish' | 'bearish' | 'neutral';
  confidence: number;
  targetRange: { low: number; high: number };
  keyFactors: string[];
  probability: number;
}

export interface SectorCorrelation {
  sector1: string;
  sector2: string;
  correlation: number;
  significance: 'high' | 'medium' | 'low';
}

export interface CommodityData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  trend: 'rising' | 'falling' | 'stable';
}

export interface BondData {
  name: string;
  yield: number;
  change: number;
  duration: number;
  rating: string;
}

export interface MarketStatus {
  indian: 'open' | 'closed' | 'pre_market' | 'after_hours';
  us: 'open' | 'closed' | 'pre_market' | 'after_hours';
  european: 'open' | 'closed' | 'pre_market' | 'after_hours';
  crypto: 'open'; // Always open
}

export interface TechnicalSignal {
  type: 'buy' | 'sell' | 'hold';
  strength: 'strong' | 'moderate' | 'weak';
  indicator: string;
  market: string;
  description: string;
  confidence: number;
}

export interface ComparativeMetrics {
  gdpGrowthDiff: number;
  inflationDiff: number;
  interestRateDiff: number;
  competitivenessIndex: number;
}

export interface SentimentAnalysisConfig {
  positiveKeywords: string[];
  negativeKeywords: string[];
  neutralKeywords: string[];
  marketSpecificTerms: {
    indian: string[];
    foreign: string[];
    crypto: string[];
  };
  impactMultipliers: {
    earnings: number;
    policy: number;
    geopolitical: number;
    technical: number;
  };
}

export interface TrendAnalysisAlgorithm {
  name: string;
  weight: number;
  calculate: (articles: any[], timeframe: number) => number;
}

export interface MarketIndicator {
  name: string;
  value: number;
  change: number;
  trend: 'up' | 'down' | 'stable';
  significance: 'high' | 'medium' | 'low';
  description: string;
}

export interface AdvancedInsight {
  type: 'opportunity' | 'risk' | 'trend' | 'alert';
  market: 'indian' | 'foreign' | 'crypto' | 'global';
  title: string;
  description: string;
  confidence: number;
  timeframe: 'immediate' | 'short' | 'medium' | 'long';
  actionable: boolean;
  relatedNews: string[];
  impact: 'high' | 'medium' | 'low';
}

// Financial NLP Configuration
export const FINANCIAL_SENTIMENT_CONFIG: SentimentAnalysisConfig = {
  positiveKeywords: [
    'surge', 'rally', 'bullish', 'growth', 'profit', 'gain', 'rise', 'boost', 
    'strong', 'positive', 'optimistic', 'upgrade', 'beat', 'exceed', 'outperform',
    'recovery', 'expansion', 'breakthrough', 'milestone', 'success', 'achievement'
  ],
  negativeKeywords: [
    'crash', 'plunge', 'bearish', 'loss', 'decline', 'fall', 'drop', 'weak',
    'negative', 'pessimistic', 'downgrade', 'miss', 'underperform', 'recession',
    'crisis', 'concern', 'risk', 'volatility', 'uncertainty', 'correction'
  ],
  neutralKeywords: [
    'stable', 'unchanged', 'flat', 'sideways', 'consolidation', 'range-bound',
    'mixed', 'varied', 'moderate', 'steady', 'consistent', 'maintained'
  ],
  marketSpecificTerms: {
    indian: [
      'RBI', 'Sensex', 'Nifty', 'BSE', 'NSE', 'rupee', 'GST', 'SEBI',
      'FII', 'DII', 'mutual fund', 'SIP', 'IPO', 'QIP', 'bonus', 'dividend'
    ],
    foreign: [
      'Fed', 'Federal Reserve', 'ECB', 'BOJ', 'S&P 500', 'Nasdaq', 'Dow Jones',
      'dollar', 'euro', 'yen', 'pound', 'treasury', 'yield', 'inflation', 'GDP'
    ],
    crypto: [
      'Bitcoin', 'Ethereum', 'blockchain', 'DeFi', 'NFT', 'altcoin', 'mining',
      'wallet', 'exchange', 'staking', 'yield farming', 'smart contract'
    ]
  },
  impactMultipliers: {
    earnings: 1.5,
    policy: 2.0,
    geopolitical: 1.8,
    technical: 1.2
  }
};

// Market correlation patterns
export const MARKET_CORRELATIONS = {
  highCorrelation: 0.7,
  mediumCorrelation: 0.4,
  lowCorrelation: 0.2,
  timeframes: {
    immediate: 1, // 1 hour
    short: 24, // 24 hours
    medium: 168, // 1 week
    long: 720 // 1 month
  }
};

// Volatility thresholds
export const VOLATILITY_THRESHOLDS = {
  low: 0.3,
  medium: 0.6,
  high: 0.8,
  extreme: 0.95
};

// News impact categories
export const NEWS_IMPACT_CATEGORIES = {
  earnings: {
    keywords: ['earnings', 'results', 'profit', 'revenue', 'guidance'],
    baseImpact: 0.7,
    timeDecay: 0.8
  },
  policy: {
    keywords: ['policy', 'rate', 'regulation', 'government', 'central bank'],
    baseImpact: 0.9,
    timeDecay: 0.6
  },
  geopolitical: {
    keywords: ['war', 'conflict', 'trade', 'sanctions', 'election'],
    baseImpact: 0.8,
    timeDecay: 0.7
  },
  technical: {
    keywords: ['technical', 'support', 'resistance', 'breakout', 'pattern'],
    baseImpact: 0.5,
    timeDecay: 0.9
  },
  corporate: {
    keywords: ['merger', 'acquisition', 'IPO', 'split', 'dividend'],
    baseImpact: 0.6,
    timeDecay: 0.8
  }
};
