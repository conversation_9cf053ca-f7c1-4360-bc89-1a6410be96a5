const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function setupNextAuthTables() {
  try {
    console.log('🔧 Setting up NextAuth database tables...')
    
    // Create Account table
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "Account" (
        "id" TEXT NOT NULL,
        "userId" TEXT NOT NULL,
        "type" TEXT NOT NULL,
        "provider" TEXT NOT NULL,
        "providerAccountId" TEXT NOT NULL,
        "refresh_token" TEXT,
        "access_token" TEXT,
        "expires_at" INTEGER,
        "token_type" TEXT,
        "scope" TEXT,
        "id_token" TEXT,
        "session_state" TEXT,
        CONSTRAINT "Account_pkey" PRIMARY KEY ("id")
      )
    `
    
    // Create Session table
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "Session" (
        "id" TEXT NOT NULL,
        "sessionToken" TEXT NOT NULL,
        "userId" TEXT NOT NULL,
        "expires" TIMESTAMP(3) NOT NULL,
        CONSTRAINT "Session_pkey" PRIMARY KEY ("id")
      )
    `
    
    // Create VerificationToken table
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "VerificationToken" (
        "identifier" TEXT NOT NULL,
        "token" TEXT NOT NULL,
        "expires" TIMESTAMP(3) NOT NULL
      )
    `
    
    // Create unique indexes
    await prisma.$executeRaw`
      CREATE UNIQUE INDEX IF NOT EXISTS "Account_provider_providerAccountId_key" 
      ON "Account"("provider", "providerAccountId")
    `
    
    await prisma.$executeRaw`
      CREATE UNIQUE INDEX IF NOT EXISTS "Session_sessionToken_key" 
      ON "Session"("sessionToken")
    `
    
    await prisma.$executeRaw`
      CREATE UNIQUE INDEX IF NOT EXISTS "VerificationToken_token_key" 
      ON "VerificationToken"("token")
    `
    
    await prisma.$executeRaw`
      CREATE UNIQUE INDEX IF NOT EXISTS "VerificationToken_identifier_token_key" 
      ON "VerificationToken"("identifier", "token")
    `
    
    // Add foreign key constraints
    await prisma.$executeRaw`
      ALTER TABLE "Account" 
      ADD CONSTRAINT IF NOT EXISTS "Account_userId_fkey" 
      FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE
    `
    
    await prisma.$executeRaw`
      ALTER TABLE "Session" 
      ADD CONSTRAINT IF NOT EXISTS "Session_userId_fkey" 
      FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE
    `
    
    console.log('✅ NextAuth tables setup completed!')
    
    // Test the tables
    const accountCount = await prisma.$queryRaw`SELECT COUNT(*) FROM "Account"`
    const sessionCount = await prisma.$queryRaw`SELECT COUNT(*) FROM "Session"`
    
    console.log(`📊 Account table: ${accountCount[0].count} records`)
    console.log(`📊 Session table: ${sessionCount[0].count} records`)
    
  } catch (error) {
    console.error('❌ NextAuth tables setup failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

setupNextAuthTables()
