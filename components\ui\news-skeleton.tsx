// News loading skeleton component

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

export function NewsCardSkeleton() {
  return (
    <Card className="bg-slate-800/50 border-slate-700 backdrop-blur-sm">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Skeleton className="h-4 w-16 bg-slate-700" />
              <Skeleton className="h-3 w-12 bg-slate-700" />
            </div>
            <Skeleton className="h-5 w-full bg-slate-700 mb-1" />
            <Skeleton className="h-5 w-3/4 bg-slate-700" />
          </div>
          <Skeleton className="w-16 h-16 rounded-lg bg-slate-700 flex-shrink-0" />
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2 mb-3">
          <Skeleton className="h-3 w-full bg-slate-700" />
          <Skeleton className="h-3 w-full bg-slate-700" />
          <Skeleton className="h-3 w-2/3 bg-slate-700" />
        </div>
        
        <div className="flex gap-1 mb-3">
          <Skeleton className="h-5 w-12 bg-slate-700 rounded-full" />
          <Skeleton className="h-5 w-16 bg-slate-700 rounded-full" />
          <Skeleton className="h-5 w-14 bg-slate-700 rounded-full" />
        </div>
        
        <div className="flex items-center justify-between">
          <Skeleton className="h-4 w-20 bg-slate-700" />
          <Skeleton className="h-6 w-6 bg-slate-700 rounded" />
        </div>
      </CardContent>
    </Card>
  );
}

export function NewsGridSkeleton({ count = 6 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: count }).map((_, index) => (
        <NewsCardSkeleton key={index} />
      ))}
    </div>
  );
}

export function NewsListSkeleton({ count = 5 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index} className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Skeleton className="h-4 w-16 bg-slate-700" />
                  <Skeleton className="h-3 w-12 bg-slate-700" />
                </div>
                <Skeleton className="h-5 w-full bg-slate-700 mb-2" />
                <Skeleton className="h-4 w-3/4 bg-slate-700 mb-2" />
                <div className="flex gap-1">
                  <Skeleton className="h-4 w-12 bg-slate-700 rounded-full" />
                  <Skeleton className="h-4 w-16 bg-slate-700 rounded-full" />
                </div>
              </div>
              <Skeleton className="w-20 h-20 rounded-lg bg-slate-700 flex-shrink-0" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
