// AI News Impact Analyzer with Gemini Integration

import { NewsImpact, MarketData } from './types';

export class NewsImpactAnalyzer {
  
  // News Categories and Keywords
  static readonly NEWS_CATEGORIES = {
    earnings: ['earnings', 'profit', 'revenue', 'quarterly', 'results', 'guidance', 'eps'],
    regulation: ['regulation', 'policy', 'government', 'rbi', 'sebi', 'compliance', 'law'],
    macro: ['inflation', 'gdp', 'interest rate', 'fed', 'economy', 'recession', 'growth'],
    company: ['merger', 'acquisition', 'ceo', 'management', 'restructuring', 'expansion'],
    sector: ['sector', 'industry', 'vertical', 'segment', 'market share']
  };
  
  // Sentiment Keywords
  static readonly SENTIMENT_KEYWORDS = {
    positive: ['beat', 'exceed', 'strong', 'growth', 'positive', 'bullish', 'upgrade', 'buy', 'outperform'],
    negative: ['miss', 'decline', 'weak', 'loss', 'negative', 'bearish', 'downgrade', 'sell', 'underperform'],
    neutral: ['maintain', 'hold', 'stable', 'unchanged', 'neutral', 'mixed']
  };
  
  // Analyze News Impact
  static async analyzeNewsImpact(
    newsArticles: any[],
    marketData: MarketData[],
    geminiApiKey?: string
  ): Promise<NewsImpact[]> {
    const impacts: NewsImpact[] = [];
    
    for (const article of newsArticles) {
      try {
        const impact = await this.processNewsArticle(article, marketData, geminiApiKey);
        if (impact) {
          impacts.push(impact);
        }
      } catch (error) {
        console.error('Error processing news article:', error);
      }
    }
    
    // Sort by impact score and return top impacts
    return impacts
      .sort((a, b) => b.impactScore - a.impactScore)
      .slice(0, 10);
  }
  
  // Process Single News Article
  static async processNewsArticle(
    article: any,
    marketData: MarketData[],
    geminiApiKey?: string
  ): Promise<NewsImpact | null> {
    const headline = article.title || article.headline || '';
    const content = article.description || article.content || article.summary || '';
    const fullText = `${headline} ${content}`.toLowerCase();
    
    // Extract affected assets
    const affectedAssets = this.extractAffectedAssets(fullText, marketData);
    if (affectedAssets.length === 0) {
      return null; // No relevant assets found
    }
    
    // Categorize news
    const category = this.categorizeNews(fullText);
    
    // Analyze sentiment
    const sentiment = this.analyzeSentiment(fullText);
    
    // Calculate impact score
    const impactScore = this.calculateImpactScore(fullText, affectedAssets, marketData);
    
    // Generate AI analysis
    const aiAnalysis = geminiApiKey 
      ? await this.generateGeminiAnalysis(article, affectedAssets, geminiApiKey)
      : this.generateBasicAnalysis(article, affectedAssets, sentiment);
    
    // Calculate price impact
    const priceImpact = this.calculatePriceImpact(affectedAssets, marketData, sentiment, impactScore);
    
    return {
      headline,
      summary: content.substring(0, 200) + (content.length > 200 ? '...' : ''),
      affectedAssets,
      impactScore,
      sentiment,
      category,
      aiAnalysis,
      priceImpact
    };
  }
  
  // Extract Affected Assets from News Text
  static extractAffectedAssets(text: string, marketData: MarketData[]): string[] {
    const assets: string[] = [];
    
    for (const asset of marketData) {
      // Check for symbol mentions
      if (text.includes(asset.symbol.toLowerCase())) {
        assets.push(asset.symbol);
        continue;
      }
      
      // Check for company name mentions
      const nameParts = asset.name.toLowerCase().split(' ');
      const hasNameMatch = nameParts.some(part => 
        part.length > 3 && text.includes(part)
      );
      
      if (hasNameMatch) {
        assets.push(asset.symbol);
      }
    }
    
    return [...new Set(assets)]; // Remove duplicates
  }
  
  // Categorize News
  static categorizeNews(text: string): NewsImpact['category'] {
    let maxScore = 0;
    let category: NewsImpact['category'] = 'company';
    
    for (const [cat, keywords] of Object.entries(this.NEWS_CATEGORIES)) {
      const score = keywords.reduce((sum, keyword) => {
        return sum + (text.includes(keyword) ? 1 : 0);
      }, 0);
      
      if (score > maxScore) {
        maxScore = score;
        category = cat as NewsImpact['category'];
      }
    }
    
    return category;
  }
  
  // Analyze Sentiment
  static analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    let positiveScore = 0;
    let negativeScore = 0;
    
    // Count positive keywords
    for (const keyword of this.SENTIMENT_KEYWORDS.positive) {
      if (text.includes(keyword)) {
        positiveScore++;
      }
    }
    
    // Count negative keywords
    for (const keyword of this.SENTIMENT_KEYWORDS.negative) {
      if (text.includes(keyword)) {
        negativeScore++;
      }
    }
    
    // Determine sentiment
    if (positiveScore > negativeScore) return 'positive';
    if (negativeScore > positiveScore) return 'negative';
    return 'neutral';
  }
  
  // Calculate Impact Score
  static calculateImpactScore(
    text: string,
    affectedAssets: string[],
    marketData: MarketData[]
  ): number {
    let score = 0;
    
    // Base score from number of affected assets
    score += Math.min(affectedAssets.length * 10, 50);
    
    // Score from market cap of affected assets
    const totalMarketCap = affectedAssets.reduce((sum, symbol) => {
      const asset = marketData.find(m => m.symbol === symbol);
      return sum + (asset?.marketCap || 0);
    }, 0);
    
    if (totalMarketCap > 1000000000000) score += 30; // >1T market cap
    else if (totalMarketCap > 100000000000) score += 20; // >100B market cap
    else if (totalMarketCap > 10000000000) score += 10; // >10B market cap
    
    // Score from news urgency keywords
    const urgencyKeywords = ['breaking', 'urgent', 'alert', 'immediate', 'emergency'];
    for (const keyword of urgencyKeywords) {
      if (text.includes(keyword)) {
        score += 15;
        break;
      }
    }
    
    // Score from financial impact keywords
    const impactKeywords = ['billion', 'million', 'percent', '%', 'guidance', 'forecast'];
    for (const keyword of impactKeywords) {
      if (text.includes(keyword)) {
        score += 5;
      }
    }
    
    return Math.min(score, 100); // Cap at 100
  }
  
  // Generate Gemini Analysis
  static async generateGeminiAnalysis(
    article: any,
    affectedAssets: string[],
    geminiApiKey: string
  ): Promise<string> {
    try {
      const prompt = `Analyze this financial news and its market impact:

Headline: ${article.title || article.headline}
Content: ${article.description || article.content || ''}
Affected Assets: ${affectedAssets.join(', ')}

Provide a concise analysis covering:
1. Key market implications
2. Expected price impact direction
3. Time horizon for impact
4. Risk factors to consider

Keep response under 150 words and focus on actionable insights.`;

      const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': geminiApiKey
        },
        body: JSON.stringify({
          contents: [{
            parts: [{ text: prompt }]
          }]
        })
      });

      const data = await response.json();
      
      if (data.candidates && data.candidates[0]?.content?.parts?.[0]?.text) {
        return data.candidates[0].content.parts[0].text;
      }
      
      return this.generateBasicAnalysis(article, affectedAssets, 'neutral');
    } catch (error) {
      console.error('Gemini API error:', error);
      return this.generateBasicAnalysis(article, affectedAssets, 'neutral');
    }
  }
  
  // Generate Basic Analysis (Fallback)
  static generateBasicAnalysis(
    article: any,
    affectedAssets: string[],
    sentiment: 'positive' | 'negative' | 'neutral'
  ): string {
    const headline = article.title || article.headline || 'Market News';
    const assetList = affectedAssets.slice(0, 3).join(', ');
    
    let analysis = `${headline} affects ${assetList}${affectedAssets.length > 3 ? ' and others' : ''}. `;
    
    switch (sentiment) {
      case 'positive':
        analysis += "This development is likely to drive positive sentiment and potential upward price movement. ";
        analysis += "Investors should monitor for sustained momentum and consider the broader market context.";
        break;
      case 'negative':
        analysis += "This news may create downward pressure on affected assets. ";
        analysis += "Risk management and careful position sizing are recommended given the negative sentiment.";
        break;
      default:
        analysis += "Market impact remains uncertain with mixed signals. ";
        analysis += "Traders should await further clarity before making significant position changes.";
    }
    
    return analysis;
  }
  
  // Calculate Price Impact
  static calculatePriceImpact(
    affectedAssets: string[],
    marketData: MarketData[],
    sentiment: 'positive' | 'negative' | 'neutral',
    impactScore: number
  ): NewsImpact['priceImpact'] {
    const priceImpacts: NewsImpact['priceImpact'] = [];
    
    for (const symbol of affectedAssets) {
      const asset = marketData.find(m => m.symbol === symbol);
      if (!asset) continue;
      
      // Calculate expected move based on sentiment and impact score
      let expectedMove = 0;
      const baseMove = (impactScore / 100) * 3; // Max 3% move for 100 impact score
      
      switch (sentiment) {
        case 'positive':
          expectedMove = baseMove;
          break;
        case 'negative':
          expectedMove = -baseMove;
          break;
        default:
          expectedMove = 0;
      }
      
      // Adjust for volatility (higher volatility = higher potential move)
      const volatilityMultiplier = Math.min(asset.changePercent ? Math.abs(asset.changePercent) / 5 : 1, 2);
      expectedMove *= volatilityMultiplier;
      
      // Calculate confidence based on impact score and market cap
      const confidence = Math.min(
        (impactScore / 100) * 80 + 
        (asset.marketCap ? Math.min(asset.marketCap / 1000000000, 20) : 0),
        95
      );
      
      priceImpacts.push({
        symbol,
        expectedMove: Number(expectedMove.toFixed(2)),
        confidence: Number(confidence.toFixed(0))
      });
    }
    
    return priceImpacts;
  }
  
  // Get News-Driven Market Movements
  static getNewsDrivernMovements(
    newsImpacts: NewsImpact[],
    marketData: MarketData[]
  ): Array<{
    symbol: string;
    movement: number;
    newsReason: string;
    confidence: number;
  }> {
    const movements: Array<{
      symbol: string;
      movement: number;
      newsReason: string;
      confidence: number;
    }> = [];
    
    for (const impact of newsImpacts) {
      for (const priceImpact of impact.priceImpact) {
        const asset = marketData.find(m => m.symbol === priceImpact.symbol);
        if (!asset) continue;
        
        // Check if actual movement aligns with expected
        const actualMovement = asset.changePercent;
        const expectedMovement = priceImpact.expectedMove;
        
        // If movements are in same direction and significant
        if (
          Math.abs(actualMovement) > 1 &&
          Math.sign(actualMovement) === Math.sign(expectedMovement)
        ) {
          movements.push({
            symbol: priceImpact.symbol,
            movement: actualMovement,
            newsReason: impact.headline,
            confidence: priceImpact.confidence
          });
        }
      }
    }
    
    return movements.sort((a, b) => b.confidence - a.confidence);
  }
}
