'use client';

import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { cn } from '@/lib/utils';

interface NewsMarkdownProps {
  content: string;
  className?: string;
  variant?: 'insight' | 'summary' | 'compact';
}

export function NewsMarkdown({ content, className, variant = 'insight' }: NewsMarkdownProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'insight':
        return 'prose-slate prose-sm max-w-none [&>*]:text-slate-300 [&>h1]:text-white [&>h2]:text-white [&>h3]:text-white [&>strong]:text-white [&>em]:text-slate-200 [&>p]:mb-3 [&>ul]:mb-3 [&>ol]:mb-3 [&>li]:text-slate-300';
      case 'summary':
        return 'prose-slate prose-xs max-w-none [&>*]:text-slate-300 [&>p]:mb-2 [&>ul]:mb-2 [&>ol]:mb-2 [&>li]:text-slate-300 [&>strong]:text-slate-200';
      case 'compact':
        return 'prose-slate prose-xs max-w-none [&>*]:text-slate-300 [&>p]:mb-1 [&>ul]:mb-1 [&>ol]:mb-1 [&>li]:text-slate-300 [&>strong]:text-slate-200 [&>h1]:text-sm [&>h2]:text-sm [&>h3]:text-sm';
      default:
        return 'prose-slate prose-sm max-w-none [&>*]:text-slate-300';
    }
  };

  return (
    <div className={cn('prose prose-invert break-words overflow-hidden', getVariantStyles(), className)}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        components={{
          // Headings - styled for news content
          h1: ({ children }) => (
            <h1 className={cn(
              'font-bold mb-2 mt-3 first:mt-0 break-words',
              variant === 'insight' ? 'text-base text-white' : 'text-sm text-slate-200'
            )}>
              {children}
            </h1>
          ),
          h2: ({ children }) => (
            <h2 className={cn(
              'font-semibold mb-2 mt-3 first:mt-0 break-words',
              variant === 'insight' ? 'text-sm text-white' : 'text-xs text-slate-200'
            )}>
              {children}
            </h2>
          ),
          h3: ({ children }) => (
            <h3 className={cn(
              'font-medium mb-1 mt-2 first:mt-0 break-words',
              variant === 'insight' ? 'text-sm text-white' : 'text-xs text-slate-200'
            )}>
              {children}
            </h3>
          ),
          
          // Paragraphs
          p: ({ children }) => (
            <p className={cn(
              'break-words',
              variant === 'compact' ? 'mb-1' : variant === 'summary' ? 'mb-2' : 'mb-3'
            )}>
              {children}
            </p>
          ),
          
          // Lists
          ul: ({ children }) => (
            <ul className={cn(
              'list-disc list-inside space-y-1',
              variant === 'compact' ? 'mb-1' : variant === 'summary' ? 'mb-2' : 'mb-3'
            )}>
              {children}
            </ul>
          ),
          ol: ({ children }) => (
            <ol className={cn(
              'list-decimal list-inside space-y-1',
              variant === 'compact' ? 'mb-1' : variant === 'summary' ? 'mb-2' : 'mb-3'
            )}>
              {children}
            </ol>
          ),
          li: ({ children }) => (
            <li className="text-slate-300 break-words">
              {children}
            </li>
          ),
          
          // Emphasis
          strong: ({ children }) => (
            <strong className={variant === 'insight' ? 'text-white font-semibold' : 'text-slate-200 font-medium'}>
              {children}
            </strong>
          ),
          em: ({ children }) => (
            <em className="text-slate-200 italic">
              {children}
            </em>
          ),
          
          // Code
          code: ({ children }) => (
            <code className="bg-slate-800 text-slate-300 px-1 py-0.5 rounded text-xs font-mono">
              {children}
            </code>
          ),
          pre: ({ children }) => (
            <pre className="bg-slate-800 text-slate-300 p-3 rounded-md overflow-x-auto text-xs font-mono mb-3">
              {children}
            </pre>
          ),
          
          // Blockquotes
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-blue-500 pl-4 italic text-slate-300 mb-3">
              {children}
            </blockquote>
          ),
          
          // Links
          a: ({ href, children }) => (
            <a 
              href={href} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-300 underline break-words"
            >
              {children}
            </a>
          ),
          
          // Tables
          table: ({ children }) => (
            <div className="overflow-x-auto mb-3">
              <table className="min-w-full border border-slate-600 rounded">
                {children}
              </table>
            </div>
          ),
          thead: ({ children }) => (
            <thead className="bg-slate-800">
              {children}
            </thead>
          ),
          tbody: ({ children }) => (
            <tbody className="bg-slate-900">
              {children}
            </tbody>
          ),
          tr: ({ children }) => (
            <tr className="border-b border-slate-600">
              {children}
            </tr>
          ),
          th: ({ children }) => (
            <th className="px-3 py-2 text-left text-xs font-medium text-slate-300 border-r border-slate-600 last:border-r-0">
              {children}
            </th>
          ),
          td: ({ children }) => (
            <td className="px-3 py-2 text-xs text-slate-300 border-r border-slate-600 last:border-r-0">
              {children}
            </td>
          ),
          
          // Horizontal rule
          hr: () => (
            <hr className="border-slate-600 my-4" />
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}
