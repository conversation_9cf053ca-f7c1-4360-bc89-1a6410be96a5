'use client'

import { useState } from 'react'
import { signIn, useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function TestLoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [result, setResult] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const { data: session, status } = useSession()

  const handleTestLogin = async () => {
    setIsLoading(true)
    setResult(null)

    try {
      console.log('🔐 Testing login with:', email)
      
      const loginResult = await signIn('credentials', {
        email,
        password,
        redirect: false,
      })

      console.log('📊 Login result:', loginResult)
      setResult(loginResult)
      
    } catch (error) {
      console.error('❌ Login error:', error)
      setResult({ error: error.message })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-slate-900 p-8">
      <div className="max-w-2xl mx-auto space-y-6">
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">🧪 Authentication Test Page</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-slate-300 block mb-2">Email:</label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            
            <div>
              <label className="text-slate-300 block mb-2">Password:</label>
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter your password"
                className="bg-slate-700 border-slate-600 text-white"
              />
            </div>
            
            <Button 
              onClick={handleTestLogin}
              disabled={isLoading || !email || !password}
              className="w-full"
            >
              {isLoading ? 'Testing Login...' : 'Test Login'}
            </Button>
          </CardContent>
        </Card>

        {/* Session Status */}
        <Card className="bg-slate-800 border-slate-700">
          <CardHeader>
            <CardTitle className="text-white">📊 Session Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-slate-300">
              <p><strong>Status:</strong> {status}</p>
              {session && (
                <div className="mt-2">
                  <p><strong>User:</strong> {session.user?.email}</p>
                  <p><strong>Name:</strong> {session.user?.name}</p>
                  <p><strong>ID:</strong> {session.user?.id}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Login Result */}
        {result && (
          <Card className="bg-slate-800 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">🔍 Login Result</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-slate-300 text-sm overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card className="bg-blue-900/20 border-blue-700">
          <CardHeader>
            <CardTitle className="text-blue-300">📝 Instructions</CardTitle>
          </CardHeader>
          <CardContent className="text-blue-200 space-y-2">
            <p>1. Enter the email and password you used during signup</p>
            <p>2. Click "Test Login" to see the raw NextAuth response</p>
            <p>3. Check the browser console for detailed logs</p>
            <p>4. If login is successful, you should see session data above</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
