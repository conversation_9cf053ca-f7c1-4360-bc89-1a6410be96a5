import { NextRequest, NextResponse } from 'next/server'
import { signupSchema, createUser } from '@/lib/auth'
import { prisma } from '@/lib/db'
import { z } from 'zod'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate input
    const validatedData = signupSchema.parse(body)

    // Ensure users table exists
    try {
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS "users" (
          "id" TEXT NOT NULL,
          "name" TEXT,
          "email" TEXT NOT NULL,
          "emailVerified" TIMESTAMP(3),
          "image" TEXT,
          "password" TEXT,
          "role" TEXT NOT NULL DEFAULT 'user',
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          "firstName" TEXT,
          "lastName" TEXT,
          "phone" TEXT,
          "dateOfBirth" TIMESTAMP(3),
          "country" TEXT,
          "city" TEXT,
          "investmentExperience" TEXT,
          "riskTolerance" TEXT,
          "investmentGoals" TEXT[],
          "annualIncome" TEXT,
          "preferredCurrency" TEXT NOT NULL DEFAULT 'USD',
          "preferredRegion" TEXT NOT NULL DEFAULT 'global',
          "notificationsEnabled" BOOLEAN NOT NULL DEFAULT true,
          "darkMode" BOOLEAN NOT NULL DEFAULT true,
          "subscriptionTier" TEXT NOT NULL DEFAULT 'free',
          "subscriptionExpiry" TIMESTAMP(3),
          "apiCallsUsed" INTEGER NOT NULL DEFAULT 0,
          "apiCallsLimit" INTEGER NOT NULL DEFAULT 100,
          CONSTRAINT "users_pkey" PRIMARY KEY ("id")
        )
      `

      await prisma.$executeRaw`
        CREATE UNIQUE INDEX IF NOT EXISTS "users_email_key" ON "users"("email")
      `

      // Create user_activities table
      await prisma.$executeRaw`
        CREATE TABLE IF NOT EXISTS "user_activities" (
          "id" TEXT NOT NULL,
          "userId" TEXT NOT NULL,
          "action" TEXT NOT NULL,
          "details" JSONB,
          "ipAddress" TEXT,
          "userAgent" TEXT,
          "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
          CONSTRAINT "user_activities_pkey" PRIMARY KEY ("id")
        )
      `

      // Create foreign key constraint
      await prisma.$executeRaw`
        ALTER TABLE "user_activities"
        ADD CONSTRAINT IF NOT EXISTS "user_activities_userId_fkey"
        FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE
      `

    } catch (tableError) {
      console.log('Table creation note:', tableError.message)
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: {
        email: validatedData.email
      }
    })
    
    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 400 }
      )
    }
    
    // Create user
    const user = await createUser({
      firstName: validatedData.firstName,
      lastName: validatedData.lastName,
      email: validatedData.email,
      password: validatedData.password,
    })
    
    // Return success (without password)
    const { password, ...userWithoutPassword } = user
    
    return NextResponse.json({
      message: 'User created successfully',
      user: userWithoutPassword
    }, { status: 201 })
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      )
    }
    
    console.error('Signup error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
