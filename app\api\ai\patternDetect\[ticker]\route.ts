import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || process.env.NEXT_PUBLIC_GEMINI_API_KEY || '');

interface PatternAnalysis {
  patterns: DetectedPattern[];
  seasonality: SeasonalPattern[];
  keyEvents: HistoricalEvent[];
  priceTargets: PriceTarget[];
  riskFactors: string[];
  summary: string;
}

interface DetectedPattern {
  name: string;
  type: 'bullish' | 'bearish' | 'neutral';
  confidence: number;
  timeframe: string;
  description: string;
  historicalAccuracy: number;
  nextExpectedMove: string;
}

interface SeasonalPattern {
  period: string;
  pattern: string;
  strength: number;
  description: string;
}

interface HistoricalEvent {
  date: string;
  event: string;
  priceImpact: number;
  description: string;
}

interface PriceTarget {
  target: number;
  probability: number;
  timeframe: string;
  reasoning: string;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { ticker: string } }
) {
  try {
    const ticker = params.ticker.toUpperCase();
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '1Y';
    
    // Fetch historical data for pattern analysis
    const historicalData = await fetchHistoricalDataForAnalysis(ticker, timeframe);
    
    // Generate AI-powered pattern analysis
    const analysis = await generatePatternAnalysis(ticker, historicalData, timeframe);
    
    return NextResponse.json(analysis);
  } catch (error) {
    console.error('Error in pattern detection:', error);
    
    // Return mock analysis as fallback
    const mockAnalysis = generateMockPatternAnalysis(params.ticker.toUpperCase());
    return NextResponse.json(mockAnalysis);
  }
}

async function fetchHistoricalDataForAnalysis(ticker: string, timeframe: string) {
  try {
    // This would typically fetch from our historical API
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3002'}/api/historical/${ticker}?timeframe=${timeframe}`);
    
    if (response.ok) {
      return await response.json();
    }
    
    throw new Error('Failed to fetch historical data');
  } catch (error) {
    console.error('Historical data fetch error:', error);
    return null;
  }
}

async function generatePatternAnalysis(ticker: string, historicalData: any, timeframe: string): Promise<PatternAnalysis> {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    
    // Prepare data summary for AI analysis
    const dataSummary = historicalData ? {
      dataPoints: historicalData.data?.length || 0,
      priceRange: historicalData.keyLevels || {},
      detectedPatterns: historicalData.patterns || [],
      timeframe: timeframe
    } : null;
    
    const prompt = `
    As a professional technical analyst, analyze the historical price patterns for ${ticker} over the ${timeframe} timeframe and provide a comprehensive pattern analysis.

    ${dataSummary ? `
    Historical Data Summary:
    - Data Points: ${dataSummary.dataPoints}
    - Support Levels: ${JSON.stringify(dataSummary.priceRange.support || [])}
    - Resistance Levels: ${JSON.stringify(dataSummary.priceRange.resistance || [])}
    - Detected Patterns: ${JSON.stringify(dataSummary.detectedPatterns)}
    ` : 'Note: Limited historical data available, provide analysis based on general market patterns.'}

    Please provide a detailed analysis including:

    1. **Detected Patterns** (3-5 patterns):
       - Pattern name (e.g., "Head and Shoulders", "Ascending Triangle")
       - Type: bullish/bearish/neutral
       - Confidence level (0-100)
       - Timeframe for completion
       - Description of the pattern
       - Historical accuracy percentage
       - Next expected move

    2. **Seasonal Patterns** (2-3 patterns):
       - Period (e.g., "Q4", "Summer months")
       - Pattern description
       - Strength (0-100)
       - Explanation

    3. **Key Historical Events** (3-4 events):
       - Date
       - Event description
       - Price impact percentage
       - Context

    4. **Price Targets** (2-3 targets):
       - Target price
       - Probability (0-100)
       - Timeframe
       - Technical reasoning

    5. **Risk Factors** (3-4 factors as array of strings)

    6. **Summary** (2-3 sentences about overall pattern outlook)

    Return the response as a valid JSON object matching this structure:
    {
      "patterns": [{"name": "", "type": "", "confidence": 0, "timeframe": "", "description": "", "historicalAccuracy": 0, "nextExpectedMove": ""}],
      "seasonality": [{"period": "", "pattern": "", "strength": 0, "description": ""}],
      "keyEvents": [{"date": "", "event": "", "priceImpact": 0, "description": ""}],
      "priceTargets": [{"target": 0, "probability": 0, "timeframe": "", "reasoning": ""}],
      "riskFactors": [""],
      "summary": ""
    }
    `;
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    try {
      const analysis = JSON.parse(text);
      return validateAndCleanAnalysis(analysis);
    } catch (parseError) {
      console.error('Failed to parse AI response:', parseError);
      return generateMockPatternAnalysis(ticker);
    }
    
  } catch (error) {
    console.error('Gemini AI pattern analysis error:', error);
    return generateMockPatternAnalysis(ticker);
  }
}

function validateAndCleanAnalysis(analysis: any): PatternAnalysis {
  return {
    patterns: (analysis.patterns || []).map((p: any) => ({
      name: p.name || 'Unknown Pattern',
      type: ['bullish', 'bearish', 'neutral'].includes(p.type) ? p.type : 'neutral',
      confidence: Math.min(Math.max(p.confidence || 50, 0), 100),
      timeframe: p.timeframe || 'Medium-term',
      description: p.description || 'Pattern analysis pending',
      historicalAccuracy: Math.min(Math.max(p.historicalAccuracy || 60, 0), 100),
      nextExpectedMove: p.nextExpectedMove || 'Monitor for breakout'
    })),
    seasonality: (analysis.seasonality || []).map((s: any) => ({
      period: s.period || 'Annual',
      pattern: s.pattern || 'Neutral',
      strength: Math.min(Math.max(s.strength || 50, 0), 100),
      description: s.description || 'Seasonal pattern analysis'
    })),
    keyEvents: (analysis.keyEvents || []).map((e: any) => ({
      date: e.date || new Date().toISOString().split('T')[0],
      event: e.event || 'Market Event',
      priceImpact: e.priceImpact || 0,
      description: e.description || 'Historical market event'
    })),
    priceTargets: (analysis.priceTargets || []).map((t: any) => ({
      target: t.target || 100,
      probability: Math.min(Math.max(t.probability || 50, 0), 100),
      timeframe: t.timeframe || '3-6 months',
      reasoning: t.reasoning || 'Technical analysis based'
    })),
    riskFactors: Array.isArray(analysis.riskFactors) ? analysis.riskFactors : [
      'Market volatility',
      'Economic uncertainty',
      'Sector-specific risks'
    ],
    summary: analysis.summary || 'Pattern analysis indicates mixed signals with moderate confidence levels.'
  };
}

function generateMockPatternAnalysis(ticker: string): PatternAnalysis {
  return {
    patterns: [
      {
        name: 'Ascending Triangle',
        type: 'bullish',
        confidence: 78,
        timeframe: '2-4 weeks',
        description: `${ticker} is forming an ascending triangle pattern with higher lows and consistent resistance around current levels. This typically indicates accumulation and potential upward breakout.`,
        historicalAccuracy: 72,
        nextExpectedMove: 'Breakout above resistance with increased volume'
      },
      {
        name: 'Support Zone Test',
        type: 'neutral',
        confidence: 65,
        timeframe: '1-2 weeks',
        description: 'Stock is testing a key support zone that has held multiple times. A bounce here would confirm support strength.',
        historicalAccuracy: 68,
        nextExpectedMove: 'Watch for bounce or breakdown at support'
      },
      {
        name: 'Volume Divergence',
        type: 'bearish',
        confidence: 55,
        timeframe: 'Short-term',
        description: 'Recent price advances have been accompanied by declining volume, suggesting weakening momentum.',
        historicalAccuracy: 63,
        nextExpectedMove: 'Potential pullback if volume doesn\'t improve'
      }
    ],
    seasonality: [
      {
        period: 'Q4 (Oct-Dec)',
        pattern: 'Historically Strong',
        strength: 75,
        description: `${ticker} typically performs well in Q4 due to seasonal factors and year-end positioning by institutional investors.`
      },
      {
        period: 'Summer (Jun-Aug)',
        pattern: 'Consolidation',
        strength: 60,
        description: 'Summer months often see reduced volatility and sideways trading as volume decreases.'
      }
    ],
    keyEvents: [
      {
        date: '2024-01-15',
        event: 'Q4 Earnings Beat',
        priceImpact: 8.5,
        description: 'Strong quarterly results led to significant price appreciation and analyst upgrades.'
      },
      {
        date: '2023-10-20',
        event: 'Product Launch',
        priceImpact: 5.2,
        description: 'New product announcement generated positive investor sentiment and increased trading volume.'
      },
      {
        date: '2023-08-10',
        event: 'Market Correction',
        priceImpact: -12.3,
        description: 'Broader market selloff affected stock despite strong fundamentals.'
      }
    ],
    priceTargets: [
      {
        target: 165.50,
        probability: 70,
        timeframe: '1-3 months',
        reasoning: 'Breakout above ascending triangle resistance projects to this level based on pattern height.'
      },
      {
        target: 180.00,
        probability: 45,
        timeframe: '3-6 months',
        reasoning: 'Long-term resistance level and psychological round number target.'
      }
    ],
    riskFactors: [
      'Broader market volatility could override individual stock patterns',
      'Earnings disappointment could invalidate bullish technical setup',
      'Sector rotation away from growth stocks',
      'Rising interest rates affecting valuation multiples'
    ],
    summary: `${ticker} shows a predominantly bullish technical setup with the ascending triangle pattern providing the strongest signal. However, volume trends and broader market conditions warrant careful monitoring for confirmation of the expected breakout.`
  };
}
