"use client";

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';

interface ChartData {
  date: string;
  price: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
}

interface StockChartProps {
  ticker: string;
  timeframe: string;
  height?: number;
}

export default function StockChart({ ticker, timeframe, height = 320 }: StockChartProps) {
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [chartType, setChartType] = useState<'price' | 'volume'>('price');

  useEffect(() => {
    generateMockChartData();
  }, [ticker, timeframe]);

  const generateMockChartData = () => {
    setIsLoading(true);
    
    // Generate realistic mock data based on timeframe
    const dataPoints = getDataPointsForTimeframe(timeframe);
    const data: ChartData[] = [];
    
    let basePrice = 100 + Math.random() * 100; // Start between $100-$200
    const today = new Date();
    
    for (let i = dataPoints; i >= 0; i--) {
      const date = new Date(today);
      
      // Adjust date based on timeframe
      if (timeframe === '1D') {
        date.setHours(date.getHours() - i);
      } else {
        date.setDate(date.getDate() - i);
      }
      
      // Generate realistic price movement
      const volatility = 0.02; // 2% daily volatility
      const change = (Math.random() - 0.5) * volatility;
      const newPrice = basePrice * (1 + change);
      
      const high = newPrice * (1 + Math.random() * 0.01);
      const low = newPrice * (1 - Math.random() * 0.01);
      const volume = Math.floor(Math.random() * 10000000) + 1000000;
      
      data.push({
        date: timeframe === '1D' 
          ? date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })
          : date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        price: parseFloat(newPrice.toFixed(2)),
        volume: volume,
        high: parseFloat(high.toFixed(2)),
        low: parseFloat(low.toFixed(2)),
        open: parseFloat(basePrice.toFixed(2)),
        close: parseFloat(newPrice.toFixed(2))
      });
      
      basePrice = newPrice;
    }
    
    setChartData(data);
    setIsLoading(false);
  };

  const getDataPointsForTimeframe = (timeframe: string): number => {
    switch (timeframe) {
      case '1D': return 24; // 24 hours
      case '5D': return 5;   // 5 days
      case '1M': return 30;  // 30 days
      case '3M': return 90;  // 90 days
      case '1Y': return 252; // 252 trading days
      default: return 30;
    }
  };

  const formatTooltipValue = (value: number, name: string) => {
    if (name === 'price') {
      return [`$${value.toFixed(2)}`, 'Price'];
    } else if (name === 'volume') {
      return [formatVolume(value), 'Volume'];
    }
    return [value, name];
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1e9) return `${(volume / 1e9).toFixed(1)}B`;
    if (volume >= 1e6) return `${(volume / 1e6).toFixed(1)}M`;
    if (volume >= 1e3) return `${(volume / 1e3).toFixed(1)}K`;
    return volume.toString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center" style={{ height }}>
        <div className="text-center space-y-2">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto"></div>
          <p className="text-slate-400 text-sm">Loading chart data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Chart Type Toggle */}
      <div className="flex gap-2">
        <button
          onClick={() => setChartType('price')}
          className={`px-3 py-1 rounded text-xs ${
            chartType === 'price' 
              ? 'bg-blue-600 text-white' 
              : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
          }`}
        >
          Price
        </button>
        <button
          onClick={() => setChartType('volume')}
          className={`px-3 py-1 rounded text-xs ${
            chartType === 'volume' 
              ? 'bg-blue-600 text-white' 
              : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
          }`}
        >
          Volume
        </button>
      </div>

      {/* Chart */}
      <ResponsiveContainer width="100%" height={height}>
        {chartType === 'price' ? (
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis 
              dataKey="date" 
              stroke="#9CA3AF"
              fontSize={12}
              tick={{ fill: '#9CA3AF' }}
            />
            <YAxis 
              stroke="#9CA3AF"
              fontSize={12}
              tick={{ fill: '#9CA3AF' }}
              domain={['dataMin - 1', 'dataMax + 1']}
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: '#1F2937',
                border: '1px solid #374151',
                borderRadius: '8px',
                color: '#F9FAFB'
              }}
              formatter={formatTooltipValue}
              labelStyle={{ color: '#9CA3AF' }}
            />
            <Line 
              type="monotone" 
              dataKey="price" 
              stroke="#3B82F6" 
              strokeWidth={2}
              dot={false}
              activeDot={{ r: 4, stroke: '#3B82F6', strokeWidth: 2, fill: '#1F2937' }}
            />
          </LineChart>
        ) : (
          <BarChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis 
              dataKey="date" 
              stroke="#9CA3AF"
              fontSize={12}
              tick={{ fill: '#9CA3AF' }}
            />
            <YAxis 
              stroke="#9CA3AF"
              fontSize={12}
              tick={{ fill: '#9CA3AF' }}
            />
            <Tooltip 
              contentStyle={{
                backgroundColor: '#1F2937',
                border: '1px solid #374151',
                borderRadius: '8px',
                color: '#F9FAFB'
              }}
              formatter={formatTooltipValue}
              labelStyle={{ color: '#9CA3AF' }}
            />
            <Bar 
              dataKey="volume" 
              fill="#8B5CF6"
              opacity={0.8}
            />
          </BarChart>
        )}
      </ResponsiveContainer>

      {/* Chart Info */}
      <div className="flex justify-between text-xs text-slate-400">
        <span>{ticker} • {timeframe}</span>
        <span>{chartData.length} data points</span>
      </div>
    </div>
  );
}
