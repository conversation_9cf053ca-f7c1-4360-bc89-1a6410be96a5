const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function checkUsers() {
  try {
    console.log('🔍 Checking users in database...')
    
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        password: true,
        createdAt: true
      }
    })
    
    console.log(`\n📊 Found ${users.length} users:`)
    
    for (const user of users) {
      console.log(`\n👤 User: ${user.firstName} ${user.lastName}`)
      console.log(`   📧 Email: ${user.email}`)
      console.log(`   🆔 ID: ${user.id}`)
      console.log(`   📅 Created: ${user.createdAt}`)
      console.log(`   🔐 Has Password: ${user.password ? 'Yes' : 'No'}`)
      
      if (user.password) {
        // Test password verification with a common test password
        const testPasswords = ['password123', 'Password123', 'SecurePass123', 'Test123']
        
        for (const testPass of testPasswords) {
          try {
            const isValid = await bcrypt.compare(testPass, user.password)
            if (isValid) {
              console.log(`   ✅ Password matches: ${testPass}`)
              break
            }
          } catch (error) {
            console.log(`   ❌ Password check error: ${error.message}`)
          }
        }
      }
    }
    
    console.log('\n✅ User check completed!')
    
  } catch (error) {
    console.error('❌ Error checking users:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkUsers()
