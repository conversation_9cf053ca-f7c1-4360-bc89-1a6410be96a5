// Real Market Data Service
// Integrates with multiple APIs for comprehensive market data

interface StockData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  sector?: string;
}

interface SectorStocks {
  [sector: string]: string[];
}

// Comprehensive stock lists for analysis
const INDIAN_STOCKS: SectorStocks = {
  'Information Technology': [
    'TCS.NS', 'INFY.NS', 'WIPRO.NS', 'HCLTECH.NS', 'TECHM.NS',
    'LTI.NS', 'MINDTREE.NS', 'MPHASIS.NS', 'COFORGE.NS', 'LTTS.NS',
    'PERSISTENT.NS', 'CYIENT.NS', 'ROLTA.NS', 'NIIT.NS', 'KPIT.NS',
    'ZENSAR.NS', 'SONATA.NS', 'HEXAWARE.NS', 'INTELLECT.NS', 'POLYCAB.NS'
  ],
  'Banking & Financial Services': [
    'HDFCBANK.NS', 'ICICIBANK.NS', 'SBIN.NS', 'KOTAKBANK.NS', 'AXISBANK.NS',
    'INDUSINDBK.NS', 'FEDERALBNK.NS', 'BANDHANBNK.NS', 'RBLBANK.NS', 'YESBANK.NS',
    'IDFCFIRSTB.NS', 'PNB.NS', 'BANKBARODA.NS', 'CANBK.NS', 'UNIONBANK.NS',
    'INDIANB.NS', 'CENTRALBK.NS', 'IOB.NS', 'MAHABANK.NS', 'JKBANK.NS'
  ],
  'Pharmaceuticals & Healthcare': [
    'SUNPHARMA.NS', 'DRREDDY.NS', 'CIPLA.NS', 'DIVISLAB.NS', 'BIOCON.NS',
    'CADILAHC.NS', 'LUPIN.NS', 'AUROPHARMA.NS', 'TORNTPHARM.NS', 'GLENMARK.NS',
    'ALKEM.NS', 'ABBOTINDIA.NS', 'PFIZER.NS', 'GSK.NS', 'NOVARTIS.NS',
    'SANOFI.NS', 'ERIS.NS', 'LALPATHLAB.NS', 'METROPOLIS.NS', 'THYROCARE.NS'
  ],
  'Automotive & Auto Components': [
    'MARUTI.NS', 'TATAMOTORS.NS', 'M&M.NS', 'BAJAJ-AUTO.NS', 'HEROMOTOCO.NS',
    'TVSMOTORS.NS', 'EICHERMOT.NS', 'ASHOKLEY.NS', 'TVSMOTOR.NS', 'BALKRISIND.NS',
    'MRF.NS', 'APOLLOTYRE.NS', 'CEAT.NS', 'JK.NS', 'MOTHERSUMI.NS',
    'BOSCHLTD.NS', 'BHARATFORG.NS', 'EXIDEIND.NS', 'AMARA.NS', 'FORCE.NS'
  ],
  'FMCG & Consumer Goods': [
    'HINDUNILVR.NS', 'ITC.NS', 'NESTLEIND.NS', 'BRITANNIA.NS', 'DABUR.NS',
    'GODREJCP.NS', 'MARICO.NS', 'COLPAL.NS', 'PGHH.NS', 'UBL.NS',
    'TATACONSUM.NS', 'EMAMILTD.NS', 'JYOTHYLAB.NS', 'RADICO.NS', 'VBL.NS',
    'CCL.NS', 'GILLETTE.NS', 'HONASA.NS', 'PATANJALI.NS', 'BIKAJI.NS'
  ],
  'Energy & Oil': [
    'RELIANCE.NS', 'ONGC.NS', 'IOC.NS', 'BPCL.NS', 'HPCL.NS',
    'GAIL.NS', 'NTPC.NS', 'POWERGRID.NS', 'COALINDIA.NS', 'ADANIGREEN.NS',
    'TATAPOWER.NS', 'ADANIPOWER.NS', 'JSPL.NS', 'VEDL.NS', 'HINDALCO.NS',
    'SAIL.NS', 'NMDC.NS', 'MOIL.NS', 'GMRINFRA.NS', 'ADANIPORTS.NS'
  ],
  'Metals & Mining': [
    'TATASTEEL.NS', 'JSWSTEEL.NS', 'HINDALCO.NS', 'VEDL.NS', 'SAIL.NS',
    'JINDALSTEL.NS', 'NMDC.NS', 'MOIL.NS', 'COALINDIA.NS', 'NATIONALUM.NS',
    'HINDZINC.NS', 'WELCORP.NS', 'RATNAMANI.NS', 'APL.NS', 'WELSPUNIND.NS',
    'JSWENERGY.NS', 'ADANIENT.NS', 'ADANIPORTS.NS', 'GMRINFRA.NS', 'IRCON.NS'
  ],
  'Telecommunications': [
    'BHARTIARTL.NS', 'RJIO.NS', 'IDEA.NS', 'MTNL.NS', 'BSNL.NS',
    'TTML.NS', 'RCOM.NS', 'GTPL.NS', 'HFCL.NS', 'STERLITE.NS',
    'RAILTEL.NS', 'ROUTE.NS', 'OPTIEMUS.NS', 'TEJAS.NS', 'AKSH.NS',
    'VINDHYATEL.NS', 'NELCO.NS', 'ZENTEC.NS', 'VIPIND.NS', 'WEBELSOLAR.NS'
  ],
  'Real Estate & Construction': [
    'DLF.NS', 'GODREJPROP.NS', 'OBEROIRLTY.NS', 'PRESTIGE.NS', 'BRIGADE.NS',
    'SOBHA.NS', 'PHOENIXLTD.NS', 'MAHLIFE.NS', 'SUNTECK.NS', 'KOLTE.NS',
    'L&TFH.NS', 'IBREALEST.NS', 'MAHINDCIE.NS', 'ASHOKA.NS', 'HCC.NS',
    'JKCEMENT.NS', 'SHREECEM.NS', 'ULTRACEMCO.NS', 'ACC.NS', 'AMBUJACEMENT.NS'
  ]
};

const GLOBAL_STOCKS: SectorStocks = {
  'Technology & Software': [
    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'TSLA', 'NVDA', 'NFLX',
    'ADBE', 'CRM', 'ORCL', 'IBM', 'INTC', 'AMD', 'QCOM', 'AVGO',
    'TXN', 'AMAT', 'LRCX', 'KLAC'
  ],
  'Healthcare & Pharmaceuticals': [
    'JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'ABT', 'DHR',
    'BMY', 'AMGN', 'GILD', 'MDT', 'ISRG', 'VRTX', 'REGN', 'BIIB',
    'ILMN', 'MRNA', 'ZTS', 'CVS'
  ],
  'Financial Services & Banking': [
    'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP', 'BLK',
    'SCHW', 'USB', 'PNC', 'TFC', 'COF', 'CME', 'ICE', 'SPGI',
    'MCO', 'AON', 'MMC', 'AJG'
  ],
  'Consumer Discretionary & Retail': [
    'AMZN', 'TSLA', 'HD', 'MCD', 'NKE', 'SBUX', 'TJX', 'LOW',
    'BKNG', 'ABNB', 'GM', 'F', 'NCLH', 'CCL', 'RCL', 'MAR',
    'HLT', 'MGM', 'LVS', 'WYNN'
  ],
  'Energy & Utilities': [
    'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'PXD', 'KMI', 'OKE',
    'WMB', 'MPC', 'VLO', 'PSX', 'HES', 'DVN', 'FANG', 'APA',
    'OXY', 'HAL', 'BKR', 'NOV'
  ],
  'Consumer Staples & Food': [
    'PG', 'KO', 'PEP', 'WMT', 'COST', 'CL', 'KMB', 'GIS',
    'K', 'HSY', 'MDLZ', 'CPB', 'SJM', 'CAG', 'HRL', 'TSN',
    'TYSON', 'ADM', 'BG', 'INGR'
  ],
  'Industrial & Manufacturing': [
    'BA', 'CAT', 'GE', 'MMM', 'HON', 'UPS', 'FDX', 'LMT',
    'RTX', 'NOC', 'GD', 'DE', 'EMR', 'ETN', 'ITW', 'PH',
    'ROK', 'DOV', 'XYL', 'FLS'
  ],
  'Materials & Chemicals': [
    'LIN', 'APD', 'ECL', 'SHW', 'DD', 'DOW', 'PPG', 'NEM',
    'FCX', 'GOLD', 'AA', 'X', 'CLF', 'NUE', 'STLD', 'RS',
    'VMC', 'MLM', 'CRH', 'FMC'
  ],
  'Real Estate & REITs': [
    'AMT', 'PLD', 'CCI', 'EQIX', 'PSA', 'EXR', 'AVB', 'EQR',
    'DLR', 'WELL', 'VTR', 'ESS', 'MAA', 'UDR', 'CPT', 'AIV',
    'BXP', 'VNO', 'KIM', 'REG'
  ]
};

const CRYPTO_ASSETS: SectorStocks = {
  'Layer 1 Blockchains': [
    'BTC-USD', 'ETH-USD', 'BNB-USD', 'ADA-USD', 'SOL-USD', 'DOT-USD',
    'AVAX-USD', 'ATOM-USD', 'ALGO-USD', 'NEAR-USD', 'FTM-USD', 'ONE-USD',
    'HBAR-USD', 'ICP-USD', 'FLOW-USD', 'EGLD-USD', 'KLAY-USD', 'ROSE-USD',
    'CELO-USD', 'MINA-USD'
  ],
  'DeFi & DEX Tokens': [
    'UNI-USD', 'AAVE-USD', 'COMP-USD', 'MKR-USD', 'SNX-USD', 'CRV-USD',
    'SUSHI-USD', '1INCH-USD', 'YFI-USD', 'BAL-USD', 'LRC-USD', 'ZRX-USD',
    'KNC-USD', 'REN-USD', 'BAND-USD', 'ALPHA-USD', 'CREAM-USD', 'BADGER-USD',
    'PICKLE-USD', 'ROOK-USD'
  ],
  'Layer 2 & Scaling Solutions': [
    'MATIC-USD', 'LRC-USD', 'OMG-USD', 'SKALE-USD', 'CELR-USD', 'CTSI-USD',
    'METIS-USD', 'BOBA-USD', 'IMX-USD', 'STRK-USD', 'ARB-USD', 'OP-USD',
    'MUTE-USD', 'DYDX-USD', 'GMX-USD', 'GNS-USD', 'PERP-USD', 'GAINS-USD',
    'CAP-USD', 'KWENTA-USD'
  ],
  'Smart Contract Platforms': [
    'ETH-USD', 'ADA-USD', 'SOL-USD', 'DOT-USD', 'AVAX-USD', 'LUNA-USD',
    'FTM-USD', 'ONE-USD', 'HBAR-USD', 'ALGO-USD', 'EGLD-USD', 'NEAR-USD',
    'ICP-USD', 'FLOW-USD', 'KLAY-USD', 'CELO-USD', 'ROSE-USD', 'MINA-USD',
    'APTOS-USD', 'SUI-USD'
  ],
  'Meme & Community Tokens': [
    'DOGE-USD', 'SHIB-USD', 'FLOKI-USD', 'PEPE-USD', 'BONK-USD', 'WIF-USD',
    'MEME-USD', 'WOJAK-USD', 'TURBO-USD', 'AIDOGE-USD', 'LADYS-USD', 'PEPE2-USD',
    'BABYDOGE-USD', 'KISHU-USD', 'ELON-USD', 'AKITA-USD', 'HOGE-USD', 'SAFEMOON-USD',
    'CATGIRL-USD', 'SAITAMA-USD'
  ],
  'Infrastructure & Oracles': [
    'LINK-USD', 'DOT-USD', 'ATOM-USD', 'FIL-USD', 'GRT-USD', 'OCEAN-USD',
    'API3-USD', 'BAND-USD', 'TRB-USD', 'DIA-USD', 'FLUX-USD', 'ERG-USD',
    'SC-USD', 'STORJ-USD', 'AR-USD', 'THETA-USD', 'TFUEL-USD', 'LPT-USD',
    'RLC-USD', 'ANKR-USD'
  ],
  'Gaming & Metaverse': [
    'AXS-USD', 'SAND-USD', 'MANA-USD', 'ENJ-USD', 'GALA-USD', 'ILV-USD',
    'ALICE-USD', 'TLM-USD', 'SLP-USD', 'WAXP-USD', 'CHR-USD', 'PYR-USD',
    'GHST-USD', 'REVV-USD', 'TOWER-USD', 'NFTX-USD', 'SUPER-USD', 'UFO-USD',
    'STARL-USD', 'BLOK-USD'
  ],
  'Privacy & Security': [
    'XMR-USD', 'ZEC-USD', 'DASH-USD', 'DCR-USD', 'FIRO-USD', 'BEAM-USD',
    'GRIN-USD', 'ARRR-USD', 'DERO-USD', 'OXEN-USD', 'PART-USD', 'NAV-USD',
    'PIVX-USD', 'GHOST-USD', 'XVG-USD', 'KMD-USD', 'ZEN-USD', 'BTCP-USD',
    'ANON-USD', 'BTCZ-USD'
  ]
};

class RealMarketDataService {
  private readonly TWELVE_DATA_BASE = 'https://api.twelvedata.com';
  private readonly FINNHUB_BASE = 'https://finnhub.io/api/v1';
  private readonly ALPHA_VANTAGE_BASE = 'https://www.alphavantage.co/query';
  private readonly NEWS_API_BASE = 'https://newsapi.org/v2/everything';
  private readonly FREE_FOREX_BASE = 'https://api.exchangerate-api.com/v4/latest';

  // API Keys from environment
  private readonly TWELVE_DATA_KEY = process.env.TWELVE_DATA_API_KEY || '';
  private readonly FINNHUB_KEY = process.env.FINNHUB_API_KEY || '';
  private readonly ALPHA_VANTAGE_KEY = process.env.ALPHA_VANTAGE_API_KEY || '';
  private readonly NEWS_API_KEY = process.env.NEWS_API_KEY || '';

  async fetchStockData(symbol: string): Promise<StockData | null> {
    try {
      // Use the same approach as stock analysis
      const market = this.detectMarket(symbol);

      if (market === 'IN') {
        // Try Indian market APIs
        let stockData = await this.fetchIndianStock(symbol);
        if (!stockData) {
          stockData = await this.fetchFromYahooFinance(symbol, market);
        }
        return stockData;
      } else {
        // Try US market APIs in order
        let stockData = await this.fetchFromAlphaVantage(symbol);
        if (!stockData) {
          stockData = await this.fetchFromYahooFinance(symbol, market);
        }
        if (!stockData) {
          stockData = await this.fetchFromTwelveData(symbol);
        }
        if (!stockData) {
          stockData = await this.fetchFromFinnhub(symbol);
        }
        return stockData || this.generateMockStockData(symbol);
      }
    } catch (error) {
      console.error(`Error fetching data for ${symbol}:`, error);
      return this.generateMockStockData(symbol);
    }
  }

  private detectMarket(ticker: string): 'US' | 'IN' {
    // Indian stock patterns
    if (ticker.endsWith('.NS') || ticker.endsWith('.BO') || ticker.endsWith('.BSE')) {
      return 'IN';
    }

    // Common Indian stock symbols
    const indianStocks = ['RELIANCE', 'TCS', 'INFY', 'HDFCBANK', 'ICICIBANK', 'SBIN', 'ITC', 'LT', 'BHARTIARTL', 'ASIANPAINT'];
    if (indianStocks.includes(ticker.replace(/\.(NS|BO|BSE)$/, ''))) {
      return 'IN';
    }

    return 'US';
  }

  private async fetchIndianStock(symbol: string): Promise<StockData | null> {
    try {
      // Try Yahoo Finance for Indian stocks first
      const yahooData = await this.fetchFromYahooFinance(symbol, 'IN');
      if (yahooData) return yahooData;

      // Try Twelve Data for Indian stocks
      const twelveData = await this.fetchFromTwelveData(symbol);
      if (twelveData) return twelveData;

      return null;
    } catch (error) {
      return null;
    }
  }

  private async fetchFromYahooFinance(symbol: string, market: 'US' | 'IN'): Promise<StockData | null> {
    try {
      // Ensure proper symbol format for Yahoo Finance
      let yahooSymbol = symbol;
      if (market === 'IN' && !symbol.includes('.')) {
        yahooSymbol = `${symbol}.NS`;
      }

      const response = await fetch(`https://query1.finance.yahoo.com/v8/finance/chart/${yahooSymbol}?interval=1d&range=2d`);
      const data = await response.json();

      if (!data.chart?.result?.[0]) {
        return null;
      }

      const result = data.chart.result[0];
      const meta = result.meta;
      const quotes = result.indicators.quote[0];

      if (!quotes.close || quotes.close.length < 2) {
        return null;
      }

      const currentPrice = quotes.close[quotes.close.length - 1];
      const previousPrice = quotes.close[quotes.close.length - 2];
      const volume = quotes.volume?.[quotes.volume.length - 1] || 0;

      const change = currentPrice - previousPrice;
      const changePercent = (change / previousPrice) * 100;

      return {
        symbol: symbol || 'UNKNOWN',
        name: meta.longName || meta.shortName || symbol || 'Unknown Company',
        price: Number((currentPrice || 0).toFixed(2)),
        change: Number((change || 0).toFixed(2)),
        changePercent: Number((changePercent || 0).toFixed(2)),
        volume: Math.floor(volume || 0),
        marketCap: Math.floor(meta.marketCap || 0),
        sector: meta.sector || 'Technology'
      };
    } catch (error) {
      return null;
    }
  }

  private async fetchFromTwelveData(symbol: string): Promise<StockData | null> {
    if (!this.TWELVE_DATA_KEY) return null;

    try {
      const cleanSymbol = symbol.replace('.NS', '').replace('-USD', '');
      const exchange = symbol.includes('.NS') ? 'NSE' : symbol.includes('-USD') ? 'Binance' : 'NASDAQ';

      const response = await fetch(
        `${this.TWELVE_DATA_BASE}/quote?symbol=${cleanSymbol}&exchange=${exchange}&apikey=${this.TWELVE_DATA_KEY}`
      );
      const data = await response.json();

      if (data.status === 'error' || !data.close) {
        return null;
      }

      const currentPrice = parseFloat(data.close);
      const previousClose = parseFloat(data.previous_close);
      const change = currentPrice - previousClose;
      const changePercent = (change / previousClose) * 100;

      return {
        symbol: symbol,
        name: data.name || cleanSymbol,
        price: currentPrice,
        change: change,
        changePercent: changePercent,
        volume: parseInt(data.volume) || 0,
        marketCap: data.market_cap,
        sector: data.sector
      };
    } catch (error) {
      return null;
    }
  }

  private async fetchFromFinnhub(symbol: string): Promise<StockData | null> {
    if (!this.FINNHUB_KEY) return null;

    try {
      const cleanSymbol = symbol.replace('.NS', '').replace('-USD', '');

      const [quoteResponse, profileResponse] = await Promise.all([
        fetch(`${this.FINNHUB_BASE}/quote?symbol=${cleanSymbol}&token=${this.FINNHUB_KEY}`),
        fetch(`${this.FINNHUB_BASE}/stock/profile2?symbol=${cleanSymbol}&token=${this.FINNHUB_KEY}`)
      ]);

      const quoteData = await quoteResponse.json();
      const profileData = await profileResponse.json();

      if (!quoteData.c || quoteData.c === 0) {
        return null;
      }

      const currentPrice = quoteData.c;
      const change = quoteData.d;
      const changePercent = quoteData.dp;

      return {
        symbol: symbol,
        name: profileData.name || cleanSymbol,
        price: currentPrice,
        change: change,
        changePercent: changePercent,
        volume: quoteData.volume || 0,
        marketCap: profileData.marketCapitalization,
        sector: profileData.finnhubIndustry
      };
    } catch (error) {
      return null;
    }
  }

  private async fetchFromAlphaVantage(symbol: string): Promise<StockData | null> {
    if (!this.ALPHA_VANTAGE_KEY) return null;

    try {
      const cleanSymbol = symbol.replace('.NS', '').replace('-USD', '');

      const response = await fetch(
        `${this.ALPHA_VANTAGE_BASE}?function=GLOBAL_QUOTE&symbol=${cleanSymbol}&apikey=${this.ALPHA_VANTAGE_KEY}`
      );
      const data = await response.json();

      const quote = data['Global Quote'];
      if (!quote || !quote['05. price']) {
        return null;
      }

      const currentPrice = parseFloat(quote['05. price']);
      const change = parseFloat(quote['09. change']);
      const changePercent = parseFloat(quote['10. change percent'].replace('%', ''));

      return {
        symbol: symbol,
        name: cleanSymbol,
        price: currentPrice,
        change: change,
        changePercent: changePercent,
        volume: parseInt(quote['06. volume']) || 0,
        marketCap: undefined,
        sector: undefined
      };
    } catch (error) {
      return null;
    }
  }

  private generateMockStockData(symbol: string): StockData {
    // Generate realistic mock data as fallback
    const basePrice = symbol.includes('.NS') ?
      Math.random() * 3000 + 100 :
      symbol.includes('-USD') ?
        Math.random() * 50000 + 100 :
        Math.random() * 300 + 50;

    const changePercent = (Math.random() - 0.5) * 10; // -5% to +5%
    const change = (basePrice * changePercent) / 100;
    const volume = Math.floor(Math.random() * 10000000) + 100000;

    return {
      symbol: symbol || 'UNKNOWN',
      name: this.getCompanyName(symbol) || 'Unknown Company',
      price: Number(basePrice.toFixed(2)),
      change: Number(change.toFixed(2)),
      changePercent: Number(changePercent.toFixed(2)),
      volume: Math.floor(volume),
      marketCap: Math.floor(basePrice * volume * 100),
      sector: this.getSectorForSymbol(symbol) || 'Technology'
    };
  }

  private getCompanyName(symbol: string): string {
    const names: { [key: string]: string } = {
      'TCS.NS': 'Tata Consultancy Services',
      'INFY.NS': 'Infosys Limited',
      'WIPRO.NS': 'Wipro Limited',
      'HDFCBANK.NS': 'HDFC Bank Limited',
      'ICICIBANK.NS': 'ICICI Bank Limited',
      'SBIN.NS': 'State Bank of India',
      'AAPL': 'Apple Inc.',
      'MSFT': 'Microsoft Corporation',
      'GOOGL': 'Alphabet Inc.',
      'BTC-USD': 'Bitcoin',
      'ETH-USD': 'Ethereum',
      'SOL-USD': 'Solana'
    };
    return names[symbol] || symbol.replace('.NS', '').replace('-USD', '');
  }

  private getSectorForSymbol(symbol: string): string {
    if (symbol.includes('.NS')) {
      if (['TCS.NS', 'INFY.NS', 'WIPRO.NS'].includes(symbol)) return 'Information Technology';
      if (['HDFCBANK.NS', 'ICICIBANK.NS', 'SBIN.NS'].includes(symbol)) return 'Banking';
      return 'Other';
    }
    if (symbol.includes('-USD')) return 'Cryptocurrency';
    return 'Technology';
  }

  async fetchMultipleStocks(symbols: string[]): Promise<StockData[]> {
    const results: StockData[] = [];
    const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
    const batchSize = 3; // Process 3 stocks at a time to respect rate limits

    for (let i = 0; i < symbols.length; i += batchSize) {
      const batch = symbols.slice(i, i + batchSize);
      const batchPromises = batch.map(async (symbol) => {
        try {
          const data = await this.fetchStockData(symbol);
          return data;
        } catch (error) {
          console.log(`⚠️ Failed to fetch ${symbol}:`, error);
          return null;
        }
      });

      // Wait for all requests in the batch to complete
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults.filter(data => data !== null));

      // Add delay between batches to respect rate limits
      if (i + batchSize < symbols.length) {
        await delay(300); // 300ms delay between batches
      }
    }

    return results;
  }

  async analyzeSectorPerformance(region: 'india' | 'global' | 'crypto') {
    let stocksToAnalyze: SectorStocks;

    switch (region) {
      case 'india':
        stocksToAnalyze = INDIAN_STOCKS;
        break;
      case 'global':
        stocksToAnalyze = GLOBAL_STOCKS;
        break;
      case 'crypto':
        stocksToAnalyze = CRYPTO_ASSETS;
        break;
      default:
        stocksToAnalyze = INDIAN_STOCKS;
    }

    const sectorAnalysis = [];
    console.log(`🔍 Analyzing ${Object.keys(stocksToAnalyze).length} sectors for ${region}...`);

    for (const [sectorName, symbols] of Object.entries(stocksToAnalyze)) {
      console.log(`📊 Processing sector: ${sectorName} with ${symbols.length} stocks`);

      // Take first 20 stocks from each sector for comprehensive momentum analysis
      const sampleSymbols = symbols.slice(0, 20);
      const stockData = await this.fetchMultipleStocks(sampleSymbols);

      console.log(`✅ Fetched ${stockData.length}/${sampleSymbols.length} stocks for ${sectorName}`);

      if (stockData.length === 0) {
        // Generate mock data for the sector if no real data available
        const mockData = this.generateMockSectorData(sectorName, region);
        sectorAnalysis.push(mockData);
        continue;
      }

      // Calculate sector performance (weighted by market cap if available)
      const totalMarketCap = stockData.reduce((sum, stock) => sum + (stock.marketCap || 1000000), 0);
      const weightedPerformance = stockData.reduce((sum, stock) => {
        const weight = (stock.marketCap || 1000000) / totalMarketCap;
        return sum + (stock.changePercent * weight);
      }, 0);

      // Get top 5 momentum stocks with live prices
      const topStocks = stockData
        .sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent)) // Sort by momentum (absolute change)
        .slice(0, 5)
        .map(stock => ({
          symbol: stock.symbol.replace('.NS', '').replace('-USD', ''),
          name: stock.name,
          performance: stock.changePercent,
          currentPrice: stock.currentPrice, // Live current price
          volume: stock.volume,
          momentum: Math.abs(stock.changePercent), // Momentum indicator
          signal: this.determineSignal(stock.changePercent, stock.volume) as 'opportunity' | 'caution' | 'neutral'
        }));

      // Calculate momentum score for the sector (based on top 20 stocks)
      const momentumScore = stockData.length > 0 ?
        stockData.reduce((sum, stock) => sum + Math.abs(stock.changePercent), 0) / stockData.length : 0;

      sectorAnalysis.push({
        sector: sectorName,
        performance: weightedPerformance,
        momentumScore: momentumScore, // Overall sector momentum percentage
        topStocks: topStocks,
        totalStocksAnalyzed: stockData.length,
        outlook: this.generateSectorOutlook(sectorName, weightedPerformance, stockData.length, momentumScore)
      });
    }

    console.log(`🎯 Completed analysis for ${sectorAnalysis.length} sectors`);
    return sectorAnalysis;
  }

  private generateMockSectorData(sectorName: string, region: string) {
    const performance = (Math.random() - 0.5) * 8; // -4% to +4%

    return {
      sector: sectorName,
      performance: performance,
      topStocks: [
        {
          symbol: `STOCK1`,
          name: `${sectorName} Leader 1`,
          performance: performance + Math.random() * 2,
          volume: Math.floor(Math.random() * 5000000) + 1000000,
          signal: 'neutral' as const
        },
        {
          symbol: `STOCK2`,
          name: `${sectorName} Leader 2`,
          performance: performance + Math.random() * 1.5,
          volume: Math.floor(Math.random() * 3000000) + 500000,
          signal: 'opportunity' as const
        },
        {
          symbol: `STOCK3`,
          name: `${sectorName} Leader 3`,
          performance: performance + Math.random() * 1,
          volume: Math.floor(Math.random() * 2000000) + 300000,
          signal: 'neutral' as const
        }
      ],
      outlook: this.generateSectorOutlook(sectorName, performance, 10, Math.abs(performance))
    };
  }

  private determineSignal(changePercent: number, volume: number): string {
    if (changePercent > 2 && volume > 1000000) return 'opportunity';
    if (changePercent < -2) return 'caution';
    return 'neutral';
  }

  private generateSectorOutlook(sector: string, performance: number, stockCount: number, momentumScore?: number): string {
    const direction = performance > 0 ? 'positive' : 'negative';
    const strength = Math.abs(performance) > 2 ? 'strong' : 'moderate';
    const momentum = momentumScore ? (momentumScore > 3 ? 'high' : momentumScore > 1.5 ? 'moderate' : 'low') : 'moderate';

    return `${sector} showing ${strength} ${direction} momentum (${momentum} volatility) with ${stockCount} stocks analyzed. ${
      performance > 2 ? 'Strong buying interest observed.' :
      performance > 0 ? 'Cautious optimism in the sector.' :
      performance > -2 ? 'Mixed signals with some consolidation.' :
      'Sector facing headwinds, monitor for reversal signals.'
    } ${momentumScore ? `Sector momentum: ${momentumScore.toFixed(2)}%` : ''}`;
  }

  async getTopMovers(region: 'india' | 'global' | 'crypto', count: number = 3) {
    let allSymbols: string[] = [];
    
    switch (region) {
      case 'india':
        allSymbols = Object.values(INDIAN_STOCKS).flat();
        break;
      case 'global':
        allSymbols = Object.values(GLOBAL_STOCKS).flat();
        break;
      case 'crypto':
        allSymbols = Object.values(CRYPTO_ASSETS).flat();
        break;
    }

    const stockData = await this.fetchMultipleStocks(allSymbols);
    
    return stockData
      .sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent))
      .slice(0, count);
  }

  async fetchMarketNews(region: 'india' | 'global' | 'crypto', count: number = 10) {
    if (!this.NEWS_API_KEY) {
      return this.getMockNews(region, count);
    }

    try {
      const query = this.getNewsQuery(region);
      const response = await fetch(
        `${this.NEWS_API_BASE}?q=${encodeURIComponent(query)}&sortBy=publishedAt&pageSize=${count}&apiKey=${this.NEWS_API_KEY}`
      );
      
      const data = await response.json();
      
      if (!data.articles) {
        return this.getMockNews(region, count);
      }

      return data.articles.map((article: any) => ({
        headline: article.title,
        summary: article.description || article.title,
        sentiment: this.analyzeSentiment(article.title + ' ' + (article.description || '')),
        impactScore: this.calculateImpactScore(article.title, region),
        affectedAssets: this.extractAffectedAssets(article.title + ' ' + (article.description || ''), region),
        url: article.url,
        publishedAt: article.publishedAt
      }));
    } catch (error) {
      console.error('Error fetching news:', error);
      return this.getMockNews(region, count);
    }
  }

  private getNewsQuery(region: 'india' | 'global' | 'crypto'): string {
    switch (region) {
      case 'india':
        return 'India stock market OR Nifty OR Sensex OR RBI OR Indian economy';
      case 'global':
        return 'stock market OR S&P 500 OR Nasdaq OR Federal Reserve OR economy';
      case 'crypto':
        return 'cryptocurrency OR Bitcoin OR Ethereum OR blockchain OR crypto market';
      default:
        return 'stock market';
    }
  }

  private analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = ['gain', 'rise', 'up', 'bull', 'growth', 'strong', 'beat', 'surge', 'rally'];
    const negativeWords = ['fall', 'drop', 'down', 'bear', 'decline', 'weak', 'miss', 'crash', 'sell'];
    
    const lowerText = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private calculateImpactScore(headline: string, region: string): number {
    let score = 50; // Base score
    
    // High impact keywords
    const highImpactWords = ['fed', 'rbi', 'earnings', 'gdp', 'inflation', 'rate', 'policy'];
    const mediumImpactWords = ['market', 'stock', 'index', 'sector', 'company'];
    
    const lowerHeadline = headline.toLowerCase();
    
    highImpactWords.forEach(word => {
      if (lowerHeadline.includes(word)) score += 15;
    });
    
    mediumImpactWords.forEach(word => {
      if (lowerHeadline.includes(word)) score += 5;
    });
    
    return Math.min(score, 100);
  }

  private extractAffectedAssets(text: string, region: 'india' | 'global' | 'crypto'): string[] {
    const assets: string[] = [];
    const lowerText = text.toLowerCase();
    
    switch (region) {
      case 'india':
        if (lowerText.includes('nifty')) assets.push('NIFTY');
        if (lowerText.includes('sensex')) assets.push('SENSEX');
        if (lowerText.includes('bank')) assets.push('BANKNIFTY');
        if (lowerText.includes('it') || lowerText.includes('tech')) assets.push('IT');
        break;
      case 'global':
        if (lowerText.includes('s&p') || lowerText.includes('sp500')) assets.push('SPY');
        if (lowerText.includes('nasdaq')) assets.push('QQQ');
        if (lowerText.includes('dow')) assets.push('DIA');
        break;
      case 'crypto':
        if (lowerText.includes('bitcoin')) assets.push('BTC');
        if (lowerText.includes('ethereum')) assets.push('ETH');
        if (lowerText.includes('crypto')) assets.push('CRYPTO');
        break;
    }
    
    return assets;
  }

  private getMockNews(region: 'india' | 'global' | 'crypto', count: number) {
    // Fallback mock news when API is not available
    const mockNews = {
      india: [
        {
          headline: 'RBI Maintains Repo Rate at 6.5%, Signals Pause in Rate Hike Cycle',
          summary: 'Reserve Bank of India keeps policy rates unchanged, citing balanced approach to inflation and growth.',
          sentiment: 'positive' as const,
          impactScore: 85,
          affectedAssets: ['NIFTY', 'SENSEX', 'BANKNIFTY']
        }
      ],
      global: [
        {
          headline: 'Fed Signals Potential Rate Cuts in 2024, Markets Rally',
          summary: 'Federal Reserve hints at possible rate cuts if inflation continues to moderate.',
          sentiment: 'positive' as const,
          impactScore: 88,
          affectedAssets: ['SPY', 'QQQ', 'DIA']
        }
      ],
      crypto: [
        {
          headline: 'Bitcoin ETF Approval Odds Rise to 90%, Analysts Say',
          summary: 'Multiple sources suggest SEC approval for spot Bitcoin ETF is imminent.',
          sentiment: 'positive' as const,
          impactScore: 92,
          affectedAssets: ['BTC', 'ETH', 'CRYPTO']
        }
      ]
    };
    
    return mockNews[region].slice(0, count);
  }
}

export const realMarketDataService = new RealMarketDataService();
export type { StockData, SectorStocks };
