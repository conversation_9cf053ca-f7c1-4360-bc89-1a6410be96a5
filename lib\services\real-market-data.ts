// Real Market Data Service
// Integrates with multiple APIs for comprehensive market data

interface StockData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  sector?: string;
}

interface SectorStocks {
  [sector: string]: string[];
}

// Comprehensive stock lists for analysis
const INDIAN_STOCKS: SectorStocks = {
  'Information Technology': [
    'TCS.NS', 'INFY.NS', 'WIPRO.NS', 'HCLTECH.NS', 'TECHM.NS', 
    'LTI.NS', 'MINDTREE.NS', 'MPHASIS.NS', 'COFORGE.NS', 'LTTS.NS',
    'PERSISTENT.NS', 'CYIENT.NS', 'ROLTA.NS', 'NIIT.NS', 'KPIT.NS',
    'ZENSAR.NS', 'SONATA.NS', 'HEXAWARE.NS', 'INTELLECT.NS', 'POLYCAB.NS'
  ],
  'Banking': [
    'HDFCBANK.NS', 'ICICIBANK.NS', 'SBIN.NS', 'KOTAKBANK.NS', 'AXISBANK.NS',
    'INDUSINDBK.NS', 'FEDERALBNK.NS', 'BANDHANBNK.NS', 'RBLBANK.NS', 'YESBANK.NS',
    'IDFCFIRSTB.NS', 'PNB.NS', 'BANKBARODA.NS', 'CANBK.NS', 'UNIONBANK.NS',
    'INDIANB.NS', 'CENTRALBK.NS', 'IOB.NS', 'MAHABANK.NS', 'JKBANK.NS'
  ],
  'Pharmaceuticals': [
    'SUNPHARMA.NS', 'DRREDDY.NS', 'CIPLA.NS', 'DIVISLAB.NS', 'BIOCON.NS',
    'CADILAHC.NS', 'LUPIN.NS', 'AUROPHARMA.NS', 'TORNTPHARM.NS', 'GLENMARK.NS',
    'ALKEM.NS', 'ABBOTINDIA.NS', 'PFIZER.NS', 'GSK.NS', 'NOVARTIS.NS',
    'SANOFI.NS', 'ERIS.NS', 'LALPATHLAB.NS', 'METROPOLIS.NS', 'THYROCARE.NS'
  ],
  'Automotive': [
    'MARUTI.NS', 'TATAMOTORS.NS', 'M&M.NS', 'BAJAJ-AUTO.NS', 'HEROMOTOCO.NS',
    'TVSMOTORS.NS', 'EICHERMOT.NS', 'ASHOKLEY.NS', 'TVSMOTOR.NS', 'BALKRISIND.NS',
    'MRF.NS', 'APOLLOTYRE.NS', 'CEAT.NS', 'JK.NS', 'MOTHERSUMI.NS',
    'BOSCHLTD.NS', 'BHARATFORG.NS', 'EXIDEIND.NS', 'AMARA.NS', 'FORCE.NS'
  ],
  'FMCG': [
    'HINDUNILVR.NS', 'ITC.NS', 'NESTLEIND.NS', 'BRITANNIA.NS', 'DABUR.NS',
    'GODREJCP.NS', 'MARICO.NS', 'COLPAL.NS', 'PGHH.NS', 'UBL.NS',
    'TATACONSUM.NS', 'EMAMILTD.NS', 'JYOTHYLAB.NS', 'RADICO.NS', 'VBL.NS',
    'CCL.NS', 'GILLETTE.NS', 'HONASA.NS', 'PATANJALI.NS', 'BIKAJI.NS'
  ]
};

const GLOBAL_STOCKS: SectorStocks = {
  'Technology': [
    'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'TSLA', 'NVDA', 'NFLX',
    'ADBE', 'CRM', 'ORCL', 'IBM', 'INTC', 'AMD', 'QCOM', 'AVGO',
    'TXN', 'AMAT', 'LRCX', 'KLAC'
  ],
  'Healthcare': [
    'JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'ABT', 'DHR',
    'BMY', 'AMGN', 'GILD', 'MDT', 'ISRG', 'VRTX', 'REGN', 'BIIB',
    'ILMN', 'MRNA', 'ZTS', 'CVS'
  ],
  'Financial Services': [
    'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP', 'BLK',
    'SCHW', 'USB', 'PNC', 'TFC', 'COF', 'CME', 'ICE', 'SPGI',
    'MCO', 'AON', 'MMC', 'AJG'
  ],
  'Consumer Discretionary': [
    'AMZN', 'TSLA', 'HD', 'MCD', 'NKE', 'SBUX', 'TJX', 'LOW',
    'BKNG', 'ABNB', 'GM', 'F', 'NCLH', 'CCL', 'RCL', 'MAR',
    'HLT', 'MGM', 'LVS', 'WYNN'
  ],
  'Energy': [
    'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'PXD', 'KMI', 'OKE',
    'WMB', 'MPC', 'VLO', 'PSX', 'HES', 'DVN', 'FANG', 'APA',
    'OXY', 'HAL', 'BKR', 'NOV'
  ]
};

const CRYPTO_ASSETS = [
  'BTC-USD', 'ETH-USD', 'BNB-USD', 'XRP-USD', 'ADA-USD', 'SOL-USD',
  'DOGE-USD', 'DOT-USD', 'MATIC-USD', 'SHIB-USD', 'AVAX-USD', 'UNI-USD',
  'LINK-USD', 'ATOM-USD', 'XLM-USD', 'ALGO-USD', 'VET-USD', 'ICP-USD',
  'FIL-USD', 'THETA-USD'
];

class RealMarketDataService {
  private readonly YAHOO_FINANCE_BASE = 'https://query1.finance.yahoo.com/v8/finance/chart/';
  private readonly ALPHA_VANTAGE_BASE = 'https://www.alphavantage.co/query';
  private readonly NEWS_API_BASE = 'https://newsapi.org/v2/everything';
  
  // Free APIs - no key required for basic Yahoo Finance
  private readonly ALPHA_VANTAGE_KEY = process.env.ALPHA_VANTAGE_API_KEY || 'demo';
  private readonly NEWS_API_KEY = process.env.NEWS_API_KEY || '';

  async fetchStockData(symbol: string): Promise<StockData | null> {
    try {
      // Use Yahoo Finance for real-time data
      const response = await fetch(`${this.YAHOO_FINANCE_BASE}${symbol}?interval=1d&range=2d`);
      const data = await response.json();
      
      if (!data.chart?.result?.[0]) {
        return null;
      }

      const result = data.chart.result[0];
      const meta = result.meta;
      const quotes = result.indicators.quote[0];
      const timestamps = result.timestamp;
      
      if (!quotes.close || quotes.close.length < 2) {
        return null;
      }

      const currentPrice = quotes.close[quotes.close.length - 1];
      const previousPrice = quotes.close[quotes.close.length - 2];
      const volume = quotes.volume?.[quotes.volume.length - 1] || 0;
      
      const change = currentPrice - previousPrice;
      const changePercent = (change / previousPrice) * 100;

      return {
        symbol: symbol,
        name: meta.longName || meta.shortName || symbol,
        price: currentPrice,
        change: change,
        changePercent: changePercent,
        volume: volume,
        marketCap: meta.marketCap,
        sector: meta.sector
      };
    } catch (error) {
      console.error(`Error fetching data for ${symbol}:`, error);
      return null;
    }
  }

  async fetchMultipleStocks(symbols: string[]): Promise<StockData[]> {
    const promises = symbols.map(symbol => this.fetchStockData(symbol));
    const results = await Promise.allSettled(promises);
    
    return results
      .filter((result): result is PromiseFulfilledResult<StockData> => 
        result.status === 'fulfilled' && result.value !== null
      )
      .map(result => result.value);
  }

  async analyzeSectorPerformance(region: 'india' | 'global' | 'crypto') {
    let stocksToAnalyze: SectorStocks;
    
    switch (region) {
      case 'india':
        stocksToAnalyze = INDIAN_STOCKS;
        break;
      case 'global':
        stocksToAnalyze = GLOBAL_STOCKS;
        break;
      case 'crypto':
        // For crypto, we'll treat each major category as a "sector"
        stocksToAnalyze = {
          'Major Cryptocurrencies': CRYPTO_ASSETS.slice(0, 10),
          'DeFi Tokens': ['UNI-USD', 'AAVE-USD', 'COMP-USD', 'MKR-USD', 'SNX-USD'],
          'Layer 1 Blockchains': ['BTC-USD', 'ETH-USD', 'ADA-USD', 'SOL-USD', 'DOT-USD']
        };
        break;
      default:
        stocksToAnalyze = INDIAN_STOCKS;
    }

    const sectorAnalysis = [];

    for (const [sectorName, symbols] of Object.entries(stocksToAnalyze)) {
      const stockData = await this.fetchMultipleStocks(symbols);
      
      if (stockData.length === 0) continue;

      // Calculate sector performance (weighted by market cap if available)
      const totalMarketCap = stockData.reduce((sum, stock) => sum + (stock.marketCap || 1), 0);
      const weightedPerformance = stockData.reduce((sum, stock) => {
        const weight = (stock.marketCap || 1) / totalMarketCap;
        return sum + (stock.changePercent * weight);
      }, 0);

      // Get top 3 performers
      const topStocks = stockData
        .sort((a, b) => b.changePercent - a.changePercent)
        .slice(0, 3)
        .map(stock => ({
          symbol: stock.symbol.replace('.NS', '').replace('-USD', ''),
          name: stock.name,
          performance: stock.changePercent,
          volume: stock.volume,
          signal: this.determineSignal(stock.changePercent, stock.volume) as 'opportunity' | 'caution' | 'neutral'
        }));

      sectorAnalysis.push({
        sector: sectorName,
        performance: weightedPerformance,
        topStocks: topStocks,
        outlook: this.generateSectorOutlook(sectorName, weightedPerformance, stockData.length)
      });
    }

    return sectorAnalysis;
  }

  private determineSignal(changePercent: number, volume: number): string {
    if (changePercent > 2 && volume > 1000000) return 'opportunity';
    if (changePercent < -2) return 'caution';
    return 'neutral';
  }

  private generateSectorOutlook(sector: string, performance: number, stockCount: number): string {
    const direction = performance > 0 ? 'positive' : 'negative';
    const strength = Math.abs(performance) > 2 ? 'strong' : 'moderate';
    
    return `${sector} showing ${strength} ${direction} momentum with ${stockCount} stocks analyzed. ${
      performance > 2 ? 'Strong buying interest observed.' :
      performance > 0 ? 'Cautious optimism in the sector.' :
      performance > -2 ? 'Mixed signals with some consolidation.' :
      'Sector facing headwinds, monitor for reversal signals.'
    }`;
  }

  async getTopMovers(region: 'india' | 'global' | 'crypto', count: number = 3) {
    let allSymbols: string[] = [];
    
    switch (region) {
      case 'india':
        allSymbols = Object.values(INDIAN_STOCKS).flat();
        break;
      case 'global':
        allSymbols = Object.values(GLOBAL_STOCKS).flat();
        break;
      case 'crypto':
        allSymbols = CRYPTO_ASSETS;
        break;
    }

    const stockData = await this.fetchMultipleStocks(allSymbols);
    
    return stockData
      .sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent))
      .slice(0, count);
  }

  async fetchMarketNews(region: 'india' | 'global' | 'crypto', count: number = 10) {
    if (!this.NEWS_API_KEY) {
      return this.getMockNews(region, count);
    }

    try {
      const query = this.getNewsQuery(region);
      const response = await fetch(
        `${this.NEWS_API_BASE}?q=${encodeURIComponent(query)}&sortBy=publishedAt&pageSize=${count}&apiKey=${this.NEWS_API_KEY}`
      );
      
      const data = await response.json();
      
      if (!data.articles) {
        return this.getMockNews(region, count);
      }

      return data.articles.map((article: any) => ({
        headline: article.title,
        summary: article.description || article.title,
        sentiment: this.analyzeSentiment(article.title + ' ' + (article.description || '')),
        impactScore: this.calculateImpactScore(article.title, region),
        affectedAssets: this.extractAffectedAssets(article.title + ' ' + (article.description || ''), region),
        url: article.url,
        publishedAt: article.publishedAt
      }));
    } catch (error) {
      console.error('Error fetching news:', error);
      return this.getMockNews(region, count);
    }
  }

  private getNewsQuery(region: 'india' | 'global' | 'crypto'): string {
    switch (region) {
      case 'india':
        return 'India stock market OR Nifty OR Sensex OR RBI OR Indian economy';
      case 'global':
        return 'stock market OR S&P 500 OR Nasdaq OR Federal Reserve OR economy';
      case 'crypto':
        return 'cryptocurrency OR Bitcoin OR Ethereum OR blockchain OR crypto market';
      default:
        return 'stock market';
    }
  }

  private analyzeSentiment(text: string): 'positive' | 'negative' | 'neutral' {
    const positiveWords = ['gain', 'rise', 'up', 'bull', 'growth', 'strong', 'beat', 'surge', 'rally'];
    const negativeWords = ['fall', 'drop', 'down', 'bear', 'decline', 'weak', 'miss', 'crash', 'sell'];
    
    const lowerText = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private calculateImpactScore(headline: string, region: string): number {
    let score = 50; // Base score
    
    // High impact keywords
    const highImpactWords = ['fed', 'rbi', 'earnings', 'gdp', 'inflation', 'rate', 'policy'];
    const mediumImpactWords = ['market', 'stock', 'index', 'sector', 'company'];
    
    const lowerHeadline = headline.toLowerCase();
    
    highImpactWords.forEach(word => {
      if (lowerHeadline.includes(word)) score += 15;
    });
    
    mediumImpactWords.forEach(word => {
      if (lowerHeadline.includes(word)) score += 5;
    });
    
    return Math.min(score, 100);
  }

  private extractAffectedAssets(text: string, region: 'india' | 'global' | 'crypto'): string[] {
    const assets: string[] = [];
    const lowerText = text.toLowerCase();
    
    switch (region) {
      case 'india':
        if (lowerText.includes('nifty')) assets.push('NIFTY');
        if (lowerText.includes('sensex')) assets.push('SENSEX');
        if (lowerText.includes('bank')) assets.push('BANKNIFTY');
        if (lowerText.includes('it') || lowerText.includes('tech')) assets.push('IT');
        break;
      case 'global':
        if (lowerText.includes('s&p') || lowerText.includes('sp500')) assets.push('SPY');
        if (lowerText.includes('nasdaq')) assets.push('QQQ');
        if (lowerText.includes('dow')) assets.push('DIA');
        break;
      case 'crypto':
        if (lowerText.includes('bitcoin')) assets.push('BTC');
        if (lowerText.includes('ethereum')) assets.push('ETH');
        if (lowerText.includes('crypto')) assets.push('CRYPTO');
        break;
    }
    
    return assets;
  }

  private getMockNews(region: 'india' | 'global' | 'crypto', count: number) {
    // Fallback mock news when API is not available
    const mockNews = {
      india: [
        {
          headline: 'RBI Maintains Repo Rate at 6.5%, Signals Pause in Rate Hike Cycle',
          summary: 'Reserve Bank of India keeps policy rates unchanged, citing balanced approach to inflation and growth.',
          sentiment: 'positive' as const,
          impactScore: 85,
          affectedAssets: ['NIFTY', 'SENSEX', 'BANKNIFTY']
        }
      ],
      global: [
        {
          headline: 'Fed Signals Potential Rate Cuts in 2024, Markets Rally',
          summary: 'Federal Reserve hints at possible rate cuts if inflation continues to moderate.',
          sentiment: 'positive' as const,
          impactScore: 88,
          affectedAssets: ['SPY', 'QQQ', 'DIA']
        }
      ],
      crypto: [
        {
          headline: 'Bitcoin ETF Approval Odds Rise to 90%, Analysts Say',
          summary: 'Multiple sources suggest SEC approval for spot Bitcoin ETF is imminent.',
          sentiment: 'positive' as const,
          impactScore: 92,
          affectedAssets: ['BTC', 'ETH', 'CRYPTO']
        }
      ]
    };
    
    return mockNews[region].slice(0, count);
  }
}

export const realMarketDataService = new RealMarketDataService();
export type { StockData, SectorStocks };
