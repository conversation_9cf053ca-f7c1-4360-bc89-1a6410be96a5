<!DOCTYPE html>
<html>
<head>
    <title>Test Login</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1e293b; color: white; }
        .container { max-width: 500px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input { width: 100%; padding: 10px; border: 1px solid #475569; background: #334155; color: white; border-radius: 5px; }
        button { background: #3b82f6; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #2563eb; }
        .result { margin-top: 20px; padding: 15px; border-radius: 5px; }
        .success { background: #065f46; border: 1px solid #10b981; }
        .error { background: #7f1d1d; border: 1px solid #ef4444; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test Authentication</h1>
        
        <div style="margin-bottom: 20px; padding: 15px; background: #1e40af; border-radius: 5px;">
            <h3>🎯 Direct Login Test</h3>
            <p>This will test the authentication and redirect directly.</p>
            <button onclick="testDirectLogin()" style="background: #059669;">🚀 Test Direct Login</button>
        </div>
        
        <div style="margin-top: 30px;">
            <h3>📋 Test Credentials:</h3>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Password:</strong> (your signup password)</p>
            
            <h3>🔗 Manual Test Links:</h3>
            <p><a href="/auth/login" style="color: #60a5fa;">Go to Login Page</a></p>
            <p><a href="/" style="color: #60a5fa;">Go to Home Page</a></p>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        async function testDirectLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="result">🔄 Testing authentication flow...</div>';
            
            try {
                // Test 1: Check if we can access the login page
                resultDiv.innerHTML += '<div class="result">📝 Step 1: Checking login page access...</div>';
                
                const loginPageResponse = await fetch('/auth/login');
                if (loginPageResponse.ok) {
                    resultDiv.innerHTML += '<div class="result success">✅ Login page accessible</div>';
                } else {
                    resultDiv.innerHTML += '<div class="result error">❌ Login page not accessible</div>';
                    return;
                }
                
                // Test 2: Check NextAuth API
                resultDiv.innerHTML += '<div class="result">🔧 Step 2: Checking NextAuth API...</div>';
                
                const authResponse = await fetch('/api/auth/session');
                if (authResponse.ok) {
                    const session = await authResponse.json();
                    resultDiv.innerHTML += '<div class="result success">✅ NextAuth API working</div>';
                    resultDiv.innerHTML += `<div class="result">📊 Current session: ${JSON.stringify(session, null, 2)}</div>`;
                } else {
                    resultDiv.innerHTML += '<div class="result error">❌ NextAuth API not working</div>';
                }
                
                // Test 3: Redirect to login page
                resultDiv.innerHTML += '<div class="result success">🎯 Redirecting to login page for manual test...</div>';
                
                setTimeout(() => {
                    window.location.href = '/auth/login';
                }, 2000);
                
            } catch (error) {
                resultDiv.innerHTML += `<div class="result error">❌ Test failed: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
