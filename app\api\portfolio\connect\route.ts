import { NextRequest, NextResponse } from 'next/server';
import { brokerManager, BrokerCredentials, BrokerType } from '@/lib/brokers/broker-manager';

interface BrokerConnectionRequest {
  brokerId: BrokerType;
  credentials: {
    apiKey: string;
    apiSecret?: string;
    clientCode?: string;
    password?: string;
    accessToken?: string;
  };
  useRealAPI?: boolean; // Flag to enable real API vs mock data
}

interface PortfolioData {
  totalValue: number;
  totalInvestment: number;
  totalPnL: number;
  totalPnLPercent: number;
  dayPnL: number;
  dayPnLPercent: number;
  holdings: Array<{
    symbol: string;
    name: string;
    quantity: number;
    avgPrice: number;
    ltp: number;
    investment: number;
    currentValue: number;
    pnl: number;
    pnlPercent: number;
    dayChange: number;
    dayChangePercent: number;
    sector: string;
  }>;
  sectorAllocation: Array<{
    name: string;
    value: number;
    color: string;
  }>;
  performanceData: Array<{
    date: string;
    value: number;
  }>;
}

export async function POST(request: NextRequest) {
  try {
    const body: BrokerConnectionRequest = await request.json();
    const { brokerId, credentials, useRealAPI = false } = body;

    // Validate broker ID
    const supportedBrokers: BrokerType[] = [
      'zerodha', 'upstox', 'angelone', 'groww',
      'icicidirect', 'hdfcsec', 'kotak', 'sharekhan'
    ];

    if (!supportedBrokers.includes(brokerId)) {
      return NextResponse.json(
        { error: 'Unsupported broker' },
        { status: 400 }
      );
    }

    // If using real API, attempt actual broker connection
    if (useRealAPI && credentials && Object.keys(credentials).length > 0) {
      try {
        // Validate that we have the required credentials
        if (!credentials.apiKey || !credentials.clientCode || !credentials.password) {
          return NextResponse.json(
            {
              error: 'Missing required credentials. Please provide API Key, Client Code, and Password.',
              broker: brokerId,
              isRealData: true
            },
            { status: 400 }
          );
        }

        const brokerCredentials: BrokerCredentials = {
          brokerId,
          ...credentials
        };

        const connected = await brokerManager.connectBroker(brokerCredentials);

        if (connected) {
          const portfolio = await brokerManager.getPortfolio(brokerId);

          return NextResponse.json({
            success: true,
            data: portfolio,
            broker: brokerId,
            connectedAt: new Date().toISOString(),
            isRealData: true
          });
        } else {
          throw new Error('Failed to connect to broker');
        }
      } catch (error: any) {
        // If OAuth is required, return the OAuth URL
        if (error.message.includes('OAuth required:')) {
          const oauthUrl = error.message.split('OAuth required: ')[1];
          return NextResponse.json({
            success: false,
            requiresOAuth: true,
            oauthUrl,
            broker: brokerId
          });
        }

        // Provide specific error messages for common issues
        let errorMessage = error.message || 'Failed to connect to broker';
        let userFriendlyMessage = errorMessage;

        if (errorMessage.includes('TOTP_REQUIRED')) {
          userFriendlyMessage = 'Two-Factor Authentication is enabled on your account. Please disable 2FA in your Angel One account settings and try again.';
        } else if (errorMessage.includes('INVALID_CREDENTIALS')) {
          userFriendlyMessage = 'Invalid credentials. Please check your API Key, Client Code, and Password are correct.';
        } else if (errorMessage.includes('API_KEY_NOT_FOUND')) {
          userFriendlyMessage = 'API Key not found. Please ensure you have registered for SmartAPI and received your API key.';
        }

        return NextResponse.json(
          {
            error: userFriendlyMessage,
            originalError: errorMessage,
            broker: brokerId,
            isRealData: true
          },
          { status: 400 }
        );
      }
    }

    // Fallback to mock data (existing implementation)
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Mock portfolio data based on broker
    const mockPortfolioData: PortfolioData = {
      totalValue: Math.floor(Math.random() * 5000000) + 2000000,
      totalInvestment: Math.floor(Math.random() * 3000000) + 1500000,
      totalPnL: 0,
      totalPnLPercent: 0,
      dayPnL: Math.floor(Math.random() * 50000) - 25000,
      dayPnLPercent: (Math.random() * 2) - 1,
      holdings: generateMockHoldings(brokerId),
      sectorAllocation: [
        { name: 'Banking', value: 35.8, color: '#3B82F6' },
        { name: 'IT', value: 29.2, color: '#10B981' },
        { name: 'Energy', value: 25.7, color: '#F59E0B' },
        { name: 'Others', value: 9.3, color: '#8B5CF6' }
      ],
      performanceData: generatePerformanceData()
    };

    // Calculate derived values
    mockPortfolioData.totalPnL = mockPortfolioData.totalValue - mockPortfolioData.totalInvestment;
    mockPortfolioData.totalPnLPercent = (mockPortfolioData.totalPnL / mockPortfolioData.totalInvestment) * 100;

    return NextResponse.json({
      success: true,
      data: mockPortfolioData,
      broker: brokerId,
      connectedAt: new Date().toISOString(),
      isRealData: false
    });

  } catch (error) {
    console.error('Portfolio connection error:', error);
    return NextResponse.json(
      { error: 'Failed to connect to broker' },
      { status: 500 }
    );
  }
}

function generateMockHoldings(_brokerId: string) {
  const stocks = [
    { symbol: 'RELIANCE', name: 'Reliance Industries Ltd', sector: 'Energy' },
    { symbol: 'TCS', name: 'Tata Consultancy Services', sector: 'IT' },
    { symbol: 'HDFCBANK', name: 'HDFC Bank Ltd', sector: 'Banking' },
    { symbol: 'INFY', name: 'Infosys Ltd', sector: 'IT' },
    { symbol: 'ICICIBANK', name: 'ICICI Bank Ltd', sector: 'Banking' },
    { symbol: 'HINDUNILVR', name: 'Hindustan Unilever Ltd', sector: 'FMCG' },
    { symbol: 'ITC', name: 'ITC Ltd', sector: 'FMCG' },
    { symbol: 'SBIN', name: 'State Bank of India', sector: 'Banking' },
    { symbol: 'BHARTIARTL', name: 'Bharti Airtel Ltd', sector: 'Telecom' },
    { symbol: 'KOTAKBANK', name: 'Kotak Mahindra Bank', sector: 'Banking' }
  ];

  return stocks.slice(0, 5 + Math.floor(Math.random() * 3)).map(stock => {
    const quantity = Math.floor(Math.random() * 200) + 50;
    const avgPrice = Math.floor(Math.random() * 3000) + 500;
    const ltp = avgPrice + (Math.random() * 400) - 200;
    const investment = quantity * avgPrice;
    const currentValue = quantity * ltp;
    const pnl = currentValue - investment;
    const pnlPercent = (pnl / investment) * 100;
    const dayChange = (Math.random() * 50) - 25;
    const dayChangePercent = (dayChange / ltp) * 100;

    return {
      symbol: stock.symbol,
      name: stock.name,
      quantity,
      avgPrice,
      ltp,
      investment,
      currentValue,
      pnl,
      pnlPercent,
      dayChange,
      dayChangePercent,
      sector: stock.sector
    };
  });
}

function generatePerformanceData() {
  const data = [];
  const startValue = 2000000;
  let currentValue = startValue;
  
  for (let i = 0; i < 12; i++) {
    const date = new Date();
    date.setMonth(date.getMonth() - (11 - i));
    
    // Simulate portfolio growth with some volatility
    const monthlyReturn = (Math.random() * 0.1) - 0.02; // -2% to +8% monthly
    currentValue = currentValue * (1 + monthlyReturn);
    
    data.push({
      date: date.toISOString().split('T')[0],
      value: Math.floor(currentValue)
    });
  }
  
  return data;
}

export async function GET() {
  return NextResponse.json({
    supportedBrokers: [
      { id: 'zerodha', name: 'Zerodha', status: 'active' },
      { id: 'upstox', name: 'Upstox', status: 'active' },
      { id: 'angelone', name: 'Angel One', status: 'active' },
      { id: 'groww', name: 'Groww', status: 'active' },
      { id: 'icicidirect', name: 'ICICI Direct', status: 'active' },
      { id: 'hdfcsec', name: 'HDFC Securities', status: 'active' },
      { id: 'kotak', name: 'Kotak Securities', status: 'active' },
      { id: 'sharekhan', name: 'Sharekhan', status: 'active' }
    ]
  });
}
