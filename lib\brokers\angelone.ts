// Angel One SmartAPI Integration
// Documentation: https://smartapi.angelbroking.com/docs

interface AngelOneConfig {
  apiKey: string;
  clientCode: string;
  password: string;
  totpSecret?: string;
  mpin?: string; // Mobile PIN for some accounts
}

interface AngelOneLoginResponse {
  status: boolean;
  message: string;
  errorcode: string;
  data: {
    jwtToken: string;
    refreshToken: string;
    feedToken: string;
  };
}

interface AngelOneHolding {
  symboltoken: string;
  symbol: string;
  isin: string;
  t1quantity: string;
  realizedquantity: string;
  quantity: string;
  authorizedquantity: string;
  profitandloss: string;
  product: string;
  collateralquantity: string;
  collateraltype: string;
  haircut: string;
  averageprice: string;
  ltp: string;
  symbolname: string;
  strikeprice: string;
  optiontype: string;
  expirydate: string;
}

export class AngelOneAPI {
  private baseURL = 'https://apiconnect.angelbroking.com';
  private config: AngelOneConfig;
  private jwtToken: string | null = null;

  constructor(config: AngelOneConfig) {
    this.config = config;
  }

  async login(): Promise<boolean> {
    try {
      // First, try login without TOTP (for accounts without 2FA)
      let loginPayload: any = {
        clientcode: this.config.clientCode,
        password: this.config.password
      };

      // Add TOTP if available
      if (this.config.totpSecret) {
        loginPayload.totp = this.generateTOTP();
      }

      const response = await fetch(`${this.baseURL}/rest/auth/angelbroking/user/v1/loginByPassword`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-UserType': 'USER',
          'X-SourceID': 'WEB',
          'X-ClientLocalIP': '127.0.0.1',
          'X-ClientPublicIP': '127.0.0.1',
          'X-MACAddress': '00:00:00:00:00:00',
          'X-PrivateKey': this.config.apiKey
        },
        body: JSON.stringify(loginPayload)
      });

      const data: AngelOneLoginResponse = await response.json();

      if (data.status && data.data?.jwtToken) {
        this.jwtToken = data.data.jwtToken;
        return true;
      }

      // If login failed due to TOTP, provide helpful error message
      if (data.message?.toLowerCase().includes('totp') || data.message?.toLowerCase().includes('otp')) {
        throw new Error('TOTP required. Please enable 2FA in your Angel One account and provide TOTP secret.');
      }

      throw new Error(data.message || 'Login failed');
    } catch (error) {
      console.error('Angel One login error:', error);
      return false;
    }
  }

  async getHoldings(): Promise<any[]> {
    if (!this.jwtToken) {
      const loginSuccess = await this.login();
      if (!loginSuccess) {
        throw new Error('Authentication failed');
      }
    }

    try {
      const response = await fetch(`${this.baseURL}/rest/secure/angelbroking/portfolio/v1/getHolding`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.jwtToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-UserType': 'USER',
          'X-SourceID': 'WEB',
          'X-ClientLocalIP': '127.0.0.1',
          'X-ClientPublicIP': '127.0.0.1',
          'X-MACAddress': '00:00:00:00:00:00',
          'X-PrivateKey': this.config.apiKey
        }
      });

      const data = await response.json();
      
      if (data.status && data.data) {
        return this.transformHoldings(data.data);
      }
      
      throw new Error(data.message || 'Failed to fetch holdings');
    } catch (error) {
      console.error('Angel One holdings error:', error);
      throw error;
    }
  }

  private transformHoldings(holdings: AngelOneHolding[]): any[] {
    return holdings.map(holding => {
      const quantity = parseInt(holding.realizedquantity) || parseInt(holding.quantity);
      const avgPrice = parseFloat(holding.averageprice);
      const ltp = parseFloat(holding.ltp);
      const investment = quantity * avgPrice;
      const currentValue = quantity * ltp;
      const pnl = currentValue - investment;
      const pnlPercent = (pnl / investment) * 100;

      return {
        symbol: holding.symbol,
        name: holding.symbolname,
        quantity: quantity,
        avgPrice: avgPrice,
        ltp: ltp,
        investment: investment,
        currentValue: currentValue,
        pnl: pnl,
        pnlPercent: pnlPercent,
        dayChange: 0, // Would need separate API call for day change
        dayChangePercent: 0,
        sector: this.getSectorFromSymbol(holding.symbol),
        isin: holding.isin
      };
    });
  }

  private getSectorFromSymbol(symbol: string): string {
    // Simple sector mapping - in production, use a comprehensive database
    const sectorMap: { [key: string]: string } = {
      'RELIANCE': 'Energy',
      'TCS': 'IT',
      'HDFCBANK': 'Banking',
      'INFY': 'IT',
      'ICICIBANK': 'Banking',
      'HINDUNILVR': 'FMCG',
      'ITC': 'FMCG',
      'SBIN': 'Banking',
      'BHARTIARTL': 'Telecom',
      'KOTAKBANK': 'Banking'
    };
    
    return sectorMap[symbol] || 'Others';
  }

  private generateTOTP(): string {
    if (!this.config.totpSecret) {
      return '';
    }

    // Simple TOTP implementation
    // In production, use a proper TOTP library like 'otplib'
    try {
      const crypto = require('crypto');
      const base32 = require('base32');

      // Decode the base32 secret
      const key = base32.decode(this.config.totpSecret);

      // Get current time step (30 seconds)
      const timeStep = Math.floor(Date.now() / 1000 / 30);

      // Convert to 8-byte buffer
      const timeBuffer = Buffer.alloc(8);
      timeBuffer.writeUInt32BE(timeStep, 4);

      // Generate HMAC
      const hmac = crypto.createHmac('sha1', key);
      hmac.update(timeBuffer);
      const digest = hmac.digest();

      // Extract 4 bytes
      const offset = digest[digest.length - 1] & 0x0f;
      const code = ((digest[offset] & 0x7f) << 24) |
                   ((digest[offset + 1] & 0xff) << 16) |
                   ((digest[offset + 2] & 0xff) << 8) |
                   (digest[offset + 3] & 0xff);

      // Return 6-digit code
      return (code % 1000000).toString().padStart(6, '0');
    } catch (error) {
      console.error('TOTP generation error:', error);
      return '';
    }
  }

  async getProfile(): Promise<any> {
    if (!this.jwtToken) {
      await this.login();
    }

    try {
      const response = await fetch(`${this.baseURL}/rest/secure/angelbroking/user/v1/getProfile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.jwtToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-UserType': 'USER',
          'X-SourceID': 'WEB',
          'X-ClientLocalIP': '127.0.0.1',
          'X-ClientPublicIP': '127.0.0.1',
          'X-MACAddress': '00:00:00:00:00:00',
          'X-PrivateKey': this.config.apiKey
        }
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Angel One profile error:', error);
      throw error;
    }
  }

  async logout(): Promise<boolean> {
    if (!this.jwtToken) return true;

    try {
      const response = await fetch(`${this.baseURL}/rest/secure/angelbroking/user/v1/logout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.jwtToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-UserType': 'USER',
          'X-SourceID': 'WEB',
          'X-ClientLocalIP': '127.0.0.1',
          'X-ClientPublicIP': '127.0.0.1',
          'X-MACAddress': '00:00:00:00:00:00',
          'X-PrivateKey': this.config.apiKey
        },
        body: JSON.stringify({
          clientcode: this.config.clientCode
        })
      });

      const data = await response.json();
      this.jwtToken = null;
      return data.status;
    } catch (error) {
      console.error('Angel One logout error:', error);
      return false;
    }
  }
}

// Usage example:
/*
const angelOne = new AngelOneAPI({
  apiKey: process.env.ANGEL_ONE_API_KEY!,
  clientCode: process.env.ANGEL_ONE_CLIENT_CODE!,
  password: process.env.ANGEL_ONE_PASSWORD!
});

const holdings = await angelOne.getHoldings();
*/
