// Angel One SmartAPI Integration
// Documentation: https://smartapi.angelbroking.com/docs

interface AngelOneConfig {
  apiKey: string;
  clientCode: string;
  password: string;
  totpSecret?: string;
}

interface AngelOneLoginResponse {
  status: boolean;
  message: string;
  errorcode: string;
  data: {
    jwtToken: string;
    refreshToken: string;
    feedToken: string;
  };
}

interface AngelOneHolding {
  symboltoken: string;
  symbol: string;
  isin: string;
  t1quantity: string;
  realizedquantity: string;
  quantity: string;
  authorizedquantity: string;
  profitandloss: string;
  product: string;
  collateralquantity: string;
  collateraltype: string;
  haircut: string;
  averageprice: string;
  ltp: string;
  symbolname: string;
  strikeprice: string;
  optiontype: string;
  expirydate: string;
}

export class AngelOneAPI {
  private baseURL = 'https://apiconnect.angelbroking.com';
  private config: AngelOneConfig;
  private jwtToken: string | null = null;

  constructor(config: AngelOneConfig) {
    this.config = config;
  }

  async login(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseURL}/rest/auth/angelbroking/user/v1/loginByPassword`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-UserType': 'USER',
          'X-SourceID': 'WEB',
          'X-ClientLocalIP': '127.0.0.1',
          'X-ClientPublicIP': '127.0.0.1',
          'X-MACAddress': '00:00:00:00:00:00',
          'X-PrivateKey': this.config.apiKey
        },
        body: JSON.stringify({
          clientcode: this.config.clientCode,
          password: this.config.password,
          totp: this.generateTOTP() // If 2FA is enabled
        })
      });

      const data: AngelOneLoginResponse = await response.json();
      
      if (data.status && data.data.jwtToken) {
        this.jwtToken = data.data.jwtToken;
        return true;
      }
      
      throw new Error(data.message || 'Login failed');
    } catch (error) {
      console.error('Angel One login error:', error);
      return false;
    }
  }

  async getHoldings(): Promise<any[]> {
    if (!this.jwtToken) {
      const loginSuccess = await this.login();
      if (!loginSuccess) {
        throw new Error('Authentication failed');
      }
    }

    try {
      const response = await fetch(`${this.baseURL}/rest/secure/angelbroking/portfolio/v1/getHolding`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.jwtToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-UserType': 'USER',
          'X-SourceID': 'WEB',
          'X-ClientLocalIP': '127.0.0.1',
          'X-ClientPublicIP': '127.0.0.1',
          'X-MACAddress': '00:00:00:00:00:00',
          'X-PrivateKey': this.config.apiKey
        }
      });

      const data = await response.json();
      
      if (data.status && data.data) {
        return this.transformHoldings(data.data);
      }
      
      throw new Error(data.message || 'Failed to fetch holdings');
    } catch (error) {
      console.error('Angel One holdings error:', error);
      throw error;
    }
  }

  private transformHoldings(holdings: AngelOneHolding[]): any[] {
    return holdings.map(holding => {
      const quantity = parseInt(holding.realizedquantity) || parseInt(holding.quantity);
      const avgPrice = parseFloat(holding.averageprice);
      const ltp = parseFloat(holding.ltp);
      const investment = quantity * avgPrice;
      const currentValue = quantity * ltp;
      const pnl = currentValue - investment;
      const pnlPercent = (pnl / investment) * 100;

      return {
        symbol: holding.symbol,
        name: holding.symbolname,
        quantity: quantity,
        avgPrice: avgPrice,
        ltp: ltp,
        investment: investment,
        currentValue: currentValue,
        pnl: pnl,
        pnlPercent: pnlPercent,
        dayChange: 0, // Would need separate API call for day change
        dayChangePercent: 0,
        sector: this.getSectorFromSymbol(holding.symbol),
        isin: holding.isin
      };
    });
  }

  private getSectorFromSymbol(symbol: string): string {
    // Simple sector mapping - in production, use a comprehensive database
    const sectorMap: { [key: string]: string } = {
      'RELIANCE': 'Energy',
      'TCS': 'IT',
      'HDFCBANK': 'Banking',
      'INFY': 'IT',
      'ICICIBANK': 'Banking',
      'HINDUNILVR': 'FMCG',
      'ITC': 'FMCG',
      'SBIN': 'Banking',
      'BHARTIARTL': 'Telecom',
      'KOTAKBANK': 'Banking'
    };
    
    return sectorMap[symbol] || 'Others';
  }

  private generateTOTP(): string {
    // If TOTP is enabled, implement TOTP generation
    // For now, return empty string (works if TOTP is disabled)
    return '';
  }

  async getProfile(): Promise<any> {
    if (!this.jwtToken) {
      await this.login();
    }

    try {
      const response = await fetch(`${this.baseURL}/rest/secure/angelbroking/user/v1/getProfile`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.jwtToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-UserType': 'USER',
          'X-SourceID': 'WEB',
          'X-ClientLocalIP': '127.0.0.1',
          'X-ClientPublicIP': '127.0.0.1',
          'X-MACAddress': '00:00:00:00:00:00',
          'X-PrivateKey': this.config.apiKey
        }
      });

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Angel One profile error:', error);
      throw error;
    }
  }

  async logout(): Promise<boolean> {
    if (!this.jwtToken) return true;

    try {
      const response = await fetch(`${this.baseURL}/rest/secure/angelbroking/user/v1/logout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.jwtToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-UserType': 'USER',
          'X-SourceID': 'WEB',
          'X-ClientLocalIP': '127.0.0.1',
          'X-ClientPublicIP': '127.0.0.1',
          'X-MACAddress': '00:00:00:00:00:00',
          'X-PrivateKey': this.config.apiKey
        },
        body: JSON.stringify({
          clientcode: this.config.clientCode
        })
      });

      const data = await response.json();
      this.jwtToken = null;
      return data.status;
    } catch (error) {
      console.error('Angel One logout error:', error);
      return false;
    }
  }
}

// Usage example:
/*
const angelOne = new AngelOneAPI({
  apiKey: process.env.ANGEL_ONE_API_KEY!,
  clientCode: process.env.ANGEL_ONE_CLIENT_CODE!,
  password: process.env.ANGEL_ONE_PASSWORD!
});

const holdings = await angelOne.getHoldings();
*/
