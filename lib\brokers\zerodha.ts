// Zerodha Kite Connect API Integration
// Documentation: https://kite.trade/docs/connect/v3/

interface ZerodhaConfig {
  apiKey: string;
  apiSecret: string;
  accessToken?: string;
}

interface ZerodhaHolding {
  tradingsymbol: string;
  exchange: string;
  instrument_token: number;
  isin: string;
  product: string;
  price: number;
  quantity: number;
  used_quantity: number;
  t1_quantity: number;
  realised_quantity: number;
  authorised_quantity: number;
  authorised_date: string;
  opening_quantity: number;
  collateral_quantity: number;
  collateral_type: string;
  discrepancy: boolean;
  average_price: number;
  last_price: number;
  close_price: number;
  pnl: number;
  day_change: number;
  day_change_percentage: number;
}

interface ZerodhaLoginURL {
  url: string;
  state: string;
}

export class ZerodhaAPI {
  private baseURL = 'https://api.kite.trade';
  private config: ZerodhaConfig;

  constructor(config: ZerodhaConfig) {
    this.config = config;
  }

  // Step 1: Generate login URL for OAuth
  generateLoginURL(redirectURL: string): ZerodhaLoginURL {
    const state = this.generateRandomString(16);
    const url = `https://kite.zerodha.com/connect/login?api_key=${this.config.apiKey}&v=3&state=${state}`;
    
    return { url, state };
  }

  // Step 2: Exchange request token for access token
  async generateAccessToken(requestToken: string, checksum?: string): Promise<string> {
    try {
      const response = await fetch(`${this.baseURL}/session/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Kite-Version': '3'
        },
        body: new URLSearchParams({
          api_key: this.config.apiKey,
          request_token: requestToken,
          checksum: checksum || this.generateChecksum(requestToken)
        })
      });

      const data = await response.json();
      
      if (data.status === 'success' && data.data.access_token) {
        this.config.accessToken = data.data.access_token;
        return data.data.access_token;
      }
      
      throw new Error(data.message || 'Failed to generate access token');
    } catch (error) {
      console.error('Zerodha access token error:', error);
      throw error;
    }
  }

  // Get user profile
  async getProfile(): Promise<any> {
    if (!this.config.accessToken) {
      throw new Error('Access token required. Please authenticate first.');
    }

    try {
      const response = await fetch(`${this.baseURL}/user/profile`, {
        method: 'GET',
        headers: {
          'Authorization': `token ${this.config.apiKey}:${this.config.accessToken}`,
          'X-Kite-Version': '3'
        }
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        return data.data;
      }
      
      throw new Error(data.message || 'Failed to fetch profile');
    } catch (error) {
      console.error('Zerodha profile error:', error);
      throw error;
    }
  }

  // Get holdings
  async getHoldings(): Promise<any[]> {
    if (!this.config.accessToken) {
      throw new Error('Access token required. Please authenticate first.');
    }

    try {
      const response = await fetch(`${this.baseURL}/portfolio/holdings`, {
        method: 'GET',
        headers: {
          'Authorization': `token ${this.config.apiKey}:${this.config.accessToken}`,
          'X-Kite-Version': '3'
        }
      });

      const data = await response.json();
      
      if (data.status === 'success' && data.data) {
        return this.transformHoldings(data.data);
      }
      
      throw new Error(data.message || 'Failed to fetch holdings');
    } catch (error) {
      console.error('Zerodha holdings error:', error);
      throw error;
    }
  }

  // Get positions
  async getPositions(): Promise<any> {
    if (!this.config.accessToken) {
      throw new Error('Access token required. Please authenticate first.');
    }

    try {
      const response = await fetch(`${this.baseURL}/portfolio/positions`, {
        method: 'GET',
        headers: {
          'Authorization': `token ${this.config.apiKey}:${this.config.accessToken}`,
          'X-Kite-Version': '3'
        }
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        return data.data;
      }
      
      throw new Error(data.message || 'Failed to fetch positions');
    } catch (error) {
      console.error('Zerodha positions error:', error);
      throw error;
    }
  }

  // Get margins
  async getMargins(): Promise<any> {
    if (!this.config.accessToken) {
      throw new Error('Access token required. Please authenticate first.');
    }

    try {
      const response = await fetch(`${this.baseURL}/user/margins`, {
        method: 'GET',
        headers: {
          'Authorization': `token ${this.config.apiKey}:${this.config.accessToken}`,
          'X-Kite-Version': '3'
        }
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        return data.data;
      }
      
      throw new Error(data.message || 'Failed to fetch margins');
    } catch (error) {
      console.error('Zerodha margins error:', error);
      throw error;
    }
  }

  private transformHoldings(holdings: ZerodhaHolding[]): any[] {
    return holdings.map(holding => {
      const investment = holding.quantity * holding.average_price;
      const currentValue = holding.quantity * holding.last_price;
      const pnl = holding.pnl;
      const pnlPercent = (pnl / investment) * 100;

      return {
        symbol: holding.tradingsymbol,
        name: this.getCompanyName(holding.tradingsymbol),
        quantity: holding.quantity,
        avgPrice: holding.average_price,
        ltp: holding.last_price,
        investment: investment,
        currentValue: currentValue,
        pnl: pnl,
        pnlPercent: pnlPercent,
        dayChange: holding.day_change,
        dayChangePercent: holding.day_change_percentage,
        sector: this.getSectorFromSymbol(holding.tradingsymbol),
        isin: holding.isin,
        exchange: holding.exchange
      };
    });
  }

  private getCompanyName(symbol: string): string {
    // Simple company name mapping - in production, use a comprehensive database
    const nameMap: { [key: string]: string } = {
      'RELIANCE': 'Reliance Industries Ltd',
      'TCS': 'Tata Consultancy Services',
      'HDFCBANK': 'HDFC Bank Ltd',
      'INFY': 'Infosys Ltd',
      'ICICIBANK': 'ICICI Bank Ltd',
      'HINDUNILVR': 'Hindustan Unilever Ltd',
      'ITC': 'ITC Ltd',
      'SBIN': 'State Bank of India',
      'BHARTIARTL': 'Bharti Airtel Ltd',
      'KOTAKBANK': 'Kotak Mahindra Bank'
    };
    
    return nameMap[symbol] || symbol;
  }

  private getSectorFromSymbol(symbol: string): string {
    // Simple sector mapping - in production, use a comprehensive database
    const sectorMap: { [key: string]: string } = {
      'RELIANCE': 'Energy',
      'TCS': 'IT',
      'HDFCBANK': 'Banking',
      'INFY': 'IT',
      'ICICIBANK': 'Banking',
      'HINDUNILVR': 'FMCG',
      'ITC': 'FMCG',
      'SBIN': 'Banking',
      'BHARTIARTL': 'Telecom',
      'KOTAKBANK': 'Banking'
    };
    
    return sectorMap[symbol] || 'Others';
  }

  private generateChecksum(requestToken: string): string {
    // Generate checksum using crypto
    const crypto = require('crypto');
    const data = this.config.apiKey + requestToken + this.config.apiSecret;
    return crypto.createHash('sha256').update(data).digest('hex');
  }

  private generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Invalidate session
  async logout(): Promise<boolean> {
    if (!this.config.accessToken) return true;

    try {
      const response = await fetch(`${this.baseURL}/session/token`, {
        method: 'DELETE',
        headers: {
          'Authorization': `token ${this.config.apiKey}:${this.config.accessToken}`,
          'X-Kite-Version': '3'
        }
      });

      const data = await response.json();
      this.config.accessToken = undefined;
      return data.status === 'success';
    } catch (error) {
      console.error('Zerodha logout error:', error);
      return false;
    }
  }
}

// Usage example:
/*
const zerodha = new ZerodhaAPI({
  apiKey: process.env.ZERODHA_API_KEY!,
  apiSecret: process.env.ZERODHA_API_SECRET!
});

// Step 1: Redirect user to login
const { url } = zerodha.generateLoginURL('http://localhost:3000/auth/zerodha/callback');
// Redirect user to `url`

// Step 2: After callback, exchange request token
const accessToken = await zerodha.generateAccessToken(requestToken);

// Step 3: Fetch portfolio data
const holdings = await zerodha.getHoldings();
*/
