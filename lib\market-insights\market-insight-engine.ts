// Main Market Insight Engine - Orchestrates All Components

import { MarketInsightEngine, MarketData, FilterOptions } from './types';
import { TechnicalAnalysisEngine } from './technical-analysis';
import { SectorAnalysisEngine } from './sector-analysis';
import { OpportunityRadarEngine } from './opportunity-radar';
import { RegionalSummariesEngine } from './regional-summaries';
import { NewsImpactAnalyzer } from './news-impact-analyzer';
import { PredictivePromptingEngine } from './predictive-prompting';

export class MarketInsightEngineMain {
  private geminiApiKey?: string;
  private dataCache: Map<string, any> = new Map();
  private lastUpdate: Date = new Date();
  
  constructor(geminiApiKey?: string) {
    this.geminiApiKey = geminiApiKey;
  }
  
  // Generate Complete Market Insights
  async generateMarketInsights(
    marketData: MarketData[],
    historicalData: Map<string, number[]>,
    newsData: any[],
    filters?: FilterOptions
  ): Promise<MarketInsightEngine> {
    try {
      console.log('🚀 Generating comprehensive market insights...');
      
      // Apply filters if provided
      const filteredData = this.applyFilters(marketData, filters);
      
      // 1. Technical Analysis for all timeframes
      console.log('📊 Analyzing technical indicators...');
      const timeFrameAnalysis = await this.generateTimeFrameAnalysis(filteredData, historicalData);
      
      // 2. Sector Analysis
      console.log('🏢 Analyzing sector performance...');
      const sectorAnalysis = SectorAnalysisEngine.analyzeAllSectors(filteredData);
      
      // 3. Opportunity & Risk Radar
      console.log('🎯 Identifying opportunities and risks...');
      const technicalData = this.calculateTechnicalData(filteredData, historicalData);
      const newsMap = this.createNewsMap(newsData);
      const opportunityRadar = OpportunityRadarEngine.getCombinedRadar(filteredData, technicalData, newsMap);
      
      // 4. Regional Market Summaries
      console.log('🌍 Generating regional summaries...');
      const regionalSummaries = await this.generateRegionalSummaries(filteredData, sectorAnalysis);
      
      // 5. News Impact Analysis
      console.log('📰 Analyzing news impact...');
      const newsImpacts = await NewsImpactAnalyzer.analyzeNewsImpact(newsData, filteredData, this.geminiApiKey);
      
      // 6. Predictive Prompting
      console.log('🤖 Generating insight questions...');
      const insightQuestions = PredictivePromptingEngine.generateInsightQuestions(
        filteredData, sectorAnalysis, newsImpacts, this.geminiApiKey
      );
      
      // 7. Overall Market Sentiment
      console.log('💭 Calculating market sentiment...');
      const marketSentiment = this.calculateMarketSentiment(filteredData, sectorAnalysis, newsImpacts);
      
      const insights: MarketInsightEngine = {
        timestamp: new Date().toISOString(),
        timeFrameAnalysis,
        sectorAnalysis,
        opportunityRadar,
        regionalSummaries,
        newsImpacts,
        insightQuestions,
        marketSentiment
      };
      
      // Cache the results
      this.cacheResults(insights);
      
      console.log('✅ Market insights generated successfully!');
      return insights;
      
    } catch (error) {
      console.error('❌ Error generating market insights:', error);
      throw error;
    }
  }
  
  // Generate Time Frame Analysis
  private async generateTimeFrameAnalysis(
    marketData: MarketData[],
    historicalData: Map<string, number[]>
  ) {
    const timeframes: ('1H' | '1D' | '1W')[] = ['1H', '1D', '1W'];
    const regions = ['india', 'global', 'crypto'];
    
    const analysis: any = {};
    
    for (const region of regions) {
      analysis[region] = [];
      
      // Filter data by region
      const regionData = this.filterByRegion(marketData, region);
      
      for (const timeframe of timeframes) {
        // Get representative data for the region
        const representativeAsset = regionData[0]; // Simplified - would use index data in production
        if (representativeAsset) {
          const prices = historicalData.get(representativeAsset.symbol) || [];
          const timeFrameAnalysis = TechnicalAnalysisEngine.analyzeTimeFrame(
            representativeAsset, prices, timeframe
          );
          analysis[region].push(timeFrameAnalysis);
        }
      }
    }
    
    return analysis;
  }
  
  // Calculate Technical Data for All Assets
  private calculateTechnicalData(
    marketData: MarketData[],
    historicalData: Map<string, number[]>
  ): Map<string, any> {
    const technicalData = new Map();
    
    for (const asset of marketData) {
      const prices = historicalData.get(asset.symbol) || [];
      if (prices.length > 0) {
        const indicators = TechnicalAnalysisEngine.calculateAllIndicators(prices);
        technicalData.set(asset.symbol, indicators);
      }
    }
    
    return technicalData;
  }
  
  // Create News Map
  private createNewsMap(newsData: any[]): Map<string, any> {
    const newsMap = new Map();
    
    for (const news of newsData) {
      if (news.symbols && Array.isArray(news.symbols)) {
        for (const symbol of news.symbols) {
          newsMap.set(symbol, news);
        }
      }
    }
    
    return newsMap;
  }
  
  // Generate Regional Summaries
  private async generateRegionalSummaries(
    marketData: MarketData[],
    sectorAnalysis: any[]
  ) {
    const summaries = [];
    
    // India Summary
    const indiaData = this.filterByRegion(marketData, 'india');
    const indicesData = this.createIndicesMap(indiaData);
    const sectorPerformance = this.createSectorPerformanceMap(sectorAnalysis);
    
    const indiaSummary = RegionalSummariesEngine.generateIndiaMarketSummary(
      indicesData, indiaData, sectorPerformance
    );
    summaries.push(indiaSummary);
    
    // Global Summary
    const globalIndicesData = this.createGlobalIndicesMap();
    const globalSummary = RegionalSummariesEngine.generateGlobalMarketSummary(globalIndicesData);
    summaries.push(globalSummary);
    
    // Crypto Summary
    const cryptoData = this.filterByRegion(marketData, 'crypto');
    const cryptoMap = this.createCryptoMap(cryptoData);
    const cryptoSummary = RegionalSummariesEngine.generateCryptoMarketSummary(cryptoMap);
    summaries.push(cryptoSummary);
    
    return summaries;
  }
  
  // Calculate Market Sentiment
  private calculateMarketSentiment(
    marketData: MarketData[],
    sectorAnalysis: any[],
    newsImpacts: any[]
  ) {
    // Calculate overall price movement
    const avgChange = marketData.reduce((sum, asset) => sum + asset.changePercent, 0) / marketData.length;
    
    // Calculate sector sentiment
    const positiveSectors = sectorAnalysis.filter(s => s.performance > 0).length;
    const negativeSectors = sectorAnalysis.filter(s => s.performance < 0).length;
    
    // Calculate news sentiment
    const positiveNews = newsImpacts.filter(n => n.sentiment === 'positive').length;
    const negativeNews = newsImpacts.filter(n => n.sentiment === 'negative').length;
    
    // Determine overall sentiment
    let overall: 'bullish' | 'bearish' | 'neutral';
    let confidence = 0;
    const factors: string[] = [];
    
    if (avgChange > 1 && positiveSectors > negativeSectors) {
      overall = 'bullish';
      confidence = Math.min(avgChange * 20 + (positiveSectors / sectorAnalysis.length) * 30, 90);
      factors.push('Broad market gains', 'Positive sector rotation');
    } else if (avgChange < -1 && negativeSectors > positiveSectors) {
      overall = 'bearish';
      confidence = Math.min(Math.abs(avgChange) * 20 + (negativeSectors / sectorAnalysis.length) * 30, 90);
      factors.push('Market decline', 'Sector weakness');
    } else {
      overall = 'neutral';
      confidence = 50;
      factors.push('Mixed signals', 'Consolidation phase');
    }
    
    // Add news factors
    if (positiveNews > negativeNews) {
      factors.push('Positive news flow');
      confidence += 10;
    } else if (negativeNews > positiveNews) {
      factors.push('Negative news impact');
      confidence += 10;
    }
    
    return {
      overall,
      confidence: Math.min(confidence, 95),
      factors
    };
  }
  
  // Apply Filters
  private applyFilters(marketData: MarketData[], filters?: FilterOptions): MarketData[] {
    if (!filters) return marketData;
    
    let filtered = [...marketData];
    
    if (filters.region && filters.region !== 'all') {
      filtered = this.filterByRegion(filtered, filters.region);
    }
    
    if (filters.sector) {
      filtered = filtered.filter(asset => asset.sector === filters.sector);
    }
    
    if (filters.assetClass) {
      filtered = filtered.filter(asset => this.getAssetClass(asset) === filters.assetClass);
    }
    
    return filtered;
  }
  
  // Filter by Region
  private filterByRegion(marketData: MarketData[], region: string): MarketData[] {
    switch (region) {
      case 'india':
        return marketData.filter(asset => asset.exchange === 'NSE' || asset.exchange === 'BSE');
      case 'global':
        return marketData.filter(asset => ['NYSE', 'NASDAQ', 'LSE', 'TSE'].includes(asset.exchange || ''));
      case 'crypto':
        return marketData.filter(asset => this.getAssetClass(asset) === 'crypto');
      default:
        return marketData;
    }
  }
  
  // Get Asset Class
  private getAssetClass(asset: MarketData): string {
    if (['BTC', 'ETH', 'SOL', 'ADA', 'MATIC'].includes(asset.symbol)) return 'crypto';
    if (asset.sector === 'Cryptocurrency') return 'crypto';
    return 'equity';
  }
  
  // Helper methods for creating maps
  private createIndicesMap(data: MarketData[]): Map<string, MarketData> {
    const map = new Map();
    // In production, this would fetch actual index data
    // For now, create mock index data based on market performance
    const avgChange = data.reduce((sum, asset) => sum + asset.changePercent, 0) / data.length;
    
    map.set('NIFTY', {
      symbol: 'NIFTY',
      name: 'Nifty 50',
      price: 19500 + (avgChange * 100),
      change: avgChange * 100,
      changePercent: avgChange,
      volume: 0,
      volumeChange: 0
    });
    
    return map;
  }
  
  private createGlobalIndicesMap(): Map<string, MarketData> {
    const map = new Map();
    // Mock global indices data
    return map;
  }
  
  private createCryptoMap(data: MarketData[]): Map<string, MarketData> {
    const map = new Map();
    for (const asset of data) {
      map.set(asset.symbol, asset);
    }
    return map;
  }
  
  private createSectorPerformanceMap(sectorAnalysis: any[]): Map<string, number> {
    const map = new Map();
    for (const sector of sectorAnalysis) {
      map.set(sector.sector, sector.performance);
    }
    return map;
  }
  
  // Cache Results
  private cacheResults(insights: MarketInsightEngine): void {
    this.dataCache.set('latest_insights', insights);
    this.lastUpdate = new Date();
  }
  
  // Get Cached Results
  getCachedInsights(): MarketInsightEngine | null {
    const cached = this.dataCache.get('latest_insights');
    const cacheAge = Date.now() - this.lastUpdate.getTime();
    
    // Return cached data if less than 5 minutes old
    if (cached && cacheAge < 5 * 60 * 1000) {
      return cached;
    }
    
    return null;
  }
}
