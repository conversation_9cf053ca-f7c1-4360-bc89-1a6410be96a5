// Simplified Angel One API Integration (No TOTP Required)
// This version uses a simpler approach for testing

interface AngelOneSimpleConfig {
  apiKey: string;
  clientCode: string;
  password: string;
}

interface AngelOneLoginResponse {
  status: boolean;
  message: string;
  errorcode?: string;
  data?: {
    jwtToken: string;
    refreshToken: string;
    feedToken: string;
  };
}

export class AngelOneSimpleAPI {
  private baseURL = 'https://apiconnect.angelbroking.com';
  private config: AngelOneSimpleConfig;
  private jwtToken: string | null = null;

  constructor(config: AngelOneSimpleConfig) {
    this.config = config;
  }

  async testConnection(): Promise<boolean> {
    try {
      // Try a simple API call to test if credentials work
      const response = await fetch(`${this.baseURL}/rest/auth/angelbroking/user/v1/loginByPassword`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'X-UserType': 'USER',
          'X-SourceID': 'WEB',
          'X-ClientLocalIP': '127.0.0.1',
          'X-ClientPublicIP': '127.0.0.1',
          'X-MACAddress': '00:00:00:00:00:00',
          'X-PrivateKey': this.config.apiKey
        },
        body: JSON.stringify({
          clientcode: this.config.clientCode,
          password: this.config.password
        })
      });

      const data: AngelOneLoginResponse = await response.json();
      
      // Check if the response indicates TOTP is required
      if (!data.status && data.message?.toLowerCase().includes('totp')) {
        throw new Error('TOTP_REQUIRED: Your Angel One account has 2FA enabled. Please disable 2FA or provide TOTP secret.');
      }
      
      // Check if credentials are valid
      if (!data.status && (data.message?.toLowerCase().includes('invalid') || data.message?.toLowerCase().includes('incorrect'))) {
        throw new Error('INVALID_CREDENTIALS: Please check your API key, client code, and password.');
      }
      
      // If we get here and status is true, credentials work
      if (data.status && data.data?.jwtToken) {
        this.jwtToken = data.data.jwtToken;
        return true;
      }
      
      // For any other error, provide the actual message
      throw new Error(data.message || 'Unknown error occurred');
      
    } catch (error: any) {
      console.error('Angel One connection test failed:', error.message);
      throw error;
    }
  }

  async getMockPortfolioData(): Promise<any[]> {
    // Return realistic mock data that simulates Angel One API response
    return [
      {
        symboltoken: "2885",
        symbol: "RELIANCE-EQ",
        isin: "INE002A01018",
        t1quantity: "0",
        realizedquantity: "150",
        quantity: "150",
        authorizedquantity: "150",
        profitandloss: "17437.50",
        product: "DELIVERY",
        collateralquantity: "0",
        collateraltype: "",
        haircut: "0.00",
        averageprice: "2340.50",
        ltp: "2456.75",
        symbolname: "RELIANCE",
        strikeprice: "0.00",
        optiontype: "",
        expirydate: ""
      },
      {
        symboltoken: "11536",
        symbol: "TCS-EQ",
        isin: "INE467B01029",
        t1quantity: "0",
        realizedquantity: "75",
        quantity: "75",
        authorizedquantity: "75",
        profitandloss: "24108.75",
        product: "DELIVERY",
        collateralquantity: "0",
        collateraltype: "",
        haircut: "0.00",
        averageprice: "3245.80",
        ltp: "3567.25",
        symbolname: "TCS",
        strikeprice: "0.00",
        optiontype: "",
        expirydate: ""
      },
      {
        symboltoken: "1333",
        symbol: "HDFCBANK-EQ",
        isin: "INE040A01034",
        t1quantity: "0",
        realizedquantity: "200",
        quantity: "200",
        authorizedquantity: "200",
        profitandloss: "13510.00",
        product: "DELIVERY",
        collateralquantity: "0",
        collateraltype: "",
        haircut: "0.00",
        averageprice: "1567.30",
        ltp: "1634.85",
        symbolname: "HDFCBANK",
        strikeprice: "0.00",
        optiontype: "",
        expirydate: ""
      }
    ];
  }

  transformHoldings(holdings: any[]): any[] {
    return holdings.map(holding => {
      const quantity = parseInt(holding.realizedquantity) || parseInt(holding.quantity);
      const avgPrice = parseFloat(holding.averageprice);
      const ltp = parseFloat(holding.ltp);
      const investment = quantity * avgPrice;
      const currentValue = quantity * ltp;
      const pnl = currentValue - investment;
      const pnlPercent = (pnl / investment) * 100;

      return {
        symbol: holding.symbolname,
        name: this.getCompanyName(holding.symbolname),
        quantity: quantity,
        avgPrice: avgPrice,
        ltp: ltp,
        investment: investment,
        currentValue: currentValue,
        pnl: pnl,
        pnlPercent: pnlPercent,
        dayChange: (Math.random() * 50) - 25, // Mock day change
        dayChangePercent: ((Math.random() * 2) - 1), // Mock day change %
        sector: this.getSectorFromSymbol(holding.symbolname),
        isin: holding.isin
      };
    });
  }

  private getCompanyName(symbol: string): string {
    const nameMap: { [key: string]: string } = {
      'RELIANCE': 'Reliance Industries Ltd',
      'TCS': 'Tata Consultancy Services',
      'HDFCBANK': 'HDFC Bank Ltd',
      'INFY': 'Infosys Ltd',
      'ICICIBANK': 'ICICI Bank Ltd',
      'HINDUNILVR': 'Hindustan Unilever Ltd',
      'ITC': 'ITC Ltd',
      'SBIN': 'State Bank of India',
      'BHARTIARTL': 'Bharti Airtel Ltd',
      'KOTAKBANK': 'Kotak Mahindra Bank'
    };
    
    return nameMap[symbol] || symbol + ' Ltd';
  }

  private getSectorFromSymbol(symbol: string): string {
    const sectorMap: { [key: string]: string } = {
      'RELIANCE': 'Energy',
      'TCS': 'IT',
      'HDFCBANK': 'Banking',
      'INFY': 'IT',
      'ICICIBANK': 'Banking',
      'HINDUNILVR': 'FMCG',
      'ITC': 'FMCG',
      'SBIN': 'Banking',
      'BHARTIARTL': 'Telecom',
      'KOTAKBANK': 'Banking'
    };
    
    return sectorMap[symbol] || 'Others';
  }

  async getHoldings(): Promise<any[]> {
    // For testing, return mock data that looks like real Angel One response
    const mockHoldings = await this.getMockPortfolioData();
    return this.transformHoldings(mockHoldings);
  }

  async logout(): Promise<boolean> {
    this.jwtToken = null;
    return true;
  }
}

// Usage example:
/*
const angelOneSimple = new AngelOneSimpleAPI({
  apiKey: process.env.ANGEL_ONE_API_KEY!,
  clientCode: process.env.ANGEL_ONE_CLIENT_CODE!,
  password: process.env.ANGEL_ONE_PASSWORD!
});

try {
  const connected = await angelOneSimple.testConnection();
  if (connected) {
    const holdings = await angelOneSimple.getHoldings();
    console.log('Portfolio:', holdings);
  }
} catch (error) {
  console.error('Connection failed:', error.message);
  
  if (error.message.includes('TOTP_REQUIRED')) {
    console.log('Please disable 2FA in Angel One or provide TOTP secret');
  } else if (error.message.includes('INVALID_CREDENTIALS')) {
    console.log('Please check your API credentials');
  }
}
*/
