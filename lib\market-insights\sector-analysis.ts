// Sector Analysis Engine for Indian Markets

import { SectorAnalysis, MarketData } from './types';
import { TechnicalAnalysisEngine } from './technical-analysis';

export class SectorAnalysisEngine {
  
  // Indian Market Sectors Configuration
  static readonly INDIAN_SECTORS = {
    'IT': {
      name: 'Information Technology',
      stocks: ['TCS', 'INFY', 'WIPRO', 'HCLTECH', 'TECHM', 'LTI', 'MINDTR<PERSON>', 'MPHASIS'],
      weightage: { 'TCS': 0.25, 'INFY': 0.20, 'WIPRO': 0.15, 'HCLTECH': 0.15, 'TECHM': 0.10 }
    },
    'BANKING': {
      name: 'Banking & Financial Services',
      stocks: ['HDFCBANK', 'ICICIBANK', 'SBIN', 'K<PERSON>AKBANK', 'AXISBANK', 'INDUSINDBK', 'FEDERALBNK'],
      weightage: { 'HDFCBANK': 0.30, 'ICICIBANK': 0.25, 'SBIN': 0.20, 'K<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>': 0.15 }
    },
    'PHAR<PERSON>': {
      name: 'Pharmaceuticals',
      stocks: ['SUNPHAR<PERSON>', 'DRREDDY', 'CIPLA', 'DIVI<PERSON>AB', 'BIOCON', 'LUPIN', 'CADILAHC'],
      weightage: { 'SUNPHARMA': 0.25, 'DRREDDY': 0.20, 'CIPLA': 0.15, 'DIVISLAB': 0.15 }
    },
    'AUTO': {
      name: 'Automobile',
      stocks: ['MARUTI', 'TATAMOTORS', 'M&M', 'BAJAJ-AUTO', 'HEROMOTOCO', 'EICHERMOT', 'TVSMOTOR'],
      weightage: { 'MARUTI': 0.25, 'TATAMOTORS': 0.20, 'M&M': 0.15, 'BAJAJ-AUTO': 0.15 }
    },
    'FMCG': {
      name: 'Fast Moving Consumer Goods',
      stocks: ['HINDUNILVR', 'ITC', 'NESTLEIND', 'BRITANNIA', 'DABUR', 'GODREJCP', 'MARICO'],
      weightage: { 'HINDUNILVR': 0.30, 'ITC': 0.25, 'NESTLEIND': 0.15, 'BRITANNIA': 0.10 }
    },
    'ENERGY': {
      name: 'Energy & Oil',
      stocks: ['RELIANCE', 'ONGC', 'IOC', 'BPCL', 'HINDPETRO', 'GAIL', 'POWERGRID'],
      weightage: { 'RELIANCE': 0.40, 'ONGC': 0.15, 'IOC': 0.15, 'BPCL': 0.10 }
    },
    'METALS': {
      name: 'Metals & Mining',
      stocks: ['TATASTEEL', 'JSWSTEEL', 'HINDALCO', 'VEDL', 'COALINDIA', 'NMDC', 'SAIL'],
      weightage: { 'TATASTEEL': 0.25, 'JSWSTEEL': 0.20, 'HINDALCO': 0.15, 'VEDL': 0.15 }
    },
    'TELECOM': {
      name: 'Telecommunications',
      stocks: ['BHARTIARTL', 'IDEA', 'RCOM', 'MTNL'],
      weightage: { 'BHARTIARTL': 0.60, 'IDEA': 0.25, 'RCOM': 0.10 }
    }
  };
  
  // Calculate Sector Performance
  static calculateSectorPerformance(sectorStocks: MarketData[]): number {
    if (sectorStocks.length === 0) return 0;
    
    // Calculate weighted average performance
    const totalMarketCap = sectorStocks.reduce((sum, stock) => sum + (stock.marketCap || 0), 0);
    
    if (totalMarketCap === 0) {
      // If no market cap data, use simple average
      return sectorStocks.reduce((sum, stock) => sum + stock.changePercent, 0) / sectorStocks.length;
    }
    
    // Weighted average by market cap
    return sectorStocks.reduce((sum, stock) => {
      const weight = (stock.marketCap || 0) / totalMarketCap;
      return sum + (stock.changePercent * weight);
    }, 0);
  }
  
  // Identify Top Performing Stocks in Sector
  static identifyTopStocks(sectorStocks: MarketData[], count: number = 5): Array<{
    symbol: string;
    name: string;
    performance: number;
    volume: number;
    newsImpact: string;
    signal: 'opportunity' | 'caution' | 'neutral';
  }> {
    // Sort by performance and volume
    const sortedStocks = sectorStocks
      .sort((a, b) => {
        // Primary sort by performance
        if (Math.abs(b.changePercent) !== Math.abs(a.changePercent)) {
          return Math.abs(b.changePercent) - Math.abs(a.changePercent);
        }
        // Secondary sort by volume
        return b.volume - a.volume;
      })
      .slice(0, count);
    
    return sortedStocks.map(stock => ({
      symbol: stock.symbol,
      name: stock.name,
      performance: stock.changePercent,
      volume: stock.volume,
      newsImpact: this.generateNewsImpact(stock),
      signal: this.determineStockSignal(stock)
    }));
  }
  
  // Generate News Impact Summary
  static generateNewsImpact(stock: MarketData): string {
    const absChange = Math.abs(stock.changePercent);
    const volumeChange = stock.volumeChange || 0;
    
    if (absChange > 5 && volumeChange > 50) {
      return stock.changePercent > 0 
        ? "Strong positive momentum with high volume - likely positive news catalyst"
        : "Sharp decline with high volume - possible negative news or profit booking";
    } else if (absChange > 3) {
      return stock.changePercent > 0
        ? "Moderate gains - sector rotation or stock-specific positive development"
        : "Moderate decline - sector weakness or stock-specific concerns";
    } else if (volumeChange > 100) {
      return "High volume activity with minimal price change - institutional activity or news pending";
    } else {
      return "Normal trading activity - following sector trend";
    }
  }
  
  // Determine Stock Signal
  static determineStockSignal(stock: MarketData): 'opportunity' | 'caution' | 'neutral' {
    const change = stock.changePercent;
    const volumeChange = stock.volumeChange || 0;
    
    // Opportunity signals
    if (change < -3 && volumeChange < 50) return 'opportunity'; // Oversold without panic selling
    if (change > 3 && volumeChange > 50) return 'opportunity'; // Strong breakout with volume
    
    // Caution signals
    if (change > 5 && volumeChange < 20) return 'caution'; // Price spike without volume
    if (change < -5 && volumeChange > 100) return 'caution'; // Panic selling
    
    return 'neutral';
  }
  
  // Generate Sector Outlook
  static generateSectorOutlook(sectorName: string, performance: number, topStocks: any[]): string {
    const absPerformance = Math.abs(performance);
    const positiveStocks = topStocks.filter(s => s.performance > 0).length;
    const negativeStocks = topStocks.filter(s => s.performance < 0).length;
    
    let outlook = "";
    
    if (performance > 2) {
      outlook = `${sectorName} sector showing strong bullish momentum (+${performance.toFixed(2)}%). `;
      if (positiveStocks > negativeStocks) {
        outlook += "Broad-based rally across sector leaders suggests sustained institutional interest.";
      } else {
        outlook += "Mixed performance within sector - selective stock picking recommended.";
      }
    } else if (performance < -2) {
      outlook = `${sectorName} sector under pressure (${performance.toFixed(2)}%). `;
      if (negativeStocks > positiveStocks) {
        outlook += "Sector-wide weakness indicates fundamental concerns or profit booking.";
      } else {
        outlook += "Selective weakness - quality stocks may present buying opportunities.";
      }
    } else {
      outlook = `${sectorName} sector trading sideways (${performance.toFixed(2)}%). `;
      outlook += "Consolidation phase - await clear directional breakout for sector trend.";
    }
    
    // Add volume-based insights
    const highVolumeStocks = topStocks.filter(s => s.volume > 1000000).length;
    if (highVolumeStocks > topStocks.length / 2) {
      outlook += " High institutional activity observed.";
    }
    
    return outlook;
  }
  
  // Analyze Single Sector
  static analyzeSector(sectorKey: string, marketData: MarketData[]): SectorAnalysis {
    const sectorConfig = this.INDIAN_SECTORS[sectorKey as keyof typeof this.INDIAN_SECTORS];
    if (!sectorConfig) {
      throw new Error(`Unknown sector: ${sectorKey}`);
    }
    
    // Filter stocks for this sector
    const sectorStocks = marketData.filter(stock => 
      sectorConfig.stocks.includes(stock.symbol)
    );
    
    // Calculate sector performance
    const performance = this.calculateSectorPerformance(sectorStocks);
    
    // Identify top stocks
    const topStocks = this.identifyTopStocks(sectorStocks);
    
    // Generate news impact
    const newsImpact = this.generateSectorNewsImpact(sectorStocks, performance);
    
    // Generate outlook
    const outlook = this.generateSectorOutlook(sectorConfig.name, performance, topStocks);
    
    return {
      sector: sectorConfig.name,
      performance,
      topStocks,
      newsImpact,
      outlook
    };
  }
  
  // Generate Sector News Impact
  static generateSectorNewsImpact(sectorStocks: MarketData[], performance: number): string {
    const avgVolume = sectorStocks.reduce((sum, stock) => sum + stock.volume, 0) / sectorStocks.length;
    const highVolumeStocks = sectorStocks.filter(stock => stock.volume > avgVolume * 1.5).length;
    
    if (Math.abs(performance) > 3 && highVolumeStocks > sectorStocks.length / 2) {
      return performance > 0
        ? "Sector rally driven by positive industry developments, earnings beats, or policy announcements"
        : "Sector decline due to regulatory concerns, earnings misses, or macroeconomic headwinds";
    } else if (Math.abs(performance) > 1.5) {
      return performance > 0
        ? "Moderate sector strength from stock-specific developments and rotation flows"
        : "Sector weakness from profit booking and risk-off sentiment";
    } else {
      return "Sector trading in line with broader market - no significant sector-specific catalysts";
    }
  }
  
  // Analyze All Sectors
  static analyzeAllSectors(marketData: MarketData[]): SectorAnalysis[] {
    const sectorAnalyses: SectorAnalysis[] = [];
    
    for (const sectorKey of Object.keys(this.INDIAN_SECTORS)) {
      try {
        const analysis = this.analyzeSector(sectorKey, marketData);
        sectorAnalyses.push(analysis);
      } catch (error) {
        console.error(`Error analyzing sector ${sectorKey}:`, error);
      }
    }
    
    // Sort by absolute performance
    return sectorAnalyses.sort((a, b) => Math.abs(b.performance) - Math.abs(a.performance));
  }
  
  // Get Sector Heatmap Data
  static getSectorHeatmap(sectorAnalyses: SectorAnalysis[]): Array<{
    sector: string;
    performance: number;
    color: string;
  }> {
    return sectorAnalyses.map(analysis => ({
      sector: analysis.sector,
      performance: analysis.performance,
      color: this.getHeatmapColor(analysis.performance)
    }));
  }
  
  // Get Heatmap Color Based on Performance
  static getHeatmapColor(performance: number): string {
    if (performance > 3) return '#22c55e'; // Strong green
    if (performance > 1) return '#84cc16'; // Light green
    if (performance > 0) return '#eab308'; // Yellow-green
    if (performance > -1) return '#f97316'; // Orange
    if (performance > -3) return '#ef4444'; // Red
    return '#dc2626'; // Dark red
  }
}
