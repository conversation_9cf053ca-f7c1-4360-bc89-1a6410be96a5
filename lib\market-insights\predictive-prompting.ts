// Gemini-Powered Predictive Prompting System

import { InsightQuestion, MarketData, SectorAnalysis } from './types';

export class PredictivePromptingEngine {
  
  // Question Templates by Category
  static readonly QUESTION_TEMPLATES = {
    market: [
      "Why is the {market} showing {direction} momentum today?",
      "What's driving the {timeframe} trend in {market}?",
      "How should investors position for {market} volatility?",
      "What are the key support/resistance levels for {index}?",
      "Is the current {market} rally sustainable?"
    ],
    sector: [
      "Why is the {sector} sector {performance} this week?",
      "What's the outlook for {sector} after recent {event}?",
      "Which {sector} stocks look most attractive now?",
      "How will {news} impact the {sector} sector?",
      "Is it time to rotate into/out of {sector}?"
    ],
    stock: [
      "Why did {stock} {movement} {percentage} today?",
      "What's the technical outlook for {stock}?",
      "Is {stock} oversold/overbought at current levels?",
      "How will {earnings/news} affect {stock} price?",
      "What's the risk-reward for {stock} at {price}?"
    ],
    crypto: [
      "Why is {crypto} {direction} while others consolidate?",
      "What's driving the correlation between {crypto1} and {crypto2}?",
      "Is this a good entry point for {crypto}?",
      "How will {regulation/news} impact {crypto} prices?",
      "Which altcoins look oversold on RSI today?"
    ]
  };
  
  // Generate Dynamic Questions
  static generateInsightQuestions(
    marketData: MarketData[],
    sectorAnalysis: SectorAnalysis[],
    newsImpacts: any[],
    geminiApiKey?: string
  ): InsightQuestion[] {
    const questions: InsightQuestion[] = [];
    
    // Generate market questions
    questions.push(...this.generateMarketQuestions(marketData));
    
    // Generate sector questions
    questions.push(...this.generateSectorQuestions(sectorAnalysis));
    
    // Generate stock questions
    questions.push(...this.generateStockQuestions(marketData));
    
    // Generate crypto questions
    questions.push(...this.generateCryptoQuestions(marketData));
    
    // Generate news-driven questions
    questions.push(...this.generateNewsQuestions(newsImpacts));
    
    // Sort by relevance and return top questions
    return questions
      .sort((a, b) => this.calculateRelevanceScore(b) - this.calculateRelevanceScore(a))
      .slice(0, 12);
  }
  
  // Generate Market Questions
  static generateMarketQuestions(marketData: MarketData[]): InsightQuestion[] {
    const questions: InsightQuestion[] = [];
    
    // Find significant market movements
    const significantMoves = marketData.filter(asset => Math.abs(asset.changePercent) > 2);
    
    if (significantMoves.length > 0) {
      const avgMove = significantMoves.reduce((sum, asset) => sum + asset.changePercent, 0) / significantMoves.length;
      const direction = avgMove > 0 ? "bullish" : "bearish";
      
      questions.push({
        id: this.generateId(),
        question: `Why is the Indian market showing ${direction} momentum today?`,
        category: 'market',
        complexity: 'intermediate',
        relatedAssets: significantMoves.slice(0, 5).map(asset => asset.symbol)
      });
    }
    
    // Volatility questions
    const highVolatilityAssets = marketData.filter(asset => Math.abs(asset.changePercent) > 5);
    if (highVolatilityAssets.length > 3) {
      questions.push({
        id: this.generateId(),
        question: "What's causing the increased volatility across Indian markets?",
        category: 'market',
        complexity: 'advanced',
        relatedAssets: highVolatilityAssets.map(asset => asset.symbol)
      });
    }
    
    return questions;
  }
  
  // Generate Sector Questions
  static generateSectorQuestions(sectorAnalysis: SectorAnalysis[]): InsightQuestion[] {
    const questions: InsightQuestion[] = [];
    
    // Best and worst performing sectors
    const sortedSectors = [...sectorAnalysis].sort((a, b) => b.performance - a.performance);
    
    if (sortedSectors.length > 0) {
      const bestSector = sortedSectors[0];
      const worstSector = sortedSectors[sortedSectors.length - 1];
      
      // Best sector question
      if (bestSector.performance > 2) {
        questions.push({
          id: this.generateId(),
          question: `Why is the ${bestSector.sector} sector outperforming today?`,
          category: 'sector',
          complexity: 'intermediate',
          relatedAssets: bestSector.topStocks.map(stock => stock.symbol)
        });
      }
      
      // Worst sector question
      if (worstSector.performance < -2) {
        questions.push({
          id: this.generateId(),
          question: `What's behind the weakness in ${worstSector.sector} sector?`,
          category: 'sector',
          complexity: 'intermediate',
          relatedAssets: worstSector.topStocks.map(stock => stock.symbol)
        });
      }
    }
    
    // Sector rotation questions
    const bullishSectors = sectorAnalysis.filter(s => s.performance > 1).length;
    const bearishSectors = sectorAnalysis.filter(s => s.performance < -1).length;
    
    if (bullishSectors > 0 && bearishSectors > 0) {
      questions.push({
        id: this.generateId(),
        question: "Is this the right time for sector rotation in Indian markets?",
        category: 'sector',
        complexity: 'advanced',
        relatedAssets: []
      });
    }
    
    return questions;
  }
  
  // Generate Stock Questions
  static generateStockQuestions(marketData: MarketData[]): InsightQuestion[] {
    const questions: InsightQuestion[] = [];
    
    // Top movers
    const sortedStocks = [...marketData].sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent));
    const topMovers = sortedStocks.slice(0, 5);
    
    for (const stock of topMovers) {
      if (Math.abs(stock.changePercent) > 3) {
        const direction = stock.changePercent > 0 ? "surge" : "decline";
        const percentage = Math.abs(stock.changePercent).toFixed(1);
        
        questions.push({
          id: this.generateId(),
          question: `Why did ${stock.name} ${direction} ${percentage}% today?`,
          category: 'stock',
          complexity: 'beginner',
          relatedAssets: [stock.symbol]
        });
      }
    }
    
    // Oversold/Overbought questions
    const oversoldStocks = marketData.filter(stock => stock.changePercent < -5);
    const overboughtStocks = marketData.filter(stock => stock.changePercent > 5);
    
    if (oversoldStocks.length > 0) {
      questions.push({
        id: this.generateId(),
        question: "Which oversold stocks present buying opportunities today?",
        category: 'stock',
        complexity: 'intermediate',
        relatedAssets: oversoldStocks.slice(0, 5).map(stock => stock.symbol)
      });
    }
    
    if (overboughtStocks.length > 0) {
      questions.push({
        id: this.generateId(),
        question: "Are the recent gainers due for a pullback?",
        category: 'stock',
        complexity: 'intermediate',
        relatedAssets: overboughtStocks.slice(0, 5).map(stock => stock.symbol)
      });
    }
    
    return questions;
  }
  
  // Generate Crypto Questions
  static generateCryptoQuestions(marketData: MarketData[]): InsightQuestion[] {
    const questions: InsightQuestion[] = [];
    
    // Filter crypto assets (assuming they have specific naming or sector)
    const cryptoAssets = marketData.filter(asset => 
      ['BTC', 'ETH', 'SOL', 'ADA', 'MATIC'].includes(asset.symbol) ||
      asset.sector === 'Cryptocurrency'
    );
    
    if (cryptoAssets.length === 0) return questions;
    
    // Crypto market direction
    const avgCryptoMove = cryptoAssets.reduce((sum, asset) => sum + asset.changePercent, 0) / cryptoAssets.length;
    
    if (Math.abs(avgCryptoMove) > 3) {
      const direction = avgCryptoMove > 0 ? "rallying" : "declining";
      questions.push({
        id: this.generateId(),
        question: `What's driving the crypto market ${direction} today?`,
        category: 'crypto',
        complexity: 'intermediate',
        relatedAssets: cryptoAssets.map(asset => asset.symbol)
      });
    }
    
    // Individual crypto questions
    const btc = cryptoAssets.find(asset => asset.symbol === 'BTC');
    const eth = cryptoAssets.find(asset => asset.symbol === 'ETH');
    
    if (btc && Math.abs(btc.changePercent) > 3) {
      questions.push({
        id: this.generateId(),
        question: `Is Bitcoin's ${btc.changePercent > 0 ? 'rally' : 'decline'} sustainable?`,
        category: 'crypto',
        complexity: 'beginner',
        relatedAssets: ['BTC']
      });
    }
    
    if (eth && Math.abs(eth.changePercent) > 3) {
      questions.push({
        id: this.generateId(),
        question: `What's behind Ethereum's ${eth.changePercent > 0 ? 'strength' : 'weakness'} today?`,
        category: 'crypto',
        complexity: 'beginner',
        relatedAssets: ['ETH']
      });
    }
    
    return questions;
  }
  
  // Generate News-Driven Questions
  static generateNewsQuestions(newsImpacts: any[]): InsightQuestion[] {
    const questions: InsightQuestion[] = [];
    
    // High impact news
    const highImpactNews = newsImpacts.filter(news => news.impactScore > 70);
    
    for (const news of highImpactNews.slice(0, 3)) {
      if (news.affectedAssets.length > 0) {
        questions.push({
          id: this.generateId(),
          question: `How will "${news.headline.substring(0, 50)}..." impact ${news.affectedAssets[0]}?`,
          category: news.affectedAssets.length > 3 ? 'market' : 'stock',
          complexity: 'intermediate',
          relatedAssets: news.affectedAssets.slice(0, 3)
        });
      }
    }
    
    return questions;
  }
  
  // Answer Question with Gemini
  static async answerQuestion(
    question: InsightQuestion,
    marketData: MarketData[],
    sectorAnalysis: SectorAnalysis[],
    newsImpacts: any[],
    geminiApiKey: string
  ): Promise<string> {
    try {
      // Gather relevant context
      const context = this.gatherQuestionContext(question, marketData, sectorAnalysis, newsImpacts);
      
      const prompt = `As a financial market expert, answer this question with specific, actionable insights:

Question: ${question.question}

Market Context:
${context}

Provide a comprehensive answer covering:
1. Current situation analysis
2. Key factors driving the movement/trend
3. Technical and fundamental considerations
4. Risk factors and opportunities
5. Actionable insights for investors

Keep the response educational, specific, and under 300 words.`;

      const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-goog-api-key': geminiApiKey
        },
        body: JSON.stringify({
          contents: [{
            parts: [{ text: prompt }]
          }]
        })
      });

      const data = await response.json();
      
      if (data.candidates && data.candidates[0]?.content?.parts?.[0]?.text) {
        return data.candidates[0].content.parts[0].text;
      }
      
      return this.generateBasicAnswer(question, marketData);
    } catch (error) {
      console.error('Gemini API error:', error);
      return this.generateBasicAnswer(question, marketData);
    }
  }
  
  // Gather Question Context
  static gatherQuestionContext(
    question: InsightQuestion,
    marketData: MarketData[],
    sectorAnalysis: SectorAnalysis[],
    newsImpacts: any[]
  ): string {
    let context = "";
    
    // Add related asset data
    if (question.relatedAssets.length > 0) {
      const relatedData = marketData.filter(asset => question.relatedAssets.includes(asset.symbol));
      context += "Related Assets:\n";
      for (const asset of relatedData) {
        context += `- ${asset.name} (${asset.symbol}): ${asset.changePercent > 0 ? '+' : ''}${asset.changePercent.toFixed(2)}%, Price: ${asset.price}\n`;
      }
      context += "\n";
    }
    
    // Add sector context for sector questions
    if (question.category === 'sector') {
      context += "Sector Performance:\n";
      for (const sector of sectorAnalysis.slice(0, 5)) {
        context += `- ${sector.sector}: ${sector.performance > 0 ? '+' : ''}${sector.performance.toFixed(2)}%\n`;
      }
      context += "\n";
    }
    
    // Add relevant news
    const relevantNews = newsImpacts.filter(news => 
      news.affectedAssets.some((asset: string) => question.relatedAssets.includes(asset))
    );
    
    if (relevantNews.length > 0) {
      context += "Recent News:\n";
      for (const news of relevantNews.slice(0, 3)) {
        context += `- ${news.headline}\n`;
      }
    }
    
    return context;
  }
  
  // Generate Basic Answer (Fallback)
  static generateBasicAnswer(question: InsightQuestion, marketData: MarketData[]): string {
    return `This is a complex market question that requires analysis of multiple factors including technical indicators, fundamental data, and market sentiment. For the most accurate and up-to-date analysis, please consult with a financial advisor or conduct detailed research using multiple data sources.`;
  }
  
  // Calculate Relevance Score
  static calculateRelevanceScore(question: InsightQuestion): number {
    let score = 0;
    
    // Category weights
    const categoryWeights = { market: 10, sector: 8, stock: 6, crypto: 7 };
    score += categoryWeights[question.category] || 5;
    
    // Complexity weights (simpler questions are more accessible)
    const complexityWeights = { beginner: 10, intermediate: 8, advanced: 6 };
    score += complexityWeights[question.complexity] || 5;
    
    // Asset relevance
    score += Math.min(question.relatedAssets.length * 2, 10);
    
    return score;
  }
  
  // Generate Unique ID
  static generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }
}
